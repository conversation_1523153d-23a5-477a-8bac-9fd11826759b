# README - 中国象棋Muzero强化学习UI程序

## 简介 (Introduction)

### 1. 项目目标与核心需求

本项目旨在开发一款基于强化学习算法Muzero的中国象棋用户界面(UI)程序。
核心需求包括：

- 提供沉浸式的用户体验，例如无边框设计、可自动隐藏/显示的工具条。
- 在Windows平台下，支持任意像素级别的界面放大缩小。
- 能够加载和使用基于UCCI (Universal Chess Interface)协议的第三方中国象棋引擎。
- (远期目标) 集成自研的基于Muzero算法的中国象棋引擎。
- (远期目标) 实现自定义连线方案，连接到各种象棋游戏平台进行自动对弈。

最初的设想是：一款基于强化学习算法Muzero的中国象棋ui程序。因基于跨平台的flutter/dart（界面）+rust（算法后端）开发，所以理论上可用于任意平台，但由于测试平台在win10，且多数ucci引擎只有windows版本，所以目前适配最好的平台只有windows（目前代码已改成只能在windows下运行）。 写这个是因为目前网上看到的所有中国象棋软件（包括引擎本身或者引擎加载软件）都是缺胳膊少腿的，用起来束手束脚，所以打算自己写一个。也正好看看flutter+rust能擦出什么样的火花。

### 软件截图（目前仍是半成品）

![image](https://github.com/dbsxdbsx/ChineseChess_Muzero_App/blob/main/img/%E5%8D%8A%E6%88%90%E5%93%81%E6%88%AA%E5%9B%BE.PNG)

### 2. 项目背景与愿景

当前市面上多数中国象棋软件在功能和用户体验上存在不足（如界面缩放不灵活、功能受限等）。本项目发起的原因是希望打造一款功能全面、体验优良的中国象棋软件，满足个人使用需求，并探索Flutter与Rust结合在桌面应用开发中的潜力。
预期用户能够通过本软件获得流畅、可高度自定义的象棋对弈、分析和训练体验。

### 3. 技术架构与关键决策

- **核心架构**: 采用跨平台UI框架Flutter (Dart语言)负责前端界面展示与用户交互，高性能系统编程语言Rust负责后端核心逻辑与算法实现（如棋局逻辑、AI引擎等）。
- **前后端通信**: 通过 `flutter_rust_bridge` (frb)库实现Dart与Rust之间的自动代码生成和高效通信。
- **平台适配**: 初期主要针对Windows平台进行开发和优化，因为多数UCCI引擎仅提供Windows版本。代码目前已调整为主要在Windows环境下运行。

### 4. 技术栈、开发环境与依赖

#### 技术栈

- **前端**: Flutter, Dart
- **后端**: Rust
- **桥接**: `flutter_rust_bridge` (frb)
- **象棋引擎协议**: UCCI (Universal Chess Interface)
- **AI算法 (计划中)**: Muzero

#### 开发环境

- **操作系统**: Windows 10
- **IDE**: Visual Studio Code (VSCode)
- **终端**: Git Bash (在Windows环境下进行编译等操作时推荐使用)
- **版本控制**: Git

#### 主要依赖与工具

- Flutter SDK
- Rust SDK (rustup)
- CMake (用于构建windows下应用)
- LLVM (编译工具链)
- `flutter_rust_bridge_codegen` (frb代码生成器)
- `just` (任务运行器，类似make)
- `ffigen` (Dart FFI生成器)
- `build_runner` (Dart代码生成工具)
- `freezed` / `freezed_annotation` (Dart类生成工具)

#### 约束与已知限制

- **平台**: 目前主要在Windows平台测试和适配。
- **文件路径**: 项目路径和文件名不支持中文字符，否则可能导致编译错误。

### 5. 主要设计模式与组件关系

- 可能会采用MVC、MVVM或GetX（目前主要就是GetX）等状态管理模式进行Flutter侧的UI与逻辑分离。
- Rust侧的象棋逻辑会封装成独立的模块，通过frb暴露API给Dart侧调用。

## 功能 (Feature)

### 已完成功能

- [X] **沉浸式用户体验**: 无边框窗口设计。实现了类似QQ的边缘停靠和自动隐藏/显示的工具条（浮动工具栏中按钮的激活状态有更清晰的视觉反馈）。
- [X] **界面缩放**: 在Windows平台下，支持任意级别的像素放大缩小棋盘和UI元素。
- [X] **UCCI引擎集成**: 成功加载和使用基于UCCI (Universal Chess Interface)协议的第三方中国象棋引擎。

  - [X] **摆谱模式**:

  - 实现手动摆谱功能。
  - UI中添加棋谱显示，并支持点击棋谱回溯到对应局面。
  - 实现棋谱自动播放功能（包括前进、后退、到开头、到结尾）。
  - **棋谱悬停显示ICCS**：当鼠标悬停在棋谱的中文表示上时，会通过Tooltip显示其ICCS表示。
  - **棋谱导航按钮**：在棋谱面板的标题栏添加了“到开头”、“后退一步”、“前进一步”、“到结尾”的控制按钮（已完善按钮在棋谱开头/结尾时的禁用逻辑）。
  - **棋谱回溯行为改进**：通过导航按钮或双击棋谱回溯时，后续棋谱不再被删除，而是以半透明状态显示，表示为“未执行”或“已撤销”的步骤。棋盘上也会显示当前回溯到的棋步的起止位置标记。棋谱视图会自动滚动以使当前激活的棋步可见且位置适中。
  - **历史局面新走法确认**：当棋局回溯到历史某一步时，若用户或引擎尝试走出新的棋步，会弹出对话框提示用户，确认后将删除该历史点之后的所有棋谱记录。
  - **无效放置后保持悬停**：在摆谱模式下，当候选棋子放置到无效位置后，候选棋子将恢复悬停状态，而不是消失。
  - **退出摆谱模式校验增强**：在退出摆谱模式时，对棋盘上所有棋子位置的合规性进行全面校验，若校验失败则提示用户并阻止退出。
  - **FEN粘贴后逻辑修复**：修复了在摆谱模式下通过FEN粘贴棋盘后，错误地触发游戏结束判断的问题。
  - **将/帅移除逻辑优化**：优化了摆谱模式下移除将/帅的逻辑（允许多个将/帅时移除一个，最后一个禁止非王棋子覆盖）。

  - [X] **“交换红黑”后翻转按钮状态同步**: 修复了在摆谱模式下执行“交换红黑”操作后，浮动工具栏中的翻转按钮状态未能与棋盘实际翻转状态 (`isFlipped`) 正确同步的问题。
  - [X] **“交换红黑”行为调整**: 调整了摆谱模式下“交换红黑”功能，现在仅交换棋子颜色，不改变棋盘当前的视觉翻转状态，使得红黑双方的棋子在视觉上真正交换位置。
  - [X] **Rust端棋盘翻转与坐标系统重构 (2025-05-21)**:
    - Rust `GameManager` 引入 `is_flipped: bool` 状态，表示用户棋盘视角是否翻转。
    - `GameManager.board` 内部始终以标准“黑上红下”存储。
    - FEN加载时能自动检测并设置 `is_flipped`。
    - 新增 `flip_board()` (切换翻转状态并返回新视角棋盘) 和 `get_display_board()` (返回当前视角棋盘) API。
    - 接收行列坐标的公共API (如 `update_board`, `is_piece_valid_move` 等) 修改为接收“视觉坐标”，并在内部根据 `is_flipped` 转换为“逻辑坐标”进行操作。
  - [X] **Dart端适配Rust坐标系统重构 (2025-05-21)**:
    - `HomeController` 中的 `isFlipped` 状态与Rust `GameManager.isFlipped` 同步。
    - 调用Rust相关API时，正确传递视觉坐标或将逻辑坐标转换为视觉坐标。
    - 棋盘渲染、ICCS生成、FEN处理等逻辑已适配新的坐标体系。
  - [X] **浮动工具栏按钮集成测试 (2025-05-22)**: 添加了针对浮动工具栏中“新对局”和“设置”按钮功能的集成测试 (`integration_test/float_bar_btns_test.dart`)。
  - [X] **浮动工具栏(FloatBoxPanel)重构与问题修复 (2025-05-22)**:
    - 将 `FloatBoxPanel`重构为使用专门的 `FloatBoxController` (GetXController)进行状态管理。
    - 修复了因直接在布尔类型Getter返回的 `bool`值上错误调用 `.value`而导致的 `NoSuchMethodError`问题（例如在 `HomeController`的 `isFlipped`和 `isArrangeMode`属性的访问上）。
  - [X] **修复数字相关问题 (2025-05-22)**: 解决了在特定场景下数字显示或计算不准确的问题。

### 待开发功能 (TODO List)

- **核心功能**
  - [ ] **AI与引擎**:
    - [ ] (远期) 开发并集成一款内置的基于Muzero算法的中国象棋引擎。
    - [ ] (远期) 实现第三方UCCI引擎与内置引擎之间的打擂比赛功能。
    - [ ] (远期) 实现第三方UCCI引擎辅助内置引擎进行训练的功能。
    - [ ] (远期) 实现自定义连线方案，通过图像识别等技术连接到各种象棋游戏平台进行自动对弈（可能需要OpenAI等AI服务支持）。
- **UI与用户体验**
  - [ ] 开发一个可拖动的TabView组件。
  - [ ] 实现应用配置的读取与保存（例如窗口位置、引擎设置等）。
  - [ ] (研究) 使用Dijkstra算法计算棋子到对方老将的最短路径，可用于局面评估或辅助提示。

## 已知问题 (Bug/Issue)

- `_buildPieceWidget`和``会影响到 `flipBoard()`, `exchangeAllPiecesOnBoard()`修改，`get_display_board` 还需要？
- `_arrangeModePlayerToMove` 是否还需要？直接用gm.current_player?
- 引擎下法竟然会触发KingTOKing
- `get_special_pawn_number` 应该说明其参数 `col` 和 `row` 是从哪里开始计数，是从0还是1，方向如何（例如，从下到上，左到右）？
- `add test for iccs_move_to_chinese_move and its anti direction chinese_move_to_iccs_move`：需要为ICCS棋谱与中文棋谱互转函数添加单元测试。
- `corner case for each pince`：需要测试各棋子的边界情况，如塞象眼（或在相的落点放置子）、兵过河后的多种移动方式、别马脚、将军和应将规则等。
- **Rust端坐标API返回**: Rust端部分API（如错误提示中）返回的坐标可能仍是逻辑坐标，Dart端在显示时可能需要根据 `gameManager.isFlipped` 自行转换。未来可考虑在Rust端统一返回视觉坐标。
- `remove test case test_iccs_to_chinese_notation_for_red_initial_moves and test_iccs_to_chinese_notation_for_black_initial_moves`：确认是否需要移除这两个针对红黑双方初始棋局的ICCS转中文棋谱的测试用例。
- `是否该通过修改PieceMove来设置设置ctrl.dart文件中的moveIndex的移动标记？`：这是一个设计决策问题，关于如何通过 `PieceMove`更新UI中的 `moveIndex`。
- `全部用256array， 去掉任何90array的部分---也许全部放在·gameManager·类中？·Board90Pieces·`：考虑统一棋盘表示法为256格数组。
- `英文棋谱记录法转义到中文象棋棋谱记录法（结合摆谱功能，检查极端的的情况，比如[5个兵一起过河的情况](https://www.xqbase.com/protocol/cchess_move.htm)）`：需要测试此转换功能，特别是极端情况。
- `测试将军照面等被将、困毙类rust函数`：需要测试Rust后端实现的将军、照面、困毙等核心规则。
- `棋盘反转功能`：待实现。
- `电脑下棋时计时器几乎没动过，不知是设置问题还是真的电脑太快了`：需要调查计时器问题。
- `addLog dart`：需要在Dart侧添加日志功能。
- `打造可拖动的tabView组件`：UI组件开发。
- `onLoadEngine以mixin的方式放到controller中，不要在EngineLoadButton中`：代码重构建议。
- `switch (player) 等的Player类可否去掉Unknown选项？`：代码可维护性与类型安全问题。
- `process反馈：“ stream did not contain valid UTF-8？”`：UCCI引擎通信编码问题。
- `若engine异常退出，程序不能关掉`：程序健壮性问题。
- `是否应该用thread 代替 Tokio来降低内存占用？`：性能优化考虑。
- `展开时并放大缩小时，1.距离边界的width会被重置到最边上，且border不固定。须修复；`：UI缩放与布局问题。
- `拓展时随便点击哪里都可以拖动（还有个yOffset问题）、 缩小dock时鼠标聚焦后可挪出一点点、`：UI拖动与停靠问题。
- `浮动工具栏须在窗口放大缩小时 等比率调整位置和大小、透明acrylic`：UI组件动态调整与样式问题。
- `重启读取配置`：应用状态持久化问题。
- `用[Dijkstra算法](https://zhuanlan.zhihu.com/p/3475462229)来计算棋子到对方老将的最短路径`：功能扩展。
- **UI相关问题**:

  - 可否内置Divider控件？
  - 可否内置垂直排列的按钮组？
  - 选中对话框的行为。
  - 下拉菜单有时可能为空。
- **棋谱转换与校验相关**：

  - [ ] 实现ICCS棋谱与中文棋谱之间的相互转换，特别是处理极端情况（如五个兵过河）。
  - [ ] 完善 `get_special_pawn_number`函数，明确其坐标系定义（行列起始点，计数方向）。
  - [ ] 为 `iccs_move_to_chinese_move`及其反向函数 `chinese_move_to_iccs_move`添加单元测试。
- **棋局逻辑与规则相关**:

  - [ ] 全面测试Rust后端实现的各种棋规，如将军、照面、困毙、长打、长捉等。
  - [ ] 棋盘表示统一：考虑将所有棋盘表示统一为256格数组，移除90格数组的相关实现（可能集中在 `GameManager`类或 `Board90Pieces`）。
- **UI与用户体验相关Bug或待细化项**:

  - [ ] 修复电脑行棋时计时器几乎不动的问题（需确认是设置问题还是引擎速度过快）。
  - [ ] 在Dart侧实现日志记录功能 (`addLog`)。
  - [ ] 将 `onLoadEngine`逻辑以Mixin的方式移入相应的Controller，而不是放在 `EngineLoadButton`中（代码重构）。
  - [ ] 审查 `Player`枚举类，考虑是否可以移除 `Unknown`选项以增强类型安全（代码重构）。
  - [ ] UI缩放与拖动问题：
    - [ ] 修复窗口展开和缩放时，元素距离边界重置及边框不固定的问题。
    - [ ] 修复拓展模式下点击任意位置拖动窗口的问题（包括yOffset问题）。
    - [ ] 修复缩小到dock状态时，鼠标移出后窗口可能微移的问题。
  - [ ] 浮动工具栏：
    - [ ] 在窗口放大缩小时，工具栏应按等比率调整位置和大小。
    - [ ] 实现工具栏的透明/亚克力(acrylic)效果。
- **底层与性能相关**:

  - [ ] 处理UCCI引擎进程通信时可能出现的 `stream did not contain valid UTF-8`错误。
  - [ ] 增强程序健壮性：当UCCI引擎异常退出时，主程序不应崩溃。
  - [ ] 性能优化：研究是否可以用原生线程(thread)替代Tokio运行时，以期降低内存占用。
- **测试相关**:

  - [ ] 针对各个棋子类型，补充和完善单元测试，特别是各种边界情况和特殊规则的测试（如塞象眼、兵过河后的多种走法、蹩马腿、将军/解将等）。
  - [ ] 移除 `README.md`中提及的 `test_iccs_to_chinese_notation_for_red_initial_moves`和 `test_iccs_to_chinese_notation_for_black_initial_moves`测试用例（如果已确认不再需要或已覆盖）。
  - [ ] **集成测试状态问题 (2025-05-22)**: `integration_test/float_bar_btns_test.dart`中的各测试用例单独运行时可以通过，但若整体连续运行，从第二个测试开始会失败，疑似由于GetX状态在测试间未能完全隔离或重置导致。
    - 注意：近期对 `FloatBoxPanel`进行的GetX重构（修复了 `NoSuchMethodError`）并未解决此集成测试的连续运行失败问题。

## 笔记 (Note)

### 1. 中国象棋核心知识

关于中国象棋的棋盘、棋子、走法、坐标系统、棋谱记录法、FEN表示法以及详细规则等，请参考：[中国象棋核心知识](./.doc/xiangqi_note.md)

### 2. 自制基于明确逻辑的引擎思路

请参考：[自制基于明确逻辑的引擎思路](./.doc/engine_logic_ideas.md)

### 3. 编译说明

本程序仅在windows测试，使用了[frb](https://github.com/fzyzcjy/flutter_rust_bridge)作为核心框架来绑定flutter和rust。

1. 安装[Flutter SDK](https://docs.flutter.dev/get-started/install)
2. 安装[Rust SDK](https://rustup.rs/)
3. 安装cmake和flutter+rust桌面app所需要的库[corrison](http://cjycode.com/flutter_rust_bridge/template/setup_desktop.html)，然后进入 `windows/rust.cmake`，将corrison的获取方式改为 `find_package(Corrosion REQUIRED)`并注释掉下方通过github获取的方式。
4. 安装llvm和安装编译所需其他工具链：
   ```shell
   winget install -e --id LLVM.LLVM
   cargo install flutter_rust_bridge_codegen just
   dart pub global activate ffigen
   ```
5. 更新必要的flutter包：
   ```dart
   flutter pub add -d build_runner
   flutter pub add -d freezed
   flutter pub add freezed_annotation
   ```

   [关于freeze的介绍](https://github.com/rrousselGit/freezed)
6. `justfile`中 `gen`流程最后添加这句话:
   ```shell
   gen:
       # ... (其他命令)
       flutter pub run build_runner build --delete-conflicting-outputs
   ```

   并运行 `just`生成rust绑定代码（只有rust代码有变动才需要）
7. `flutter run` （将生成flutter侧的绑定代码，并最终生成app）

([官方示例教程](http://cjycode.com/flutter_rust_bridge/template/generate.html)、[flutter_rust_bridge官方推荐模板](https://github.com/Desdaemon/flutter_rust_bridge_template)）

### 3. 其他平台的安装设置

虽然本项目目标仅在windows下使用，但若对于其他平台感兴趣的，可参考：

- [安卓平台](https://cjycode.com/flutter_rust_bridge/template/setup_android.html)
- [web平台](https://cjycode.com/flutter_rust_bridge/template/setup_web.html)

### 4. 常见问题

- **运行 `just clean`出现错误**

  ```
  flutter clean
  error: Recipe `clean` could not be run because just could not find the shell: program not found
  ```

  在windows平台编译时，请使用git bash，不要用power shell。
- **如何清除并重新生成？**

  ```bash
  rm -rf build && just clean && flutter pub get && just && flutter run
  ```

  若仅更改了dart代码，则直接 `flutter run`
  若还更改了rust代码，则 `just && flutter run`
- **是否支持中文文件夹？**
  经测试，不支持。否则编译app时会发生各种奇怪的错误（文件名中最好也不要有“中划线”）。
- **多rust模块生成？**
  参考: [https://github.com/fzyzcjy/flutter_rust_bridge/pull/481](https://github.com/fzyzcjy/flutter_rust_bridge/pull/481)
- **如何隐藏运行时加载的ucci引擎窗口？**
  将 `windows/runner/main.cpp`中的

  ```c++
   if (!::AttachConsole(ATTACH_PARENT_PROCESS) && ::IsDebuggerPresent()) {
      CreateAndAttachConsole();
    }
  ```

  替换为

  ```c++
  if (!::AttachConsole(ATTACH_PARENT_PROCESS) && ::IsDebuggerPresent()) {
    CreateAndAttachConsole();
  } else {
    AllocConsole();
    ShowWindow(GetConsoleWindow(), SW_HIDE);
  }
  ```

  参考：[StackOverflow Question](https://stackoverflow.com/questions/67082272/dart-how-to-hide-cmd-when-using-process-run)

### 5. 测试说明

本项目使用Flutter的 `integration_test`框架进行集成测试。

- **测试文件位置**: `integration_test/float_bar_btns_test.dart`
- **运行所有测试**:
  ```bash
  flutter test integration_test/float_bar_btns_test.dart -d windows
  ```
- **运行单个测试用例**:
  可以通过 `--name`参数指定要运行的测试用例的名称。例如，要运行名为“点击新对局按钮后，棋盘应初始化到标准开局布局FEN”的测试：
  ```bash
  flutter test integration_test/float_bar_btns_test.dart --name "点击新对局按钮后，棋盘应初始化到标准开局布局FEN" -d windows
  ```

  **注意**: 请确保测试用例名称与测试文件中定义的完全一致。

### 6. 活跃决策与经验教训

- **编译环境**: 在Windows平台进行编译操作时，务必使用Git Bash，避免使用PowerShell，以防止 `just clean`等命令执行失败。
- **文件路径**: 项目的文件夹路径和文件名中不应包含中文字符，否则可能导致编译时出现未知错误。
- **隐藏引擎窗口**: 通过修改 `windows/runner/main.cpp`中的代码，可以隐藏运行时加载的UCCI引擎的命令行窗口。
- **代码生成**: 若仅更改Dart代码，直接运行 `flutter run`即可；若更改了Rust代码，则需要先运行 `just`命令生成绑定代码，然后再运行 `flutter run`。
- **代码维护**: 定期对代码库进行审查和重构，例如清理不必要的注释、统一代码风格、将英文注释翻译为中文等，以提高代码可读性和可维护性。
- **Rust代码注释优化 (2025-05-17)**: 对Rust侧代码进行了全面的注释审查和优化，包括将英文注释翻译为中文，移除废弃代码注释和不必要的调试打印语句，以提升代码库的规范性和可读性。
- **Dart代码规范 (2025-05-17)**: 修复了多个Dart文件中由于非 `final`实例字段导致的 `@immutable` lint警告，提升了代码的健壮性和规范性。[git: 3bff70]
- **Dart API更新 (2025-05-17)**: 修复了Dart代码中因 `withOpacity`方法被弃用而产生的警告，采用了新的推荐用法。[git: 7fbf14b]
- **代码注释规范化 (2025-05-17)**: 对Dart和Rust侧多个核心文件进行了注释的全面审查和优化，统一了注释风格，将部分英文注释和不清晰的中文注释更新为更规范和易于理解的中文描述，提升了代码库的可读性和可维护性。
- **Rust端棋盘翻转与坐标系统重构 (2025-05-21)**: Rust `GameManager` 引入 `is_flipped` 状态及相关API (`flip_board`, `get_display_board`)，统一内部棋盘表示为标准“黑上红下”，并将接收坐标的API改为接收“视觉坐标”。此举简化了Dart端的坐标处理逻辑，增强了棋盘翻转功能的鲁棒性。Dart端已完成对应适配。

## 参考 (Reference)

### 开源参考

- [象棋小巫师源码](https://github.com/xqbase/xqwlight/tree/master/Win32)

### 测试用游戏

- [象棋小巫师](https://www.xqbase.com/xqwlight/index.htm)
- [ucci中国象棋引擎列表](https://www.xqbase.com/league/enginelist.htm)

### 在线对弈

- [一个好玩的中国象棋网站](https://play.xiangqi.com/)
- [fen棋盘复制粘贴](https://play.xiangqi.com/editor)

### 中国象棋ui程序、ucci引擎

- [Chinese Chess game by Flutter](https://github.com/shirne/chinese_chess)
- [中国象棋开源UCCI引擎理](https://github.com/yytdfc/ChineseChess-engines)
- [表示当前局面的FEN文件格式](https://www.xqbase.com/protocol/cchess_fen.htm)
- [着法表示](https://www.xqbase.com/protocol/cchess_move.htm)
- [棋盘array内存表示](https://www.xqbase.com/computer/stepbystep2.htm)
- [象棋AI基本教学](https://www.xqbase.com/computer.htm)
- [中国象棋中的子力价值](https://www.xqbase.com/other/compare2.htm)
- [中国象棋协议/引擎](https://www.xqbase.com/protocol.htm)
- [ELO等级分计算公式详解](https://www.xqbase.com/protocol/elostat.htm)
- [棋规(长打，长吃...)、输赢规则](https://www.xqbase.com/other/compare4.htm)
- [WXF中国象棋规则论文](https://arxiv.org/abs/2412.17334v1)

### 棋谱

- [中国象棋开局编号](https://www.xqbase.com/ecco/ecco_contents.htm#ecco_a)

### 连线器技术

- [中国象棋实时分析棋局——棋子识别方法分析](https://zhuanlan.zhihu.com/p/581841976)
- [rust处理图像视频展示](https://www.bilibili.com/video/BV18A411t7Hg/?vd_source=3facc3cb195be0a27a0ea9a4eb3bb6fe)
- [**基于yolo的C#中象识别**](https://github.com/Vincentzyx/VinXiangQi)
- [基于CNN的中象棋子识别1](https://blog.csdn.net/qq_44725872/article/details/110286528)
- [基于CNN的中象棋子识别2](https://blog.csdn.net/Together_CZ/article/details/131084015)
- [QT 写的象棋界面程序，图形连线天天象棋，王者象棋（未尝试过）](https://github.com/leedavid/QTggchess)
- [国象棋子识别视频教程](https://www.youtube.com/watch?v=rYlEEvrgmc8)
- [中象简易连线器](https://github.com/liujh168/link)

### 强化学习

- [中国象棋AlphaZero](https://github.com/bupticybee/icyChessZero)
- [中国象棋gym环境](https://github.com/bupticybee/gym_chinese_chess)

### 定式证明

- [尝试用数学逻辑证明某些定式](https://mathoverflow.net/questions/229732/can-one-make-high-level-proofs-about-chess-positions)

## 遵循协议 (License)

本项目遵循MIT协议（简言之：不约束，不负责）。
