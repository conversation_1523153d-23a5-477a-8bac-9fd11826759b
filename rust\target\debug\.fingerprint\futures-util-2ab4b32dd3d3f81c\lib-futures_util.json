{"rustc": 16591470773350601817, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"channel\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 12506755554509207344, "path": 3303257178207977090, "deps": [[5103565458935487, "futures_io", false, 7996507185821471228], [1615478164327904835, "pin_utils", false, 1460387131113170825], [1811549171721445101, "futures_channel", false, 11210955741986152583], [1906322745568073236, "pin_project_lite", false, 7012000234825486395], [3129130049864710036, "memchr", false, 15575753524003208886], [6955678925937229351, "slab", false, 7901103119362381428], [7013762810557009322, "futures_sink", false, 2326873239644073587], [7620660491849607393, "futures_core", false, 15611546190662880035], [10565019901765856648, "futures_macro", false, 16127309237607719309], [16240732885093539806, "futures_task", false, 18334226013412152506]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-util-2ab4b32dd3d3f81c\\dep-lib-futures_util", "checksum": false}}], "rustflags": ["--cfg", "frb_expand"], "config": 2069994364910194474, "compile_kind": 0}