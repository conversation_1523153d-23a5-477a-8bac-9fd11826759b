set shell := ["C:\\Program Files\\Git\\bin\\bash.exe", "-c"]

default: gen lint
all: clean gen lint

gen:
    rm -rf lib/rust/* rust/src/frb_generated*
    flutter_rust_bridge_codegen generate

lint:
    cd rust && cargo fmt
    dart format .

clean:
    # 备份 build/windows/x64/runner/Debug 目录
    if [ -d "build/windows/x64/runner/Debug" ]; then \
        echo "正在备份 build/windows/x64/runner/Debug..."; \
        mkdir -p "temp_just_clean_backup"; \
        cp -a "build/windows/x64/runner/Debug" "temp_just_clean_backup/DebugData"; \
    fi; \
    # 执行 flutter clean 和其他清理命令
    flutter clean; \
    flutter pub get; \
    (cd rust && cargo clean && cargo update); \
    # 恢复 build/windows/x64/runner/Debug 目录
    if [ -d "temp_just_clean_backup/DebugData" ]; then \
        echo "正在恢复 build/windows/x64/runner/Debug..."; \
        mkdir -p "build/windows/x64/runner/Debug"; \
        cp -a "temp_just_clean_backup/DebugData/"* "build/windows/x64/runner/Debug/"; \
        rm -rf "temp_just_clean_backup"; \
    else \
        echo "未找到 build/windows/x64/runner/Debug 的备份或备份为空。如果 flutter clean 删除了该目录，将确保其存在。"; \
        mkdir -p "build/windows/x64/runner/Debug"; \
    fi
