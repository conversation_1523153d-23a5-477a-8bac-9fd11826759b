/*
 * <AUTHOR> 老董
 * @Date         : 2022-07-21 09:49:11
 * @LastEditors  : 老董
 * @LastEditTime : 2025-05-15 22:35:22
 * @Description  : PlayerPanel中的“电脑图标”按钮，用于加载引擎
 */

import 'package:flutter/material.dart' hide BoxDecoration, BoxShadow; // 确保 hide 存在
import 'package:flutter_inset_shadow/flutter_inset_shadow.dart'; // 更改为新的包
import 'package:meng_ru_ling_shi/common/global.dart';
import 'package:meng_ru_ling_shi/common/widgets/ios_menu_widget.dart';
import 'package:meng_ru_ling_shi/common/widgets/toast/toast_message.dart';
import 'package:meng_ru_ling_shi/rust/chess/piece_utils.dart' as chess_utils;

import '../../lib.dart';

class EngineLoadButton extends StatefulWidget {
  final double iconSize;
  final IconData icon;
  final chess_utils.Player player; // 使用前缀

  // Constants moved here as they don't change per instance
  static const double _containerIconRatio = 104 / 75;
  static const double _radiusContainerRatio = 27 / 104;
  static const double _deepPressDistanceContianerRatio = 17 / 104;
  static const double _middlePressDistanceContianerRatio = 10.5 / 104;
  static const double _noPressDistanceContianerRatio = 11 / 104;

  const EngineLoadButton({
    required this.player, // 使用前缀
    required this.icon,
    required this.iconSize,
    super.key,
  });

  @override
  State<EngineLoadButton> createState() => _EngineLoadButtonState();
}

class _EngineLoadButtonState extends State<EngineLoadButton> {
  final HomeController controller = Get.find<HomeController>();
  double _distance = 0.0;
  double _blur = 0.0;
  bool _isInSet = false;
  late final double _containerSize;
  late final double _radius;
  late final Color _iconColor;
  final _buttonState = NeumorphicButtonState.noPressed.obs;
  late Worker _everWorker;

  @override
  void initState() {
    super.initState();
    _containerSize = EngineLoadButton._containerIconRatio * widget.iconSize;
    _radius = EngineLoadButton._radiusContainerRatio * _containerSize;

    switch (widget.player) {
      case chess_utils.Player.red: // 使用前缀
        _iconColor = Colors.red;
        break;
      case chess_utils.Player.black: // 使用前缀
        _iconColor = Colors.black;
        break;
      default:
        throw Exception('Player is not Red or Black, Something went wrong!');
    }

    var obsVar =
        widget.player == chess_utils.Player.red ? controller.redEngineNameObs : controller.blackEngineNameObs; // 使用前缀
    _everWorker = ever(
      obsVar,
      (String engineName) {
        if (mounted) {
          // Check if the state is still mounted
          setState(() {
            if (engineName.isNotEmpty) {
              _buttonState.value = NeumorphicButtonState.middlePressed;
            } else {
              _buttonState.value = NeumorphicButtonState.noPressed;
            }
          });
        }
      },
    );
    // Initialize button state based on current engine name
    final initialEngineName = widget.player == chess_utils.Player.red
        ? controller.redEngineNameObs.value
        : controller.blackEngineNameObs.value;
    if (initialEngineName.isNotEmpty) {
      _buttonState.value = NeumorphicButtonState.middlePressed;
    } else {
      _buttonState.value = NeumorphicButtonState.noPressed;
    }
  }

  @override
  void dispose() {
    _everWorker.dispose();
    super.dispose();
  }

  Future<void> _showEngineSelectionMenu(BuildContext context, TapUpDetails details) async {
    final menuItems = [
      IOSMenuItem(text: "加载内置引擎", icon: Icons.star),
      IOSMenuItem(text: "加载自定义引擎", icon: Icons.settings),
    ];

    final choice = await showIOSMenu(
      context,
      details.globalPosition,
      menuItems,
    );

    if (choice == "加载内置引擎") {
      bool success = controller.toggleEngineControl(widget.player);
      if (success) {
        toast("已加载内置引擎");
      } else {
        toast("加载内置引擎失败");
      }
    } else if (choice == "加载自定义引擎") {
      toast("加载自定义引擎功能暂未开放");
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) {
        setState(() {
          _buttonState.value = NeumorphicButtonState.deepPressed;
        });
      },
      onTapUp: (TapUpDetails details) async {
        // It's important to use setState for UI updates if not using Obx for everything
        // However, the primary state change here is _buttonState, which Obx handles.
        // The async operation might lead to state changes after build.
        if (controller.isEngineLoaded(widget.player)) {
          if (!controller.toggleEngineControl(widget.player)) {
            toast("卸载引擎失败");
          } else {
            toast("已卸载${widget.player == chess_utils.Player.red ? '红方' : '黑方'}引擎"); // 使用前缀
          }
        } else {
          await _showEngineSelectionMenu(context, details);
          // After menu selection, engine state might change, triggering `ever`
          // No direct setState here for _buttonState as `ever` handles it.
        }
        // Reset to appropriate state after tap up if not handled by `ever`
        // This ensures the button returns to a non-deep-pressed state.
        // The `ever` callback will set it to middlePressed or noPressed.
        // If the engine load status didn't change, we might need to manually revert from deepPressed.
        // For simplicity, relying on `ever` for now. If deepPressed persists, this needs adjustment.
        // Or, more robustly:
        if (_buttonState.value == NeumorphicButtonState.deepPressed) {
          final engineName = widget.player == chess_utils.Player.red
              ? controller.redEngineNameObs.value
              : controller.blackEngineNameObs.value;
          setState(() {
            if (engineName.isNotEmpty) {
              _buttonState.value = NeumorphicButtonState.middlePressed;
            } else {
              _buttonState.value = NeumorphicButtonState.noPressed;
            }
          });
        }
      },
      child: Obx(() {
        // This setState is now within Obx's build, which is fine.
        // The actual update of _distance, _blur, _isInSet should happen in response to _buttonState changing.
        // It's better to trigger setState in the callbacks that change _buttonState if these are not reactive.
        // Or, make _distance, _blur, _isInSet Rx variables if they need to be.
        // For now, this structure is kept, assuming Obx rebuilds and re-evaluates.
        switch (_buttonState.value) {
          case NeumorphicButtonState.deepPressed:
            _distance = EngineLoadButton._deepPressDistanceContianerRatio * _containerSize;
            _blur = 3.0;
            _isInSet = true;
            break;
          case NeumorphicButtonState.middlePressed:
            _distance = EngineLoadButton._middlePressDistanceContianerRatio * _containerSize;
            _blur = 3.0;
            _isInSet = true;
            break;
          case NeumorphicButtonState.noPressed:
            _distance = EngineLoadButton._noPressDistanceContianerRatio * _containerSize;
            _blur = 6.0;
            _isInSet = false;
            break;
          // Removed default as all enum cases are covered.
        }

        return AnimatedContainer(
          duration: const Duration(milliseconds: 150),
          width: _containerSize,
          height: _containerSize,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(_radius),
            gradient: const LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.white,
                Colors.white70,
              ],
            ),
            boxShadow: [
              BoxShadow(
                color: const Color(0xff494d52),
                offset: Offset(-_distance * 0.3, -_distance * 0.15),
                blurRadius: _blur,
                spreadRadius: 0.0,
                inset: _isInSet,
              ),
              BoxShadow(
                color: const Color(0xff090d12),
                offset: Offset(_distance, _distance),
                blurRadius: _blur,
                spreadRadius: 0.0,
                inset: _isInSet,
              ),
            ],
          ),
          child: Icon(
            widget.icon,
            size: widget.iconSize,
            color: _iconColor,
          ),
        );
      }),
    );
  }
}
