import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:macos_ui/macos_ui.dart'; // 为 MacosTabView 添加
import 'package:meng_ru_ling_shi/common/widgets/time_controller.dart';
import 'package:meng_ru_ling_shi/common/widgets/toast/toast_message.dart';
import 'package:meng_ru_ling_shi/game/placement_validator.dart'; // 摆谱校验器
import 'package:meng_ru_ling_shi/rust/chess/board_utils.dart';
import 'package:meng_ru_ling_shi/rust/chess/game_manager.dart';
import 'package:meng_ru_ling_shi/rust/chess/move_utils.dart';
import 'package:meng_ru_ling_shi/rust/chess/piece_utils.dart';

import '../../common/global.dart';

class HomeController extends GetxController {
  late final MacosTabController macosTabController; // MacosTabView的控制器

  final gameManager = GameManager();
  final _currentPlayer = Player.unknown.obs;
  final FocusNode hotKeyFocusNode = FocusNode();
  final ScrollController moveHistoryScrollController = ScrollController(); // 走法历史的滚动控制器
  final _dockActivate = false.obs;
  bool _isNavigatingHistory = false; // 标记：防止在历史导航期间进行AI移动
  final _isProcessingComputerMove = false.obs; // 状态锁：防止并发处理电脑走棋

  // 电脑走棋延迟设置（仅当双方托管时生效），单位秒
  final RxInt _computerMoveDelaySec = 0.obs;
  int get computerMoveDelaySec => _computerMoveDelaySec.value;
  void updateComputerMoveDelaySec(int seconds) {
    if (_computerMoveDelaySec.value != seconds) {
      _computerMoveDelaySec.value = seconds;
      debugPrint("电脑（双方托管时）的走棋延迟已更新为: $seconds 秒");
    }
  }

  // 鼠标悬停位置和ICCS坐标
  final Rx<Offset?> _hoverPosition = Rx<Offset?>(null);
  final RxString _hoverIccsText = RxString('');
  final RxBool _showIccsTooltip = RxBool(true); // 开关：是否显示ICCS坐标提示
  final RxBool _enableFenCopyPaste = RxBool(true); // 开关：FEN复制粘贴功能
  final RxBool _flipBoardOnExchange = RxBool(false); // 开关：交换红黑时是否翻转棋盘
  final RxBool _isArrangeMode = RxBool(false); // 状态：是否为摆谱模式
  final Rx<Player> _arrangeModePlayerToMove = Player.red.obs; // 状态：摆谱模式下的执棋方
  late final PlacementValidator _placementValidator; // 摆谱校验器实例
  final Rx<Offset?> pieceFollowingMousePosition = Rx<Offset?>(null); // 状态：摆谱模式下棋子跟随鼠标的位置
  final Rx<(int, int)?> _sourcePositionForArrangementMove = Rx<(int, int)?>(null); // 状态：摆谱模式下记录棋子移动的源位置 (row, col)
  final isBoardFlippedForDisplay = false.obs; // 新增：响应式的棋盘翻转状态

  get hoverPosition => _hoverPosition.value;
  set hoverPosition(value) => _hoverPosition.value = value;

  get hoverIccsText => _hoverIccsText.value;
  set hoverIccsText(value) => _hoverIccsText.value = value;

  get showIccsTooltip => _showIccsTooltip.value;
  set showIccsTooltip(value) => _showIccsTooltip.value = value;

  get enableFenCopyPaste => _enableFenCopyPaste.value;
  set enableFenCopyPaste(value) => _enableFenCopyPaste.value = value;

  get flipBoardOnExchange => _flipBoardOnExchange.value;
  set flipBoardOnExchange(value) => _flipBoardOnExchange.value = value;

  get isArrangeMode => _isArrangeMode.value;

  Player get arrangeModePlayerToMove => _arrangeModePlayerToMove.value;

  final _selectedPieceForArrangement = Rx<SidePieceType?>(null); // 状态：摆谱模式下当前选中的棋子
  SidePieceType? get selectedPieceForArrangement => _selectedPieceForArrangement.value;
  // (int, int)? get sourcePositionForArrangementMove => _sourcePositionForArrangementMove.value; // Getter：摆谱模式下棋子移动的源位置 (如果外部需要)

  // PlayerDigitalClock状态控制器
  final _redTimeController = DigitTimeController().obs;
  get redTimeController => _redTimeController;
  set redTimeController(value) => _redTimeController.value = value;

  final _blackTimeController = DigitTimeController().obs;
  get blackTimeController => _blackTimeController;
  set blackTimeController(value) => _blackTimeController.value = value;

  // 引擎名称
  final _redEngineName = "".obs;
  get redEngineNameObs => _redEngineName;
  get redEngineName => _redEngineName.value;
  set redEngineName(value) => _redEngineName.value = value;

  final _blackEngineName = "".obs;
  get blackEngineNameObs => _blackEngineName;
  get blackEngineName => _blackEngineName.value;
  set blackEngineName(value) => _blackEngineName.value = value;

  // 状态：是否由电脑托管
  final _isRedHosted = false.obs;
  get isRedHosted => _isRedHosted.value;
  set isRedHosted(value) => _isRedHosted.value = value;

  final _isBlackHosted = false.obs;
  get isBlackHosted => _isBlackHosted.value;
  set isBlackHosted(value) => _isBlackHosted.value = value;

  // 状态：是否显示dock
  get dockActivate => _dockActivate.value;
  set dockActivate(value) => _dockActivate.value = value;

  final _gameStarted = false.obs; // 状态：游戏是否已开始
  set gameStarted(value) => _gameStarted.value = value;
  get gameStarted => _gameStarted.value;

  final _logs = [].obs; // UI日志列表
  get logs => _logs;
  set logs(value) => _logs.value = value;

  // UI棋盘状态 (包含棋子、走法历史等)
  // 该对象存储了在棋盘上每个位置的棋子信息（包括空棋子），所以总共有90个元素
  final boardPiecesView = BoardPiecesView();
  get moveHistory => boardPiecesView.moveHistory; // 走法历史
  final arrowMoves = <ChessMove>[]; // 需要绘制箭头的棋步

  final _animatedContainerHeight = toobarHeight.obs; // 动画容器高度 (工具栏)
  get animatedContainerHeight => _animatedContainerHeight.value;
  set animatedContainerHeight(value) => _animatedContainerHeight.value = value;

  // 棋子相关的尺寸 (由UI动态计算并设置)
  var leftTopOffSet = const Offset(0.0, 0.0); // 左上角棋子位置距离棋盘左上角的offset
  var pieceGap = 0.0; // 相邻2个棋子中心位置的间距 (x、y轴相同)
  var pieceSize = 0.0; // 调整过的棋子尺寸 (宽高一致)

  // 调整过的浮动工具栏宽度 (由UI动态计算并设置)
  final _panelWidth = 0.0.obs;
  get panelWidth => _panelWidth.value;
  set panelWidth(value) => _panelWidth.value = value;

  // 调整过的浮动工具栏的圆角半径 (由UI动态计算并设置)
  final _borderRadius = 0.0.obs;
  get borderRadius => _borderRadius.value;
  set borderRadius(value) => _borderRadius.value = value;

  @override
  void onInit() {
    gameManager.clearBoard();
    super.onInit();
  }

  HomeController() {
    macosTabController = MacosTabController(initialIndex: 0, length: 2); // 初始化MacosTabController
    hotKeyFocusNode.requestFocus();
    _placementValidator = PlacementValidator(gameManager: gameManager); // 初始化PlacementValidator
    ever(_gameStarted, (_) => _handlePlayerOrGameSetChange(_currentPlayer.value));
    ever(_currentPlayer, (player) async {
      await _handlePlayerOrGameSetChange(player);
    });
    ever(_isRedHosted, (_) => _checkComputerMoveIfEngineLoaded());
    ever(_isBlackHosted, (_) => _checkComputerMoveIfEngineLoaded());
  }

  @override
  void dispose() {
    macosTabController.dispose(); // 释放MacosTabController
    hotKeyFocusNode.dispose();
    moveHistoryScrollController.dispose(); // 释放走法历史的滚动控制器
    super.dispose();
  }

  // 处理玩家切换或游戏设置变化
  Future _handlePlayerOrGameSetChange(Player player) async {
    if (!gameStarted || isArrangeMode) {
      // 如果游戏未开始，或者在摆谱模式下，则不进行游戏结束判断或电脑走棋
      return;
    }
    debugPrint("轮到${player.getName()}下");
    final winningFuture = gameManager.isCurrentPlayerWinning();
    final losingFuture = gameManager.isCurrentPlayerLosing();
    final result = await Future.any([winningFuture, losingFuture]);
    if (result) {
      gameStarted = false; // 标记本局游戏结束
      if (await winningFuture) {
        toast('游戏结束\n${_currentPlayer.value.getName()}赢\n 因${_currentPlayer.value.opposite().getName()}被将死或困毙');
      } else if (await losingFuture) {
        toast('游戏结束\n${_currentPlayer.value.getName()}输\n 因${_currentPlayer.value.getName()}被将死或困毙');
      }
      _stopTimer(); // 停止计时器
    } else {
      _checkComputerMoveIfEngineLoaded(); // 检查是否轮到电脑走棋
    }
  }

  // 检查并执行电脑走棋 (如果当前玩家由电脑托管)
  void _checkComputerMoveIfEngineLoaded() async {
    if (_isNavigatingHistory) return; // 如果正在导航历史记录，电脑不应移动
    if (_isProcessingComputerMove.value) {
      debugPrint("另一电脑走棋正在处理中，本次调用 _checkComputerMoveIfEngineLoaded 被跳过。");
      return;
    }

    if ((_currentPlayer.value == Player.red && isRedHosted) ||
        (_currentPlayer.value == Player.black && isBlackHosted)) {
      _isProcessingComputerMove.value = true; // 设置处理锁
      try {
        // 仅在双方都由电脑托管且设置了延迟时，才进行延迟
        if (isRedHosted && isBlackHosted && _computerMoveDelaySec.value > 0) {
          int delayMilliseconds = _computerMoveDelaySec.value * 1000;
          debugPrint("双方电脑托管，使用设置的延迟 ${_computerMoveDelaySec.value}秒 (即 ${delayMilliseconds}ms) 执行电脑走棋。");
          await Future.delayed(Duration(milliseconds: delayMilliseconds));
        }

        // 在尝试获取和处理移动之前，再次检查引擎是否仍然托管以及是否轮到该引擎 (因为Future.delayed后状态可能改变)
        if (!((_currentPlayer.value == Player.red && isRedHosted) ||
            (_currentPlayer.value == Player.black && isBlackHosted))) {
          debugPrint("延迟后，引擎状态或当前玩家已改变，取消本次电脑走棋。");
          return;
        }
        if (_isNavigatingHistory) {
          // 再次检查历史导航状态
          debugPrint("延迟后，进入历史导航模式，取消本次电脑走棋。");
          return;
        }

        final randomMove = await gameManager.getRandomLegalMove(); // 获取一个合法的随机走法
        debugPrint("电脑选择的随机下法: $randomMove");
        if (randomMove != null) {
          // 在调用 handleComputerMove 之前，再次检查引擎状态 (因为handleComputerMove是异步的)
          if (!((_currentPlayer.value == Player.red && isRedHosted) ||
              (_currentPlayer.value == Player.black && isBlackHosted))) {
            debugPrint("获取随机走法后，引擎状态或当前玩家已改变，取消处理该走法。");
            return;
          }
          if (_isNavigatingHistory) {
            debugPrint("获取随机走法后，进入历史导航模式，取消处理该走法。");
            return;
          }

          await handleComputerMove(randomMove); // 处理电脑走法
        } else {
          debugPrint("没有合法移动，游戏可能结束");
          // 即使没有合法移动，也可能需要检查游戏是否结束（例如困毙）
          // _handlePlayerOrGameSetChange 会在 gameStarted 变为 true 或 _currentPlayer 改变时被调用，进而检查电脑走棋
        }
      } catch (e, s) {
        debugPrint("Error in _checkComputerMoveIfEngineLoaded: $e\nStack trace:\n$s");
        toast("电脑走棋时发生错误: $e");
      } finally {
        _isProcessingComputerMove.value = false; // 释放处理锁
      }
    }
  }

  // 工具栏按钮点击事件处理
  Future onToolButtonPressed(String logContent) async {
    addUiLog(logContent);
    switch (logContent) {
      case newGameBtnLog: // 新对局
        gameManager.reset();
        boardPiecesView.reset(await gameManager.getDisplayBoard(), gameManager.currentPlayer); // 重置UI棋盘
        _resetTimer(); // 重置并启动计时器
        _currentPlayer.value = Player.red; // 设置当前玩家为红方
        gameStarted = true; // 标记游戏开始
        break;
      case newAIBtnLog: // 新AI对局 (示例，可能需要更多逻辑)
        // _redTimeController.value.runTimer();
        break;
      default: // 其他测试用按钮
    }
  }

  // 使用Rust棋盘数据更新UI棋子 (通常在初始化或FEN粘贴后调用)
  void _updateBoardPiecesWithUI(U8Array256 origBoardArray) {
    // final indexes = []; // 此变量未使用，可以移除
    for (int i = 0; i < origBoardArray.length; i++) {
      final index90 = convertArray256IndexToArray90Index(i);
      if (index90 == null) {
        continue;
      }
      final sidePieceTypeIndex = origBoardArray[i];
      boardPiecesView.updatePiece(index90, SidePieceType.getSidePieceTypeByIndex(sidePieceTypeIndex), MaskedType.none);
      // indexes.add(index90); // 此变量未使用，可以移除
    }
    boardPiecesView.refresh(); // 刷新UI棋盘
  }

  // 停止双方计时器
  void _stopTimer() {
    _redTimeController.value.stopTimer();
    _blackTimeController.value.stopTimer();
  }

  // 重置并启动计时器 (通常在新对局开始时调用)
  void _resetTimer() {
    _stopTimer();
    _redTimeController.value.resetTimer();
    _blackTimeController.value.resetTimer();
    _redTimeController.value.runTimer(); // 默认红方先手，启动红方计时器
  }

  // 切换计时器 (轮到对方走棋时调用)
  void _switchTimer() {
    switch (gameManager.currentPlayer) {
      case Player.red: // 现在轮到红方，应暂停黑方计时，启动红方计时
        _blackTimeController.value.pauseTimer(); // 暂停黑方
        _redTimeController.value.runTimer(); // 启动红方
        break;
      case Player.black: // 现在轮到黑方，应暂停红方计时，启动黑方计时
        _redTimeController.value.pauseTimer(); // 暂停红方
        _blackTimeController.value.runTimer(); // 启动黑方
        break;
      default:
        throw Exception('Player is not Red or Black, Something went wrong!');
    }
  }

  // 添加UI日志
  void addUiLog(String logContent) {
    _logs.insert(
      0,
      DataRow(
        cells: [
          DataCell(Text(getCurrentTimeString())),
          DataCell(Text(logContent)),
        ],
      ),
    );
  }

  // 显示确认对话框 (当在历史棋局中尝试走棋时)
  // 返回true表示用户确认覆盖，false表示取消
  Future<bool> _showConfirmOverwriteDialog() async {
    final result = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('确认操作'),
        content: const Text('您正在历史棋局中尝试自定义的走法（即使和下一步棋完全一致）。\n如果继续，此步之后的所有棋谱记录将被删除。\n是否确定？'),
        actions: <Widget>[
          TextButton(
            child: const Text('取消'),
            onPressed: () {
              Get.back(result: false); // 返回false
            },
          ),
          TextButton(
            child: const Text('确定'),
            onPressed: () {
              Get.back(result: true); // 返回true
            },
          ),
        ],
      ),
      barrierDismissible: false, // 用户必须通过按钮选择
    );
    return result ?? false; // 如果对话框被意外关闭，默认为取消
  }

  // 处理棋盘点击事件 (包括普通游戏模式和摆谱模式)
  Future onWindowClicked(Offset localPosition) async {
    if (isArrangeMode) {
      // --- 开始摆谱模式逻辑 ---
      final MaskedPiece? clickedPieceData = getChessPosFromOffset(localPosition);

      if (clickedPieceData != null) {
        // 点击了棋盘上的有效位置
        // 单击逻辑 (左键):
        final targetRow = clickedPieceData.row;
        final targetCol = clickedPieceData.col;
        final targetUiIndex = clickedPieceData.index;

        if (selectedPieceForArrangement != null) {
          // 当前“手中”有棋子（来自面板或之前从棋盘拾取）
          final pieceToPlaceOrMove = selectedPieceForArrangement!;

          if (_sourcePositionForArrangementMove.value != null) {
            // **** 这是一个移动操作 (从棋盘A点到B点) ****
            final (srcRow, srcCol) = _sourcePositionForArrangementMove.value!;
            final srcUiIndex =
                boardPiecesView.pieces.indexWhere((p) => p.row == srcRow && p.col == srcCol); // 查找源棋子的UI索引

            // 防止将棋子移动到自身位置（无实际移动）
            if (srcRow == targetRow && srcCol == targetCol) {
              selectPieceForArrangement(null); // 取消选择，有效取消“拾取”操作
              return;
            }

            final originalPieceAtSourceInRust = gameManager.getSidePieceByIndex(
                pos: getArray256IndexFromBoardRowCol(srcRow, srcCol)); // 存储源位置的原始棋子 (用于回滚)

            gameManager.updateBoard(
                visualRow: srcRow, visualCol: srcCol, pieceIndex: SidePieceType.none.getSidePieceTypeIndex());
            debugPrint("摆谱模式：(尝试移动) 从 r${srcRow}c$srcCol 移除 ${originalPieceAtSourceInRust.name} (Rust端)");

            // 步骤B：尝试使用验证器将棋子放置在目标位置
            // targetRow, targetCol 来自 MaskedPiece，已经是视觉坐标。
            // _placementValidator.handlePiecePlacement 内部会调用 Rust 的 gameManager.updateBoard，
            // 该函数现在期望视觉坐标。
            final bool moveSuccessful = _placementValidator.handlePiecePlacement(
              row: targetRow, // 直接传递视觉坐标
              col: targetCol, // 直接传递视觉坐标
              pieceToPlace: pieceToPlaceOrMove, // 这是源位置的棋子
              onSuccess: () {
                // Rust棋盘现在是：src=None, dst=pieceToPlaceOrMove (期望状态)
                // 同步UI：
                if (srcUiIndex != -1) {
                  boardPiecesView.updatePiece(srcUiIndex, SidePieceType.none, MaskedType.none); // 清空源UI位置
                }
                boardPiecesView.updatePiece(targetUiIndex, pieceToPlaceOrMove, MaskedType.none); // 在目标UI位置放置棋子
                debugPrint("摆谱模式：通过校验，将 r${srcRow}c$srcCol 的 ${pieceToPlaceOrMove.name} 移动到 r${targetRow}c$targetCol");
              },
              onFailure: (errorMessage) {
                // PlacementValidator失败，它应已回滚其在dst上的放置尝试 (dst已恢复原状)
                // 我们必须恢复Rust棋盘中的源棋子 (因为我们手动清除了它)
                // srcRow, srcCol are logical. Need conversion.
                gameManager.updateBoard(
                    visualRow: srcRow,
                    visualCol: srcCol,
                    pieceIndex: originalPieceAtSourceInRust.getSidePieceTypeIndex());
                debugPrint(
                    "摆谱模式：移动失败 from r${srcRow}c$srcCol to r${targetRow}c$targetCol for ${pieceToPlaceOrMove.name}: $errorMessage. 源位置已恢复。");
                toast(errorMessage); // 显示错误信息
                // UI不应从移动尝试之前的状态改变
              },
            );

            if (moveSuccessful) {
              selectPieceForArrangement(null); // 仅在成功时重置选择和源位置标记
            }
            // 失败时，由于onMouseHover，棋子保持选中状态并跟随鼠标
          } else {
            // **** 这是一个添加操作 (来自面板的棋子放置到棋盘) ****
            // targetRow, targetCol 来自 MaskedPiece，已经是视觉坐标。
            // _placementValidator.handlePiecePlacement 内部会调用 Rust 的 gameManager.updateBoard，
            // 该函数现在期望视觉坐标。
            _placementValidator.handlePiecePlacement(
              row: targetRow, // 直接传递视觉坐标
              col: targetCol, // 直接传递视觉坐标
              pieceToPlace: pieceToPlaceOrMove,
              onSuccess: () {
                boardPiecesView.updatePiece(targetUiIndex, pieceToPlaceOrMove, MaskedType.none); // 在UI上放置棋子
                debugPrint("摆谱模式：通过校验，在 r${targetRow}c$targetCol 放置 ${pieceToPlaceOrMove.name}");
                selectPieceForArrangement(null); // 放置后取消选择
              },
              onFailure: (errorMessage) {
                debugPrint("摆谱模式：放置失败 at r${targetRow}c$targetCol for ${pieceToPlaceOrMove.name}: $errorMessage");
                toast(errorMessage); // 显示错误信息, 保持棋子选中状态
              },
            );
          }
        } else {
          // “手中”没有棋子。此点击是从棋盘上“拾取”一个棋子。
          if (clickedPieceData.pieceType() != SidePieceType.none) {
            selectPieceForArrangement(
              // 调用修改后的selectPieceForArrangement，指示它来自棋盘
              clickedPieceData.pieceType(),
              fromBoard: true,
              boardRow: targetRow,
              boardCol: targetCol,
            );
            // 拾取时的视觉反馈（例如棋子跟随鼠标）由selectPieceForArrangement/onMouseHover处理
          }
        }
      }
      // --- 结束摆谱模式逻辑 ---
    } else {
      // --- 开始普通游戏模式逻辑 ---
      if ((gameManager.currentPlayer == Player.red && isRedHosted) ||
          (gameManager.currentPlayer == Player.black && isBlackHosted)) {
        // 如果当前玩家由电脑托管，则忽略用户点击
        return;
      }

      final MaskedPiece? validClickedPiece = getChessPosFromOffset(localPosition); // 获取点击位置的棋子信息
      if (validClickedPiece == null) return; // 点击无效位置
      debugPrint("点击了棋盘中的有效位置：行${validClickedPiece.row}列${validClickedPiece.col}");

      final focusedPiece = boardPiecesView.getFocusedPiece(); // 获取当前已选中的棋子
      if (focusedPiece != null) {
        // 如果已有棋子被选中
        if (gameManager.currentPlayer != focusedPiece.player()) {
          throw '当前玩家和被选中的棋子的不是同一玩家'; // 理论上不应发生
        }

        if (validClickedPiece.player() == gameManager.currentPlayer) {
          // 如果点击的是己方其他棋子
          boardPiecesView.setFocusedPiece(validClickedPiece); // 则切换焦点到新棋子
        } else if (await isMoveOrEatable(focusedPiece, validClickedPiece)) {
          // 如果点击的是对方棋子或空格，且可走/可吃
          final srcIndex = getArray256IndexFromBoardRowCol(focusedPiece.row, focusedPiece.col); // 源棋盘索引 (0-255)
          final dstIndex =
              getArray256IndexFromBoardRowCol(validClickedPiece.row, validClickedPiece.col); // 目标棋盘索引 (0-255)
          final iccsMove = await getIccsMoveStrFromPos(srcIndex: srcIndex, dstIndex: dstIndex); // 获取ICCS表示的走法
          final pieceMove = PieceMove.fromIccs(iccs: iccsMove, gameManager: gameManager); // 创建PieceMove对象 (包含中文棋谱)

          // 检查是否在历史记录中走棋
          if (boardPiecesView.activeMoveIndex < boardPiecesView.moveHistory.length - 1 &&
              boardPiecesView.moveHistory.isNotEmpty) {
            final confirm = await _showConfirmOverwriteDialog(); // 弹出确认对话框
            if (!confirm) {
              // 用户取消
              boardPiecesView.clearAllMasks(); // 清除焦点
              return;
            }
            // 用户确认，BoardPieces.move() 内部会处理历史截断逻辑
          }

          boardPiecesView.move(focusedPiece, validClickedPiece, pieceMove); // 执行移动并记录棋谱
          await _updateSwitch(pieceMove); // 更新后端数据、切换计时器和玩家
        }
      } else if (validClickedPiece.player() == gameManager.currentPlayer) {
        // 如果没有棋子被选中，且点击的是己方棋子
        boardPiecesView.setFocusedPiece(validClickedPiece); // 则选中该棋子
      }
    } // --- 结束普通游戏模式逻辑 ---
  } // 方法结束

  // 切换玩家 (Rust后端和UI前端)
  Future _switchPlayer() async {
    gameManager.switchPlayer(); // Rust后端切换
    _currentPlayer.value = gameManager.currentPlayer; // 更新UI的当前玩家状态
  }

  // 判断棋子是否可以从srcPiece移动或吃到dstPiece
  Future isMoveOrEatable(MaskedPiece srcPiece, MaskedPiece dstPiece) async {
    if (gameManager.currentPlayer == Player.unknown) return false;
    if (srcPiece.player()! != gameManager.currentPlayer) {
      throw '错误：带检查的起始位置棋子非当前玩家';
    }
    final distPiecePlayer = dstPiece.player();
    if (distPiecePlayer != null && distPiecePlayer == gameManager.currentPlayer) {
      return false; // 不能走到己方棋子位置
    }
    // srcPiece.row/col and dstPiece.row/col are already visual coordinates.
    // Pass them directly to the Rust API, which now expects visual coordinates.
    return await gameManager.isPieceValidMove(
      visualSrcRow: srcPiece.row, // 直接使用视觉坐标
      visualSrcCol: srcPiece.col, // 直接使用视觉坐标
      visualDstRow: dstPiece.row, // 直接使用视觉坐标
      visualDstCol: dstPiece.col, // 直接使用视觉坐标
    );
  }

  // 根据鼠标的本地Offset获取最近的棋盘行列号 (视觉上的，1-based)
  // 若鼠标所选位置没有（空）棋子，则返回null (此注释不准确，此函数总是返回行列号，可能无效)
  List getNearestChessPos(Offset localPosition) {
    int? finalRow;
    int? finalCol;
    const safeRatio = 0.9; // 安全边距比例，用于判断点击是否在棋子中心附近

    // 计算x轴 (列)
    final xCorrectLen = localPosition.dx - leftTopOffSet.dx;
    if (xCorrectLen <= 0) {
      finalCol = 1; // 超出左边界，视为第1列
    } else {
      final col = xCorrectLen ~/ pieceGap; // 整数除法得到大致列索引
      final xModNum = xCorrectLen % pieceGap; // 取余判断在格子内的具体位置
      if (xModNum == 0) {
        // 正好在格线上
        finalCol = col + 1;
      } else {
        if (xModNum < (pieceSize / 2) * safeRatio) {
          // 在棋子左半边
          finalCol = col + 1;
        } else if (xModNum > (pieceGap - pieceSize / 2 * safeRatio)) {
          // 在棋子右半边 (靠近下一格)
          finalCol = col + 2;
        }
        // 如果在中间区域，finalCol可能为null，表示未精确落在棋子上 (此逻辑似乎不完整，应总有返回值)
      }
    }
    finalCol ??= (xCorrectLen / pieceGap).round() + 1; // 补充：如果上述逻辑未赋值，则四舍五入

    // 计算y轴 (行)
    final yCorrectLen = localPosition.dy - leftTopOffSet.dy;
    if (yCorrectLen <= 0) {
      finalRow = 1; // 超出上边界，视为第1行
    } else {
      final row = yCorrectLen ~/ pieceGap;
      final yModNum = yCorrectLen % pieceGap;
      if (yModNum == 0) {
        finalRow = row + 1;
      } else {
        if (yModNum < (pieceSize / 2) * safeRatio) {
          finalRow = row + 1;
        } else if (yModNum > (pieceGap - pieceSize / 2 * safeRatio)) {
          finalRow = row + 2;
        }
      }
    }
    finalRow ??= (yCorrectLen / pieceGap).round() + 1; // 补充

    return [finalRow, finalCol];
  }

  // 根据鼠标的本地Offset获取棋盘上的棋子信息 (MaskedPiece)
  MaskedPiece? getChessPosFromOffset(Offset localPosition) {
    final nearestPos = getNearestChessPos(localPosition); // 获取视觉行列
    var visualRow = nearestPos[0] as int?; // 视觉行号 (1-10)
    var visualCol = nearestPos[1] as int?; // 视觉列号 (1-9)

    if (visualRow == null || visualCol == null) {
      return null;
    }

    // 直接使用视觉坐标在 board.pieces 中查找棋子
    // board.pieces 现在应该基于 gameManager.getDisplayBoard() 来存储视觉坐标
    if (visualRow >= 1 && visualRow <= 10 && visualCol >= 1 && visualCol <= 9) {
      for (var piece in boardPiecesView.pieces) {
        if (piece.row == visualRow && piece.col == visualCol) {
          return piece;
        }
      }
    }
    return null; // 未找到棋子
  }

  // 切换引擎托管状态 (暂时只考虑内置引擎)
  bool toggleEngineControl(Player fromPlayer) {
    if (fromPlayer == Player.red) {
      isRedHosted = !isRedHosted;
      if (isRedHosted) {
        _redEngineName.value = "内置引擎";
      } else {
        _redEngineName.value = "";
      }
    } else if (fromPlayer == Player.black) {
      isBlackHosted = !isBlackHosted;
      if (isBlackHosted) {
        _blackEngineName.value = "内置引擎";
      } else {
        _blackEngineName.value = "";
      }
    } else {
      return false; // 无效玩家
    }
    return true;
  }

  // 检查指定玩家的引擎是否已加载
  bool isEngineLoaded(Player player) {
    switch (player) {
      case Player.red:
        return isRedHosted;
      case Player.black:
        return isBlackHosted;
      default:
        throw Exception('Player is not Red or Black, Something went wrong!');
    }
  }

  // 获取指定玩家的引擎名称 (若为人类则显示"(人类)")
  String getEngineName(Player player) {
    switch (player) {
      case Player.red:
        return _redEngineName.value.isEmpty ? "(人类)" : _redEngineName.value;
      case Player.black:
        return _blackEngineName.value.isEmpty ? "(人类)" : _blackEngineName.value;
      default:
        throw Exception('Player is not Red or Black, Something went wrong!');
    }
  }

  // 处理电脑走棋
  Future handleComputerMove(PieceMove move) async {
    // PieceMove 对象中已经有 srcRow, srcCol, dstRow, dstCol
    final srcRow = move.srcRow;
    final srcCol = move.srcCol;
    final dstRow = move.dstRow;
    final dstCol = move.dstCol;

    // 计算90个方格棋盘的索引 (用于在 board.pieces 中查找)
    final srcUiIndex = (srcRow - 1) * 9 + (srcCol - 1);
    final dstUiIndex = (dstRow - 1) * 9 + (dstCol - 1);

    // 确保索引在有效范围内
    if (srcUiIndex < 0 || srcUiIndex >= 90 || dstUiIndex < 0 || dstUiIndex >= 90) {
      throw RangeError('无效的棋子UI索引');
    }

    // 找到源棋子和目标位置 (从UI棋盘数据 board.pieces)
    final srcPiece = boardPiecesView.pieces[srcUiIndex];
    final dstPiece = boardPiecesView.pieces[dstUiIndex];

    if (srcPiece.pieceType() == SidePieceType.none) {
      throw StateError('源位置没有找到棋子: row=$srcRow, col=$srcCol, ui_index=$srcUiIndex');
    }

    // 计算棋盘索引 (0-255，用于获取ICCS和中文棋谱，与Rust交互)
    final srcBoardIndex = getArray256IndexFromBoardRowCol(srcPiece.row, srcPiece.col);
    final dstBoardIndex = getArray256IndexFromBoardRowCol(dstPiece.row, dstPiece.col);

    // 获取ICCS坐标 (基于Rust端的逻辑坐标)
    final iccsMove = await getIccsMoveStrFromPos(
      srcIndex: srcBoardIndex,
      dstIndex: dstBoardIndex,
    );

    // 使用fromIccs方法创建PieceMove对象 (确保包含最新的中文棋谱)
    final pieceMoveForHistory = PieceMove.fromIccs(
      iccs: iccsMove, // 这个iccsMove是基于srcBoardIndex, dstBoardIndex重新计算的
      gameManager: gameManager,
    );
    // 注意：传入的 move 参数 (来自引擎) 和这里重新创建的 pieceMoveForHistory 的 chinese 字段可能不同，
    // 但 iccs, src/dst row/col 应该是相同的。我们主要用 pieceMoveForHistory 来更新棋谱历史。

    // 检查是否在历史记录中走棋 (电脑走棋)
    if (boardPiecesView.activeMoveIndex < boardPiecesView.moveHistory.length - 1 &&
        boardPiecesView.moveHistory.isNotEmpty) {
      // 对于电脑走棋，如果是在历史状态上，通常假设它应该覆盖后续历史。
      // 但根据用户要求，也应该提示。如果用户取消，电脑这一步就不走了。
      final confirm = await _showConfirmOverwriteDialog();
      if (!confirm) {
        debugPrint("电脑在历史棋局中走棋被用户取消。");
        // 为简单起见，暂时不执行，等待下一次轮到电脑。
        // 重要：需要确保当前玩家和计时器状态正确。由于棋盘未变，玩家和计时器也应保持不变。
        return; // 电脑不走这一步
      }
      // 用户确认，BoardPieces.move() 内部会处理历史截断逻辑
    }

    // 移动棋子并传入棋谱记录 (使用新创建的 pieceMoveForHistory)
    boardPiecesView.move(srcPiece, dstPiece, pieceMoveForHistory);

    await _updateSwitch(pieceMoveForHistory); // 更新后端、计时器、玩家
  }

  // 走棋后的统一更新逻辑
  Future _updateSwitch(PieceMove move) async {
    await _updateBackBoardData(); // 同步UI棋盘到Rust后端
    await _switchPlayer(); // 先切换玩家
    _switchTimer(); // 再根据新的当前玩家切换计时器
  }

  // 将当前UI棋盘状态 (board.pieces) 同步到Rust GameManager后端
  Future _updateBackBoardData() async {
    for (var piece in boardPiecesView.pieces) {
      // piece.row and piece.col are already visual coordinates.
      // Pass them directly to the Rust API, which now expects visual coordinates.
      await gameManager.updateBoard(
        visualRow: piece.row, // 直接使用视觉坐标
        visualCol: piece.col, // 直接使用视觉坐标
        pieceIndex: piece.getIndexOfSidePieceType(),
      );
    }
  }

  // 复制当前棋盘FEN到剪贴板
  Future copyFenToClipboard() async {
    if (!_enableFenCopyPaste.value) {
      // 检查功能是否启用
      return;
    }
    // 只有在非摆谱模式下，才检查游戏是否已开始
    if (!isArrangeMode && !gameStarted) {
      toast('游戏尚未开始,无法复制FEN棋盘信息到剪贴板');
      return;
    }

    // 根据是否在摆谱模式决定使用哪个玩家信息生成FEN
    final String fen = await gameManager.getDisplayBoardFen();

    await Clipboard.setData(ClipboardData(text: fen));
    toast('FEN已复制到剪贴板');
  }

  // 从剪贴板粘贴FEN到棋盘
  Future<void> copyClipboardFenToBoard() async {
    // 改为 async
    if (!_enableFenCopyPaste.value) {
      // 检查功能是否启用
      return;
    }
    try {
      final clipboardData = await Clipboard.getData(Clipboard.kTextPlain);
      if (clipboardData == null || clipboardData.text == null) {
        toast('剪贴板中没有有效的字符串数据');
        return;
      }
      String fenString = clipboardData.text!.trim();
      debugPrint("the input fen is: $fenString");

      // 调用 GameManager 的 loadFen 方法，它会处理FEN解析、棋盘归一化和is_flipped状态设置
      gameManager.loadFen(fenStr: fenString);
      // loadFen 成功后，Rust端的 gameManager 实例的 board, currentPlayer, isFlipped 均已正确更新

      // 从 Rust GameManager 同步状态到 Dart UI
      final newFenString = await gameManager.getDisplayBoardFen();
      debugPrint("the display fen is: $newFenString");
      boardPiecesView.reset(await gameManager.getDisplayBoard(), gameManager.currentPlayer); // 更新棋盘UI和棋谱
      _currentPlayer.value = gameManager.currentPlayer; // 更新执棋方
      _arrangeModePlayerToMove.value = gameManager.currentPlayer; // 更新摆谱模式执棋方
      isBoardFlippedForDisplay.value = gameManager.needFlipForDisplay; // 同步翻转状态

      if (!gameStarted) {
        // 如果游戏尚未开始，则启动游戏
        gameStarted = true;
      }

      // 统一调整计时器状态
      _stopTimer(); // 停止所有计时器
      _redTimeController.value.resetTimer(); // 重置双方时间
      _blackTimeController.value.resetTimer();

      if (gameManager.currentPlayer == Player.red) {
        // 根据FEN中的执棋方启动相应计时器
        _redTimeController.value.runTimer();
      } else if (gameManager.currentPlayer == Player.black) {
        _blackTimeController.value.runTimer();
      }
      // _handlePlayerOrGameSetChange 会在 gameStarted 变为 true 或 _currentPlayer 改变时被调用，进而检查电脑走棋

      toast('FEN处理成功。'); // 进一步注释掉此行以排查错误
    } catch (e) {
      // 这个 catch 会捕获 gameManager.loadFen 抛出的异常 (来自Result::Err)
      // 以及 Clipboard.getData 可能的异常
      toast('FEN处理失败: ${e.toString()}');
      debugPrint("FEN处理失败: $e");
    }
  }

  // 卸载指定玩家的引擎
  unloadEngine(Player player) {
    if (isEngineLoaded(player)) {
      toggleEngineControl(player); // 调用切换函数即可
    }
  }

  /// 处理鼠标悬停事件，更新悬停位置和ICCS坐标
  void onMouseHover(Offset localPosition) {
    // 如果不显示ICCS提示，并且不是在摆谱模式下已选择棋子的情况，则直接返回
    if (!showIccsTooltip && !(isArrangeMode && selectedPieceForArrangement != null)) {
      clearMouseHover(); // 清除所有悬停状态
      return;
    }

    // 更新悬停位置 (用于ICCS提示和/或棋子跟随)
    hoverPosition = localPosition;

    if (isArrangeMode && selectedPieceForArrangement != null) {
      // 在摆谱模式且有棋子选中时，更新棋子跟随鼠标的位置
      pieceFollowingMousePosition.value = localPosition;
    } else {
      // 其他情况下，不应有棋子跟随鼠标
      pieceFollowingMousePosition.value = null;
    }

    // 获取最近的棋盘位置 (仅用于ICCS坐标文本更新)
    final nearestPos = getNearestChessPos(localPosition);
    var row = nearestPos[0] as int?; // 视觉行
    var col = nearestPos[1] as int?; // 视觉列

    // 如果棋盘已翻转，需要调整行列坐标以匹配内部逻辑
    // Use gameManager.needFlipForDisplay for logic, this.needFlipForDisplay (_isFlipped.value) for UI state
    if (gameManager.needFlipForDisplay && row != null && col != null) {
      row = 11 - row; // 视觉第1行 -> 逻辑第10行
      col = 10 - col; // 视觉的第1列 -> 逻辑的第9列
    }

    // 如果位置有效，转换为ICCS坐标
    if (row != null && col != null && row >= 1 && row <= 10 && col >= 1 && col <= 9) {
      // ICCS坐标格式：列(a-i)行(0-9)，例如a0, e5, i9
      // 列：逻辑1-9 对应 a-i
      // 行：逻辑1-10 对应 ICCS的9-0 (从上到下)
      final colChar = String.fromCharCode('a'.codeUnitAt(0) + (col - 1));
      final rowChar = (10 - row).toString(); // ICCS行号是从棋盘底部向上数的

      hoverIccsText = '$colChar$rowChar';
    } else {
      hoverIccsText = ''; // 无效位置则清空
    }
  }

  /// 清除鼠标悬停状态 (位置、ICCS文本、棋子跟随)
  void clearMouseHover() {
    hoverPosition = null;
    hoverIccsText = '';
    pieceFollowingMousePosition.value = null; // 同时清除棋子跟随位置
  }

  /// 切换ICCS坐标提示的显示状态
  void toggleIccsTooltip() {
    showIccsTooltip = !showIccsTooltip;
    if (!showIccsTooltip) {
      // 如果关闭提示，则清除当前悬停信息
      clearMouseHover();
    }
  }

  /// 翻转棋盘（红黑方上下视觉翻转）
  void flipBoard({needToast = true}) async {
    gameManager.needFlipForDisplay = !gameManager.needFlipForDisplay;
    isBoardFlippedForDisplay.value = gameManager.needFlipForDisplay;

    // 清除所有mask状态，因为翻转后坐标含义已改变
    boardPiecesView.clearAllMasks();

    // 重新获取翻转后的显示棋盘数据
    boardPiecesView.reset(await gameManager.getDisplayBoard(), gameManager.currentPlayer);

    if (needToast) {
      toast(gameManager.needFlipForDisplay ? '棋盘已翻转：黑方在下' : '棋盘已复原：红方在下');
    }

    clearMouseHover();
  }

  /// 交换棋盘上的所有红黑棋子 (仅摆谱模式下使用)
  void exchangeAllPiecesOnBoard() async {
    if (!isArrangeMode) {
      toast("此功能仅在摆谱模式下可用");
      return;
    }
    await gameManager.exchangePieces();

    boardPiecesView.reset(await gameManager.getDisplayBoard(), _arrangeModePlayerToMove.value);
    toast("红黑棋子已交换");

    // 如果设置了交换红黑时翻转棋盘，则执行翻转
    if (_flipBoardOnExchange.value) {
      flipBoard(needToast: false);
    }

    clearMouseHover(); // 清除鼠标悬停状态，因为坐标的视觉意义可能已改变
  }

  // 回到历史棋谱中的某一步之后的状态
  Future<void> goBackToBoardAfterIndex(int moveIndex) async {
    _isNavigatingHistory = true; // 标记正在导航历史
    boardPiecesView.goBackToBoardAfterIndex(moveIndex); // UI棋盘回到指定状态

    // 如果有引擎正在运行，卸载它们
    unloadEngine(Player.red);
    unloadEngine(Player.black);
    toast('已自动卸载电脑引擎');

    // 更新Rust后端数据
    await _updateBackBoardData();
    _currentPlayer.value = boardPiecesView.currentPlayer; // 更新UI当前玩家
    gameManager.currentPlayer = _currentPlayer.value; // 更新Rust后端当前玩家

    // 设置当前玩家的计时器
    if (_currentPlayer.value == Player.red) {
      _redTimeController.value.runTimer();
      _blackTimeController.value.pauseTimer();
    } else {
      _blackTimeController.value.runTimer();
      _redTimeController.value.pauseTimer();
    }

    // TODO: 设置moveIndex的移动标记 (例如高亮显示棋谱中的对应项)

    gameStarted = true; // 标记游戏仍在进行 (或从历史点继续)

    // 提示用户
    if (moveIndex == -1) {
      toast('回到了初始局面');
    } else if (moveIndex < moveHistory.length) {
      toast('回到了${_currentPlayer.value.opposite().getName()}下“${moveHistory[moveIndex].chinese}”后的局面');
    } else {
      // 如果goBackToBoardAfterIndex有正确的边界检查，则不应发生这种情况
      toast('回到了一个历史局面');
    }
    _isNavigatingHistory = false; // 清除历史导航标记
  }

  /// 切换摆谱模式
  void toggleArrangeMode() async {
    _isArrangeMode.value = !_isArrangeMode.value; // 切换摆谱模式状态

    if (_isArrangeMode.value) {
      // --- 进入摆谱模式 ---
      // 当进入摆谱模式时，停止双方引擎
      if (isEngineLoaded(Player.red)) {
        unloadEngine(Player.red);
        debugPrint("进入摆谱模式：已卸载红方引擎。");
      }
      if (isEngineLoaded(Player.black)) {
        unloadEngine(Player.black);
        debugPrint("进入摆谱模式：已卸载黑方引擎。");
      }

      // 设置摆谱模式下的执棋方
      if (gameStarted) {
        // 如果游戏已开始，则摆谱模式的执棋方与当前游戏执棋方一致
        _arrangeModePlayerToMove.value = gameManager.currentPlayer;
        _redTimeController.value.pauseTimer(); // 暂停双方计时器
        _blackTimeController.value.pauseTimer();
        debugPrint("游戏进行中，进入摆谱模式，计时器已暂停。");
      } else {
        // 如果游戏未开始，则摆谱模式的执棋方默认为红方
        _arrangeModePlayerToMove.value = Player.red;
        debugPrint("游戏未开始，进入摆谱模式，执棋方默认为红方。");
      }
      // 棋盘状态保持进入前的状态
      toast('已进入摆谱模式');
    } else {
      // --- 退出摆谱模式 (完成摆谱) ---
      // 1. 将当前UI棋盘状态同步到 gameManager.board
      await _updateBackBoardData(); // 将 board.pieces 同步到 Rust gameManager

      // 2. 校验棋盘上是否存在双方将帅
      final bool isValidSetup = gameManager.isValidSetupForGameStart();
      if (!isValidSetup) {
        toast('摆谱无效，棋盘上必须有双方将帅！');
        _isArrangeMode.value = true; // 保持在摆谱模式
        debugPrint("退出摆谱模式失败：缺少双方将帅。");
        return; // 阻止退出
      }

      // 3. 全面校验棋盘布局合规性 (例如兵线、子力数量等)
      try {
        gameManager.validateFullBoardLayout(); // Rust同步调用，可能抛出异常
      } catch (e) {
        toast('摆谱无效：${e.toString()}'); // 显示Rust返回的错误信息
        _isArrangeMode.value = true; // 保持在摆谱模式
        debugPrint("退出摆谱模式失败：棋盘布局不合规 - ${e.toString()}");
        return; // 阻止退出
      }

      // 4. 设置轮到走棋的一方 (使用摆谱模式下设定的执棋方)
      gameManager.currentPlayer = _arrangeModePlayerToMove.value;
      _currentPlayer.value = _arrangeModePlayerToMove.value;

      // 5. 重置 BoardPieces 实例以使用更新后的 gameManager 状态。
      // boardPiecesView.reset() 方法内部会清空 moveHistory。
      boardPiecesView.reset(await gameManager.getDisplayBoard(), gameManager.currentPlayer);

      // 6. 处理计时器和游戏状态
      if (gameStarted) {
        // 如果游戏之前在进行中，现在相当于一个新的局面开始
        _stopTimer(); // 停止旧计时
        _redTimeController.value.resetTimer(); // 按新局面重置并启动计时
        _blackTimeController.value.resetTimer();
        if (_currentPlayer.value == Player.red) {
          _redTimeController.value.runTimer();
        } else if (_currentPlayer.value == Player.black) {
          _blackTimeController.value.runTimer();
        }
        debugPrint("退出摆谱模式，棋谱已清空，新局面开始，计时器已重置。");
      } else {
        // 如果游戏之前未开始，摆谱完成后视为游戏开始
        gameStarted = true;
        _resetTimer(); // _resetTimer 会停止、重置并启动红方计时器
        debugPrint("退出摆谱模式，棋谱已清空，游戏开始。");
      }
      toast('已退出摆谱模式，棋谱已清空');
    }
  }

  /// 在摆谱模式下选择要放置的棋子 (来自面板或棋盘)
  /// [fromBoard] 指示棋子是否从棋盘上拾起，如果是，则需要提供 [boardRow] 和 [boardCol]
  void selectPieceForArrangement(SidePieceType? pieceType, {bool fromBoard = false, int? boardRow, int? boardCol}) {
    // 如果从面板点击已选中的相同棋子，则取消选择
    if (_selectedPieceForArrangement.value == pieceType &&
        !fromBoard &&
        _sourcePositionForArrangementMove.value == null) {
      _selectedPieceForArrangement.value = null;
      _sourcePositionForArrangementMove.value = null;
      pieceFollowingMousePosition.value = null; // 清除棋子跟随
    } else {
      _selectedPieceForArrangement.value = pieceType; // 设置当前选中的棋子

      if (fromBoard && pieceType != null && boardRow != null && boardCol != null) {
        // 从棋盘拾起棋子，记录源位置
        _sourcePositionForArrangementMove.value = (boardRow, boardCol);
      } else {
        // 从面板选择棋子，或取消选择 (pieceType is null)，清除源位置标记
        _sourcePositionForArrangementMove.value = null;
      }

      if (pieceType != null) {
        // 如果有棋子被选中
        // 如果当前鼠标在棋盘上，则棋子跟随鼠标
        if (hoverPosition != null) {
          pieceFollowingMousePosition.value = hoverPosition;
        } else {
          // 如果鼠标不在棋盘上，则不显示跟随
          pieceFollowingMousePosition.value = null;
        }
      } else {
        // pieceType 为 null (取消选择)
        pieceFollowingMousePosition.value = null; // 清除棋子跟随
      }
    }
  }

  /// 清空棋盘 (仅摆谱模式下使用)
  void clearBoardForArranging() async {
    if (!isArrangeMode) return; // 确保在摆谱模式
    gameManager.clearBoard(); // 调用Rust GameManager的clear_board方法 (同步)
    debugPrint("摆谱模式：已调用Rust gameManager.clearBoard()");

    // 使用清空后的 gameManager.board 来重置UI board
    boardPiecesView.reset(await gameManager.getDisplayBoard(), gameManager.currentPlayer);

    _currentPlayer.value = gameManager.currentPlayer; // 同步HomeController中的currentPlayer状态

    toast("棋盘已清空");
  }

  /// 恢复初始棋局 (仅摆谱模式下使用)
  void resetBoardToInitialForArranging() async {
    if (!isArrangeMode) return; // 确保在摆谱模式
    gameManager.reset(); // 使用GameManager重置逻辑棋盘到初始状态
    boardPiecesView.reset(await gameManager.getDisplayBoard(), gameManager.currentPlayer); // 使用BoardPieces重置UI棋盘
    _currentPlayer.value = gameManager.currentPlayer; // 同步当前玩家
    toast("棋盘已恢复初始状态");
  }

  /// 切换摆谱模式下的执棋方
  void toggleArrangeModePlayerToMove() {
    if (!isArrangeMode) return; // 仅在摆谱模式下生效
    _arrangeModePlayerToMove.value = _arrangeModePlayerToMove.value.opposite();
    toast("摆谱后轮到：${_arrangeModePlayerToMove.value.getName()}");
  }

  // 处理右键移除棋子的逻辑 (仅摆谱模式)
  Future<void> handleRightClickRemove(Offset localPosition) async {
    if (!isArrangeMode) return; // 仅在摆谱模式下生效

    // 需求1: 右键点击时，无论结果如何，都取消当前选中的待放置棋子
    if (selectedPieceForArrangement != null) {
      selectPieceForArrangement(null);
      debugPrint("摆谱模式：右键点击，已取消选中的棋子。");
    }

    final MaskedPiece? clickedPieceData = getChessPosFromOffset(localPosition); // 获取点击位置的棋子

    if (clickedPieceData != null && clickedPieceData.pieceType() != SidePieceType.none) {
      // 点击位置有棋子
      final originalPieceType = clickedPieceData.pieceType();

      // 禁止在摆谱模式下直接移除最后一个将/帅
      final unsidePiece = getUnsidePieceBySidePiece(sidePiece: originalPieceType);
      if (unsidePiece == PieceType.king) {
        final playerOfKing = originalPieceType.getSide();
        final kingCount = gameManager.countPlayerKings(player: playerOfKing); // 统计该方将/帅数量
        if (kingCount <= 1) {
          toast("不能移除最后一个将/帅！");
          debugPrint("摆谱模式：尝试右键移除最后一个将/帅，操作被禁止。");
          return;
        }
        // 如果多于一个王，则允许移除（后续还会进行照面检查）
      }

      final originalRow = clickedPieceData.row;
      final originalCol = clickedPieceData.col;
      final originalUiIndex = clickedPieceData.index;

      // 1. 先在Rust端模拟移除 (将其设为None)
      // originalRow, originalCol 来自 MaskedPiece，已经是视觉坐标。
      // gameManager.updateBoard 现在期望视觉坐标。
      final nonePieceIndex = SidePieceType.none.getSidePieceTypeIndex();
      await gameManager.updateBoard(
          visualRow: originalRow, // 直接传递视觉坐标
          visualCol: originalCol, // 直接传递视觉坐标
          pieceIndex: nonePieceIndex);
      debugPrint("摆谱模式：(模拟)右键移除 r${originalRow}c$originalCol 的棋子 ${originalPieceType.name}，同步到Rust端。");

      // 2. 检查是否会导致将帅照面
      final bool kingsAreNowFacing = gameManager.areKingsFacing();

      if (kingsAreNowFacing) {
        // 3a. 如果照面，则撤销Rust端的移除 (放回原棋子)
        await gameManager.updateBoard(
            visualRow: originalRow, // 直接传递视觉坐标
            visualCol: originalCol, // 直接传递视觉坐标
            pieceIndex: originalPieceType.getSidePieceTypeIndex());
        toast("不能移除此子，会导致将帅照面！");
        debugPrint("摆谱模式：移除 r${originalRow}c$originalCol 的 ${originalPieceType.name} 会导致将帅照面，操作已撤销。");
        // UI棋盘不更新，棋子仍在原位
      } else {
        // 3b. 如果不照面，则确认UI棋盘的移除
        boardPiecesView.updatePiece(originalUiIndex, SidePieceType.none, MaskedType.none);
        toast("棋子 ${originalPieceType.name} 已移除");
        debugPrint("摆谱模式：成功右键移除 r${originalRow}c$originalCol 的 ${originalPieceType.name}。");
      }
    } else {
      // 点击位置无棋子，或已经是空棋子
      debugPrint("摆谱模式：右键点击了空位置或无效位置，无操作。");
    }
  }

  // 设置更新后的提示
  void settingsUpdatedToast() {
    toast("设置已更新");
  }
}
