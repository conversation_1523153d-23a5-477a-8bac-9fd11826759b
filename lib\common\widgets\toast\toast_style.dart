/*
 * @Author: 老董
 * @Date: 2025-05-17 16:24:36
 * @Description: Do not edit
 * @LastEditors: 老董
 * @LastEditTime: 2025-05-17 16:53:03
 */
import 'package:flutter/material.dart';

/// 展示如何使用自定义动画弹出覆盖层的示例。
class CustomAnimationToast extends StatelessWidget {
  final double value;
  final String message;

  static final Tween<Offset> tweenOffset = Tween<Offset>(begin: const Offset(0, 40), end: const Offset(0, 0));

  static final Tween<double> tweenOpacity = Tween<double>(begin: 0, end: 1);

  const CustomAnimationToast({super.key, required this.value, required this.message});

  @override
  Widget build(BuildContext context) {
    return Transform.translate(
      offset: tweenOffset.transform(value),
      child: Opacity(
        opacity: tweenOpacity.transform(value),
        child: IosStyleToast(message),
      ),
    );
  }
}

class IosStyleToast extends StatelessWidget {
  final String message; // Made final

  const IosStyleToast(this.message, {super.key}); // Added const

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: DefaultTextStyle(
        style: Theme.of(context).textTheme.bodyMedium!.copyWith(color: Colors.white),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Center(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(10),
              child: Container(
                color: Colors.black87,
                padding: const EdgeInsets.symmetric(
                  vertical: 8,
                  horizontal: 16,
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [Text(message)],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
