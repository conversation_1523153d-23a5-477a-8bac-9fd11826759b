import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:xly/xly.dart';

import '../lib/controllers/gobang_controller.dart';
import '../lib/widgets/gobang_board.dart';

void main() {
  group('棋盘点击检测测试', () {
    late GobangController controller;

    setUp(() {
      Get.testMode = true;
      controller = GobangController();
      Get.put(controller);
    });

    tearDown(() {
      Get.delete<GobangController>();
      Get.reset();
    });

    testWidgets('基于用户精确坐标数据的点击检测测试', (WidgetTester tester) async {
      // 构建测试应用
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(1840, 1378), // 使用用户提供的窗口大小
          minTextAdapt: true,
          splitScreenMode: true,
          builder: (context, child) {
            return MaterialApp(
              home: Scaffold(
                body: Center(
                  child: SizedB<PERSON>(
                    width: 800, // 模拟棋盘区域大小
                    height: 800,
                    child: const GobangBoard(),
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // 获取棋盘widget的位置和大小
      final boardFinder = find.byType(GobangBoard);
      expect(boardFinder, findsOneWidget);

      final RenderBox boardBox = tester.renderObject(boardFinder);
      final boardSize = boardBox.size;

      debugPrint('=== 点击检测测试开始 ===');
      debugPrint('棋盘尺寸: ${boardSize.width} x ${boardSize.height}');

      // 计算棋盘参数（与实际代码保持一致）
      final boardDimension = boardSize.width < boardSize.height ? boardSize.width : boardSize.height;
      final pieceRadius = boardDimension / (15 - 1) * 0.4; // 15x15棋盘
      final margin = pieceRadius;
      final effectiveBoardSize = boardDimension - 2 * margin;
      final cellSize = effectiveBoardSize / (15 - 1);

      debugPrint('棋盘维度: ${boardDimension.toStringAsFixed(1)}');
      debugPrint('边距: ${margin.toStringAsFixed(1)}');
      debugPrint('格子大小: ${cellSize.toStringAsFixed(1)}');

      // 测试用例：基于用户提供的精确坐标数据
      final testCases = [
        // 测试左上角第一个交叉点 (0,0)
        {
          'name': '左上角第一个交叉点 (0,0)',
          'clickOffset': Offset(margin, margin),
          'expectedRow': 0,
          'expectedCol': 0,
        },
        // 测试中心天元位置 (7,7)
        {
          'name': '中心天元位置 (7,7)',
          'clickOffset': Offset(margin + 7 * cellSize, margin + 7 * cellSize),
          'expectedRow': 7,
          'expectedCol': 7,
        },
        // 测试右下角 (14,14)
        {
          'name': '右下角 (14,14)',
          'clickOffset': Offset(margin + 14 * cellSize, margin + 14 * cellSize),
          'expectedRow': 14,
          'expectedCol': 14,
        },
        // 测试边界附近的点击
        {
          'name': '第一行第五列 (0,4)',
          'clickOffset': Offset(margin + 4 * cellSize, margin),
          'expectedRow': 0,
          'expectedCol': 4,
        },
        // 测试稍微偏移的点击（模拟真实用户点击）
        {
          'name': '稍微偏移的中心点击',
          'clickOffset': Offset(margin + 7 * cellSize + 5, margin + 7 * cellSize - 3),
          'expectedRow': 7,
          'expectedCol': 7,
        },
      ];

      // 执行测试用例
      for (final testCase in testCases) {
        debugPrint('\n--- 测试: ${testCase['name']} ---');

        final clickOffset = testCase['clickOffset'] as Offset;
        final expectedRow = testCase['expectedRow'] as int;
        final expectedCol = testCase['expectedCol'] as int;

        debugPrint('点击坐标: (${clickOffset.dx.toStringAsFixed(1)}, ${clickOffset.dy.toStringAsFixed(1)})');
        debugPrint('期望网格: ($expectedRow, $expectedCol)');

        // 重置棋盘
        await controller.resetGame();
        await tester.pumpAndSettle();

        // 模拟点击
        await tester.tapAt(tester.getTopLeft(boardFinder) + clickOffset);
        await tester.pumpAndSettle();

        // 验证结果
        final actualPiece = controller.board[expectedRow][expectedCol];
        expect(
          actualPiece,
          1, // 应该是黑子（第一步）
          reason: '测试用例 "${testCase['name']}" 失败：期望在 ($expectedRow, $expectedCol) 放置棋子，但实际没有棋子'
        );

        debugPrint('✓ 测试通过：在 ($expectedRow, $expectedCol) 成功放置棋子');
      }

      debugPrint('\n=== 所有点击检测测试通过 ===');
    });

    testWidgets('边界和无效区域点击测试', (WidgetTester tester) async {
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(1840, 1378),
          minTextAdapt: true,
          splitScreenMode: true,
          builder: (context, child) {
            return MaterialApp(
              home: Scaffold(
                body: Center(
                  child: SizedBox(
                    width: 800,
                    height: 800,
                    child: const GobangBoard(),
                  ),
                ),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      final boardFinder = find.byType(GobangBoard);
      final RenderBox boardBox = tester.renderObject(boardFinder);
      final boardSize = boardBox.size;

      // 计算边界
      final boardDimension = boardSize.width < boardSize.height ? boardSize.width : boardSize.height;
      final pieceRadius = boardDimension / (15 - 1) * 0.4;
      final margin = pieceRadius;

      debugPrint('\n=== 边界测试开始 ===');

      // 测试无效区域的点击
      final invalidTestCases = [
        {
          'name': '左边界外',
          'clickOffset': Offset(margin - 50, margin + 100),
        },
        {
          'name': '右边界外',
          'clickOffset': Offset(boardDimension - margin + 50, margin + 100),
        },
        {
          'name': '上边界外',
          'clickOffset': Offset(margin + 100, margin - 50),
        },
        {
          'name': '下边界外',
          'clickOffset': Offset(margin + 100, boardDimension - margin + 50),
        },
      ];

      for (final testCase in invalidTestCases) {
        debugPrint('\n--- 测试无效区域: ${testCase['name']} ---');

        final clickOffset = testCase['clickOffset'] as Offset;

        // 记录点击前的棋子数量
        int pieceCountBefore = 0;
        for (int i = 0; i < 15; i++) {
          for (int j = 0; j < 15; j++) {
            if (controller.board[i][j] != 0) pieceCountBefore++;
          }
        }

        // 点击无效区域
        await tester.tapAt(tester.getTopLeft(boardFinder) + clickOffset);
        await tester.pumpAndSettle();

        // 检查棋子数量是否没有变化
        int pieceCountAfter = 0;
        for (int i = 0; i < 15; i++) {
          for (int j = 0; j < 15; j++) {
            if (controller.board[i][j] != 0) pieceCountAfter++;
          }
        }

        expect(
          pieceCountAfter,
          pieceCountBefore,
          reason: '无效区域点击测试失败：点击 "${testCase['name']}" 不应该放置棋子'
        );

        debugPrint('✓ 无效区域测试通过：${testCase['name']}');
      }

      debugPrint('\n=== 边界测试完成 ===');
    });

    test('坐标转换算法单元测试', () {
      debugPrint('\n=== 坐标转换算法单元测试 ===');

      // 模拟棋盘参数
      const boardSize = 15;
      const boardDimension = 800.0;
      const pieceRadius = boardDimension / (boardSize - 1) * 0.4;
      const margin = pieceRadius;
      const effectiveBoardSize = boardDimension - 2 * margin;
      const cellSize = effectiveBoardSize / (boardSize - 1);

      debugPrint('测试参数:');
      debugPrint('- 棋盘维度: $boardDimension');
      debugPrint('- 边距: $margin');
      debugPrint('- 格子大小: $cellSize');

      // 测试精确坐标转换
      final testPoints = [
        {'point': Offset(margin, margin), 'expected': [0, 0]},
        {'point': Offset(margin + cellSize, margin), 'expected': [0, 1]},
        {'point': Offset(margin, margin + cellSize), 'expected': [1, 0]},
        {'point': Offset(margin + 7 * cellSize, margin + 7 * cellSize), 'expected': [7, 7]},
        {'point': Offset(margin + 14 * cellSize, margin + 14 * cellSize), 'expected': [14, 14]},
      ];

      for (final test in testPoints) {
        final point = test['point'] as Offset;
        final expected = test['expected'] as List<int>;

        // 模拟坐标转换逻辑
        final adjustedX = point.dx - margin;
        final adjustedY = point.dy - margin;

        final rawCol = adjustedX / cellSize;
        final rawRow = adjustedY / cellSize;

        final col = rawCol.round().clamp(0, boardSize - 1);
        final row = rawRow.round().clamp(0, boardSize - 1);

        debugPrint('点击 (${point.dx.toStringAsFixed(1)}, ${point.dy.toStringAsFixed(1)}) -> 网格 ($row, $col)');

        expect(row, expected[0], reason: '行坐标转换错误');
        expect(col, expected[1], reason: '列坐标转换错误');
      }

      debugPrint('✓ 坐标转换算法测试通过');
    });
  });
}