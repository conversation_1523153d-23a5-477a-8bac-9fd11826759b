import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:test_gobang11/main.dart';
import 'package:test_gobang11/rust/frb_generated.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  setUpAll(() async => await RustLib.init());
  testWidgets('Can launch gobang app', (WidgetTester tester) async {
    await tester.pumpWidget(const GobangApp());
    expect(find.text('五子棋'), findsOneWidget);
  });
}
