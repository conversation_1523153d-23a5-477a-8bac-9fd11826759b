/*
 * <AUTHOR> 老董
 * @Date         : 2022-04-29 10:27:31
 * @LastEditors: 老董
 * @LastEditTime: 2025-05-17 16:56:44
 * @Description  : 基于GetX的路由定义
 */
import '../../pages/home/<USER>';
import '../../pages/train/lib.dart';

abstract class Routes {
  static const home = '/home';
  static const train = '/train';
}

abstract class AppPages {
  static final pages = [
    // 主页
    GetPage(
      name: Routes.home,
      page: () => const HomeView(),
      binding: BindingsBuilder(
        () {
          Get.lazyPut<HomeController>(() => HomeController());
        },
      ),
    ),
    // 训练页
    GetPage(
      name: Routes.train,
      page: () => const TrainView(),
      binding: BindingsBuilder(
        () {
          Get.lazyPut<TrainController>(() => TrainController());
        },
      ),
    ),
  ];
}
