import 'package:flutter/material.dart';
import 'package:xly/xly.dart';

void main() {
  runApp(const TestLayoutApp());
}

class TestLayoutApp extends StatelessWidget {
  const TestLayoutApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(1400, 900), // 调整后的设计尺寸
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return MaterialApp(
          title: '布局测试',
          theme: ThemeData(primarySwatch: Colors.brown, useMaterial3: true),
          home: const TestLayoutPage(),
          debugShowCheckedModeBanner: false,
        );
      },
    );
  }
}

class TestLayoutPage extends StatelessWidget {
  const TestLayoutPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5DC), // 米色背景
      appBar: AppBar(
        title: const Text('布局测试'),
        backgroundColor: const Color(0xFF8B4513),
        centerTitle: true,
      ),
      body: SafeArea(
        child: LayoutBuilder(
          builder: (context, constraints) {
            // 计算可用空间，考虑padding
            final availableWidth = constraints.maxWidth - 32.w; // 左右padding
            final availableHeight = constraints.maxHeight - 32.h; // 上下padding

            // 计算棋盘区域的最大尺寸（4:1布局中的4部分）
            final boardAreaWidth = (availableWidth - 16.w) * 4 / 5; // 减去中间间距

            // 棋盘应该是正方形，取宽度和高度的最小值
            final maxBoardSize = [
              boardAreaWidth,
              availableHeight,
            ].reduce((a, b) => a < b ? a : b);

            return Padding(
              padding: EdgeInsets.all(16.w),
              child: Column(
                children: [
                  // 显示计算信息
                  Container(
                    padding: EdgeInsets.all(8.w),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8.r),
                      border: Border.all(color: const Color(0xFF8B4513)),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '屏幕约束: ${constraints.maxWidth.toStringAsFixed(1)} x ${constraints.maxHeight.toStringAsFixed(1)}',
                        ),
                        Text(
                          '可用空间: ${availableWidth.toStringAsFixed(1)} x ${availableHeight.toStringAsFixed(1)}',
                        ),
                        Text('棋盘区域宽度: ${boardAreaWidth.toStringAsFixed(1)}'),
                        Text(
                          '最终棋盘尺寸: ${maxBoardSize.toStringAsFixed(1)} x ${maxBoardSize.toStringAsFixed(1)}',
                        ),
                      ],
                    ),
                  ),

                  SizedBox(height: 16.h),

                  // 主要布局
                  Expanded(
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 左侧棋盘区域 (4/5)
                        Expanded(
                          flex: 4,
                          child: Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8.r),
                            ),
                            child: Center(
                              child: SizedBox(
                                width: maxBoardSize,
                                height: maxBoardSize,
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: const Color(0xFFD2B48C), // 浅棕色木质感
                                    borderRadius: BorderRadius.circular(8.r),
                                    border: Border.all(
                                      color: const Color(0xFF8B4513),
                                      width: 2,
                                    ),
                                  ),
                                  child: Padding(
                                    padding: EdgeInsets.all(16.w),
                                    child: CustomPaint(
                                      painter: TestBoardPainter(),
                                      child: const SizedBox.expand(),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),

                        SizedBox(width: 16.w),

                        // 右侧信息面板区域 (1/5)
                        Expanded(
                          flex: 1,
                          child: Container(
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(8.r),
                              border: Border.all(
                                color: const Color(0xFF8B4513),
                              ),
                            ),
                            padding: EdgeInsets.all(16.w),
                            child: const Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  '信息面板',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                SizedBox(height: 8),
                                Text('这里是右侧信息面板区域'),
                                Text('占据1/5的宽度'),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}

/// 测试棋盘绘制器
class TestBoardPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color =
          const Color(0xFF8B4513) // 深棕色线条
      ..strokeWidth = 1.5
      ..style = PaintingStyle.stroke;

    const boardSize = 15;

    // 确保棋盘是正方形，取宽度和高度的最小值
    final boardDimension = size.width < size.height ? size.width : size.height;
    final cellSize = boardDimension / (boardSize - 1);

    // 绘制网格线
    for (int i = 0; i < boardSize; i++) {
      // 水平线
      canvas.drawLine(
        Offset(0, i * cellSize),
        Offset(boardDimension, i * cellSize),
        paint,
      );

      // 垂直线
      canvas.drawLine(
        Offset(i * cellSize, 0),
        Offset(i * cellSize, boardDimension),
        paint,
      );
    }

    // 绘制天元和星位
    final starPaint = Paint()
      ..color = const Color(0xFF8B4513)
      ..style = PaintingStyle.fill;

    // 天元 (中心点)
    final center = boardSize ~/ 2;
    canvas.drawCircle(
      Offset(center * cellSize, center * cellSize),
      3.0,
      starPaint,
    );

    // 四个星位
    final starPositions = [3, 11]; // 对应15x15棋盘的星位
    for (final row in starPositions) {
      for (final col in starPositions) {
        canvas.drawCircle(
          Offset(col * cellSize, row * cellSize),
          2.0,
          starPaint,
        );
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}
