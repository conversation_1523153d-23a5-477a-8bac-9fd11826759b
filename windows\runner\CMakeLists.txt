cmake_minimum_required(VERSION 3.14)
project(runner LANGUAGES CXX)

# 确保RUNNER_BINARY_NAME已定义
if(NOT DEFINED RUNNER_BINARY_NAME)
  set(RUNNER_BINARY_NAME ${BINARY_NAME})
endif()

# Define the application target. To change its name, change BINARY_NAME in the
# top-level CMakeLists.txt, not the value here, or `flutter run` will no longer
# work.
#
# Any new source files that you add to the application should be added here.
add_executable(${RUNNER_BINARY_NAME} WIN32
  "flutter_window.cpp"
  "main.cpp"
  "utils.cpp"
  "win32_window.cpp"
  "${FLUTTER_MANAGED_DIR}/generated_plugin_registrant.cc"
  "Runner.rc"  # 确保这行存在
  "runner.exe.manifest"
)

# Apply the standard set of build settings. This can be removed for applications
# that need different build settings.
apply_standard_settings(${RUNNER_BINARY_NAME})

# Add preprocessor definitions for the build version.
target_compile_definitions(${RUNNER_BINARY_NAME} PRIVATE "FLUTTER_VERSION=\"${FLUTTER_VERSION}\"")
target_compile_definitions(${RUNNER_BINARY_NAME} PRIVATE "FLUTTER_VERSION_MAJOR=${FLUTTER_VERSION_MAJOR}")
target_compile_definitions(${RUNNER_BINARY_NAME} PRIVATE "FLUTTER_VERSION_MINOR=${FLUTTER_VERSION_MINOR}")
target_compile_definitions(${RUNNER_BINARY_NAME} PRIVATE "FLUTTER_VERSION_PATCH=${FLUTTER_VERSION_PATCH}")
target_compile_definitions(${RUNNER_BINARY_NAME} PRIVATE "FLUTTER_VERSION_BUILD=${FLUTTER_VERSION_BUILD}")

# Disable Windows macros that collide with C++ standard library functions.
target_compile_definitions(${RUNNER_BINARY_NAME} PRIVATE "NOMINMAX")

# Add dependency libraries and include directories. Add any application-specific
# dependencies here.
target_link_libraries(${RUNNER_BINARY_NAME} PRIVATE flutter flutter_wrapper_app)
target_link_libraries(${RUNNER_BINARY_NAME} PRIVATE "dwmapi.lib")
target_include_directories(${RUNNER_BINARY_NAME} PRIVATE "${CMAKE_SOURCE_DIR}")

# Run the Flutter tool portions of the build. This must not be removed.
add_dependencies(${RUNNER_BINARY_NAME} flutter_assemble)
