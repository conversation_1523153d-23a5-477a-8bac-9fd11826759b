// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

// ignore_for_file: unused_import, unused_element, unnecessary_import, duplicate_ignore, invalid_use_of_internal_member, annotate_overrides, non_constant_identifier_names, curly_braces_in_flow_control_structures, prefer_const_literals_to_create_immutables, unused_field

// Static analysis wrongly picks the IO variant, thus ignore this
// ignore_for_file: argument_type_not_assignable

import 'api/ucci_api.dart';
import 'chess/board_utils.dart';
import 'chess/game_manager.dart';
import 'chess/move_utils.dart';
import 'chess/others.dart';
import 'chess/piece_utils.dart';
import 'dart:async';
import 'dart:convert';
import 'frb_generated.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated_web.dart';

abstract class RustLibApiImplPlatform extends BaseApiImpl<RustLibWire> {
  RustLibApiImplPlatform({
    required super.handler,
    required super.wire,
    required super.generalizedFrbRustBinding,
    required super.portManager,
  });

  CrossPlatformFinalizerArg
      get rust_arc_decrement_strong_count_GameManagerPtr => wire
          .rust_arc_decrement_strong_count_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager;

  @protected
  AnyhowException dco_decode_AnyhowException(dynamic raw);

  @protected
  GameManager
      dco_decode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
          dynamic raw);

  @protected
  GameManager
      dco_decode_Auto_RefMut_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
          dynamic raw);

  @protected
  GameManager
      dco_decode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
          dynamic raw);

  @protected
  GameManager
      dco_decode_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
          dynamic raw);

  @protected
  RustStreamSink<String> dco_decode_StreamSink_String_Sse(dynamic raw);

  @protected
  String dco_decode_String(dynamic raw);

  @protected
  bool dco_decode_bool(dynamic raw);

  @protected
  PieceMove dco_decode_box_autoadd_piece_move(dynamic raw);

  @protected
  int dco_decode_box_autoadd_u_8(dynamic raw);

  @protected
  int dco_decode_i_32(dynamic raw);

  @protected
  List<int> dco_decode_list_prim_u_8_loose(dynamic raw);

  @protected
  Uint8List dco_decode_list_prim_u_8_strict(dynamic raw);

  @protected
  String? dco_decode_opt_String(dynamic raw);

  @protected
  PieceMove? dco_decode_opt_box_autoadd_piece_move(dynamic raw);

  @protected
  int? dco_decode_opt_box_autoadd_u_8(dynamic raw);

  @protected
  PieceMove dco_decode_piece_move(dynamic raw);

  @protected
  PieceType dco_decode_piece_type(dynamic raw);

  @protected
  PlacementValidity dco_decode_placement_validity(dynamic raw);

  @protected
  Player dco_decode_player(dynamic raw);

  @protected
  (U8Array256, Player) dco_decode_record_u_8_array_256_player(dynamic raw);

  @protected
  (int, int, int, int) dco_decode_record_u_8_u_8_u_8_u_8(dynamic raw);

  @protected
  SidePieceType dco_decode_side_piece_type(dynamic raw);

  @protected
  int dco_decode_u_32(dynamic raw);

  @protected
  int dco_decode_u_8(dynamic raw);

  @protected
  U8Array256 dco_decode_u_8_array_256(dynamic raw);

  @protected
  U8Array90 dco_decode_u_8_array_90(dynamic raw);

  @protected
  void dco_decode_unit(dynamic raw);

  @protected
  BigInt dco_decode_usize(dynamic raw);

  @protected
  AnyhowException sse_decode_AnyhowException(SseDeserializer deserializer);

  @protected
  GameManager
      sse_decode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
          SseDeserializer deserializer);

  @protected
  GameManager
      sse_decode_Auto_RefMut_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
          SseDeserializer deserializer);

  @protected
  GameManager
      sse_decode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
          SseDeserializer deserializer);

  @protected
  GameManager
      sse_decode_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
          SseDeserializer deserializer);

  @protected
  RustStreamSink<String> sse_decode_StreamSink_String_Sse(
      SseDeserializer deserializer);

  @protected
  String sse_decode_String(SseDeserializer deserializer);

  @protected
  bool sse_decode_bool(SseDeserializer deserializer);

  @protected
  PieceMove sse_decode_box_autoadd_piece_move(SseDeserializer deserializer);

  @protected
  int sse_decode_box_autoadd_u_8(SseDeserializer deserializer);

  @protected
  int sse_decode_i_32(SseDeserializer deserializer);

  @protected
  List<int> sse_decode_list_prim_u_8_loose(SseDeserializer deserializer);

  @protected
  Uint8List sse_decode_list_prim_u_8_strict(SseDeserializer deserializer);

  @protected
  String? sse_decode_opt_String(SseDeserializer deserializer);

  @protected
  PieceMove? sse_decode_opt_box_autoadd_piece_move(
      SseDeserializer deserializer);

  @protected
  int? sse_decode_opt_box_autoadd_u_8(SseDeserializer deserializer);

  @protected
  PieceMove sse_decode_piece_move(SseDeserializer deserializer);

  @protected
  PieceType sse_decode_piece_type(SseDeserializer deserializer);

  @protected
  PlacementValidity sse_decode_placement_validity(SseDeserializer deserializer);

  @protected
  Player sse_decode_player(SseDeserializer deserializer);

  @protected
  (U8Array256, Player) sse_decode_record_u_8_array_256_player(
      SseDeserializer deserializer);

  @protected
  (int, int, int, int) sse_decode_record_u_8_u_8_u_8_u_8(
      SseDeserializer deserializer);

  @protected
  SidePieceType sse_decode_side_piece_type(SseDeserializer deserializer);

  @protected
  int sse_decode_u_32(SseDeserializer deserializer);

  @protected
  int sse_decode_u_8(SseDeserializer deserializer);

  @protected
  U8Array256 sse_decode_u_8_array_256(SseDeserializer deserializer);

  @protected
  U8Array90 sse_decode_u_8_array_90(SseDeserializer deserializer);

  @protected
  void sse_decode_unit(SseDeserializer deserializer);

  @protected
  BigInt sse_decode_usize(SseDeserializer deserializer);

  @protected
  void sse_encode_AnyhowException(
      AnyhowException self, SseSerializer serializer);

  @protected
  void
      sse_encode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
          GameManager self, SseSerializer serializer);

  @protected
  void
      sse_encode_Auto_RefMut_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
          GameManager self, SseSerializer serializer);

  @protected
  void
      sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
          GameManager self, SseSerializer serializer);

  @protected
  void
      sse_encode_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
          GameManager self, SseSerializer serializer);

  @protected
  void sse_encode_StreamSink_String_Sse(
      RustStreamSink<String> self, SseSerializer serializer);

  @protected
  void sse_encode_String(String self, SseSerializer serializer);

  @protected
  void sse_encode_bool(bool self, SseSerializer serializer);

  @protected
  void sse_encode_box_autoadd_piece_move(
      PieceMove self, SseSerializer serializer);

  @protected
  void sse_encode_box_autoadd_u_8(int self, SseSerializer serializer);

  @protected
  void sse_encode_i_32(int self, SseSerializer serializer);

  @protected
  void sse_encode_list_prim_u_8_loose(List<int> self, SseSerializer serializer);

  @protected
  void sse_encode_list_prim_u_8_strict(
      Uint8List self, SseSerializer serializer);

  @protected
  void sse_encode_opt_String(String? self, SseSerializer serializer);

  @protected
  void sse_encode_opt_box_autoadd_piece_move(
      PieceMove? self, SseSerializer serializer);

  @protected
  void sse_encode_opt_box_autoadd_u_8(int? self, SseSerializer serializer);

  @protected
  void sse_encode_piece_move(PieceMove self, SseSerializer serializer);

  @protected
  void sse_encode_piece_type(PieceType self, SseSerializer serializer);

  @protected
  void sse_encode_placement_validity(
      PlacementValidity self, SseSerializer serializer);

  @protected
  void sse_encode_player(Player self, SseSerializer serializer);

  @protected
  void sse_encode_record_u_8_array_256_player(
      (U8Array256, Player) self, SseSerializer serializer);

  @protected
  void sse_encode_record_u_8_u_8_u_8_u_8(
      (int, int, int, int) self, SseSerializer serializer);

  @protected
  void sse_encode_side_piece_type(SidePieceType self, SseSerializer serializer);

  @protected
  void sse_encode_u_32(int self, SseSerializer serializer);

  @protected
  void sse_encode_u_8(int self, SseSerializer serializer);

  @protected
  void sse_encode_u_8_array_256(U8Array256 self, SseSerializer serializer);

  @protected
  void sse_encode_u_8_array_90(U8Array90 self, SseSerializer serializer);

  @protected
  void sse_encode_unit(void self, SseSerializer serializer);

  @protected
  void sse_encode_usize(BigInt self, SseSerializer serializer);
}

// Section: wire_class

class RustLibWire implements BaseWire {
  RustLibWire.fromExternalLibrary(ExternalLibrary lib);

  void rust_arc_increment_strong_count_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
          int ptr) =>
      wasmModule
          .rust_arc_increment_strong_count_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
              ptr);

  void rust_arc_decrement_strong_count_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
          int ptr) =>
      wasmModule
          .rust_arc_decrement_strong_count_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
              ptr);
}

@JS('wasm_bindgen')
external RustLibWasmModule get wasmModule;

@JS()
@anonymous
extension type RustLibWasmModule._(JSObject _) implements JSObject {
  external void
      rust_arc_increment_strong_count_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
          int ptr);

  external void
      rust_arc_decrement_strong_count_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
          int ptr);
}
