use super::SidePieceType;
use num_traits::FromPrimitive;

#[allow(unused)]
pub fn generate_state_picture(board: [u8; 90]) -> String {
    let mut state_picture = String::new();

    for (index, &piece) in board.iter().enumerate() {
        let piece_type = SidePieceType::from_u8(piece).unwrap_or(SidePieceType::None);
        let chinese_name = if piece_type == SidePieceType::None {
            "——"
        } else {
            piece_type.get_chinese_name()
        };
        state_picture.push_str(chinese_name);

        if (index + 1) % 9 == 0 {
            state_picture.push('\n');
        }
    }

    state_picture
}
