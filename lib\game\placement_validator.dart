import 'package:flutter/material.dart'; // 用于 SnackBar 示例
import 'package:meng_ru_ling_shi/common/widgets/toast/toast_message.dart'; // 导入toast函数
import 'package:meng_ru_ling_shi/rust/chess/game_manager.dart'; // 用于 GameManager 和 PlacementValidity
import 'package:meng_ru_ling_shi/rust/chess/piece_utils.dart'; // 用于 SidePieceType
// 确保 RustLib.init() 已经在应用启动时调用过，通常在 main.dart 中。

// 定义回调类型
typedef PlacementSuccessCallback = void Function();
typedef PlacementFailureCallback = void Function(String message);

/// 一个辅助类，用于处理棋子放置时的校验逻辑。
class PlacementValidator {
  final GameManager gameManager;

  PlacementValidator({required this.gameManager});

  /// 处理棋子放置的尝试，并调用Rust端进行校验。
  ///
  /// 参数:
  ///   row: 棋子目标位置的行号 (请确保与Rust端期望的1-10，从上到下坐标系一致)。
  ///   col: 棋子目标位置的列号 (请确保与Rust端期望的1-9，从左到右坐标系一致)。
  ///   pieceToPlace: 要放置的棋子类型 (Dart中的 `SidePieceType` 枚举)。
  ///   onSuccess: 放置成功且校验通过时的回调。
  ///   onFailure: 放置或校验失败时的回调，参数为错误信息。
  ///
  /// 返回:
  ///   一个 bool 值，表示操作是否最终视为有效放置 (true) 或无效 (false)。
  bool handlePiecePlacement({
    required int row, // 此处的 row 和 col 应该已经是转换到Rust固定参考系的坐标
    required int col,
    required SidePieceType pieceToPlace,
    PlacementSuccessCallback? onSuccess,
    PlacementFailureCallback? onFailure,
  }) {
    try {
      // 调用Rust函数进行校验和放置 (这是一个同步方法)
      // 传入的 row 和 col 假定已由调用方 (如 HomeController) 根据棋盘翻转状态转换完毕
      // Rust API place_piece_on_board now expects visualRow and visualCol
      final result = gameManager.placePieceOnBoard(
        visualRow: row,
        visualCol: col,
        pieceToPlace: pieceToPlace,
      );

      // 根据Rust返回的结果处理
      // 注意：这里的 result is PlacementValidity_Valid 等检查，
      // 需要根据 flutter_rust_bridge 生成的具体Dart类名进行调整。
      // 通常是 Rust枚举名_变体名 (e.g., PlacementValidity_Valid)。
      if (result is PlacementValidity_Valid) {
        onSuccess?.call();
        return true;
      } else {
        String errorMessage = "未知的放置错误";
        if (result is PlacementValidity_InvalidLocation) {
          errorMessage = result.field0;
        } else if (result is PlacementValidity_MaxPiecesReached) {
          errorMessage = result.field0;
        } else if (result is PlacementValidity_KingsFacing) {
          errorMessage = result.field0;
        }

        onFailure?.call(errorMessage);
        debugPrint("PlacementValidator: About to call toast() for known error: '$errorMessage'");
        toast(errorMessage); // 在这里添加toast调用
        debugPrint("PlacementValidator: Called toast() for known error successfully.");
        return false;
      }
    } catch (e) {
      // 处理调用过程中可能发生的任何FRB或Rust异常
      String exceptionErrorMessage = "调用棋子放置校验时发生意外错误: $e";
      onFailure?.call(exceptionErrorMessage);
      debugPrint("PlacementValidator (catch): About to call toast() for exception: '$exceptionErrorMessage'");
      toast(exceptionErrorMessage); // 在这里添加toast调用
      debugPrint("PlacementValidator (catch): Called toast() for exception successfully.");
      return false;
    }
  }
}
