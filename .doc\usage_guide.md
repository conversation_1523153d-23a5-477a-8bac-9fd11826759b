# 五子棋游戏使用指南

## 游戏界面

### 主界面布局
- **左侧 (4/5)**: 15x15棋盘区域，木质纹理背景
- **右侧 (1/5)**: 游戏信息面板

### 棋盘特征
- 15x15标准棋盘网格
- 木质纹理背景，深棕色网格线
- 天元和星位标记点
- 可选坐标显示 (A-O, 1-15)

### 棋子样式
- **黑子**: 黑色渐变，立体质感
- **白子**: 白色渐变，立体质感
- 带阴影效果，增强视觉层次

## 游戏操作

### 基本操作
1. **落子**: 点击棋盘空白交叉点
2. **悔棋**: 点击右侧面板"悔棋"按钮
3. **重新开始**: 点击右侧面板"重新开始"按钮

### 游戏流程
1. 黑子先手，白子后手
2. 双方轮流在棋盘交叉点落子
3. 率先连成5子者获胜
4. 游戏结束后可选择重新开始

### 胜利条件
在横、竖、斜任一方向连成5子即可获胜：
- **水平方向**: ●●●●●
- **垂直方向**: 竖直连线5子
- **对角线**: 主对角线或副对角线5子

## 游戏设置

### 显示设置
- **显示坐标**: 开启/关闭棋盘坐标显示
- **动画效果**: 开启/关闭棋子落下动画
- **音效**: 开启/关闭游戏音效 (预留功能)

### 设置调整
在右侧信息面板的"游戏设置"区域，通过开关切换各项设置。

## 游戏信息

### 状态显示
- **游戏状态**: 显示当前游戏进行状态
- **当前玩家**: 显示轮到哪方落子
- **步数统计**: 显示已下棋子数量

### 状态说明
- "轮到黑子" / "轮到白子": 游戏进行中
- "黑子获胜!" / "白子获胜!": 游戏结束
- "平局!": 棋盘下满无胜负 (理论情况)

## 游戏规则

### 无禁手规则
本游戏采用无禁手五子棋规则：
- 黑白双方均无禁手限制
- 任何一方率先连成5子即获胜
- 不存在"三三禁手"、"四四禁手"等限制

### 落子规则
- 只能在空白交叉点落子
- 不能在已有棋子的位置落子
- 不能在棋盘外落子
- 落子后不可移动

### 悔棋规则
- 可以撤销上一步棋
- 悔棋后轮到上一个玩家
- 游戏开始前无法悔棋
- 游戏结束后无法悔棋

## 快捷操作

### 键盘快捷键 (计划功能)
- `Ctrl + Z`: 悔棋
- `Ctrl + N`: 新游戏
- `Ctrl + S`: 保存游戏 (计划功能)
- `Ctrl + L`: 加载游戏 (计划功能)

### 鼠标操作
- **左键点击**: 落子
- **右键点击**: 取消操作 (计划功能)

## 常见问题

### Q: 为什么点击没有反应？
A: 检查是否点击在交叉点上，确保该位置没有棋子。

### Q: 如何判断获胜？
A: 任意方向连成5子即获胜，系统会自动判断并显示结果。

### Q: 可以悔棋几步？
A: 目前只支持悔棋一步，后续版本将支持多步悔棋。

### Q: 游戏记录在哪里？
A: 当前版本暂不支持游戏记录，该功能在开发计划中。

## 技术支持

如遇到问题或有建议，请通过以下方式联系：
- GitHub Issues: [项目地址]
- 邮箱: [联系邮箱]

## 版本信息

当前版本: v1.0.0
更新日期: 2025-06-09
支持平台: Windows, macOS, Linux
