{"rustc": 16591470773350601817, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 7465627196321967167, "profile": 12506755554509207344, "path": 13236182631312062850, "deps": [[5103565458935487, "futures_io", false, 7996507185821471228], [1811549171721445101, "futures_channel", false, 11210955741986152583], [7013762810557009322, "futures_sink", false, 2326873239644073587], [7620660491849607393, "futures_core", false, 15611546190662880035], [10629569228670356391, "futures_util", false, 5518378812936502018], [12779779637805422465, "futures_executor", false, 6007304900664882303], [16240732885093539806, "futures_task", false, 18334226013412152506]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-c218b9181b13e06e\\dep-lib-futures", "checksum": false}}], "rustflags": ["--cfg", "frb_expand"], "config": 2069994364910194474, "compile_kind": 0}