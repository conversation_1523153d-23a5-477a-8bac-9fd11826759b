{"rustc": 16591470773350601817, "features": "[\"alloc\", \"block-buffer\", \"core-api\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"blobby\", \"block-buffer\", \"const-oid\", \"core-api\", \"default\", \"dev\", \"mac\", \"oid\", \"rand_core\", \"std\", \"subtle\"]", "target": 7510122432137863311, "profile": 8285369720897779924, "path": 13658073524040212353, "deps": [[2352660017780662552, "crypto_common", false, 13185609988450797557], [10626340395483396037, "block_buffer", false, 33675415653070453]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\digest-a6d2097a093fb1e8\\dep-lib-digest", "checksum": false}}], "rustflags": ["--cfg", "frb_expand"], "config": 2069994364910194474, "compile_kind": 0}