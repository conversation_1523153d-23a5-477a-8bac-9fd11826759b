// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../frb_generated.dart';
import 'package:collection/collection.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';
import 'package:freezed_annotation/freezed_annotation.dart' hide protected;
import 'piece_utils.dart';
part 'game_manager.freezed.dart';

// These functions are ignored because they are not marked as `pub`: `count_pieces_on_board`, `find_king_position`, `get_board_fen`, `get_piece_all_valid_moves`, `get_piece_offset_tag`, `get_special_pawn_number`, `is_pos_in_home_side`, `is_pos_in_player_home_side`, `is_pos_stuck`, `is_position_under_attack_with_custom_board`, `is_same_side_piece`, `is_the_player_king_being_killed_or_trapped`, `logical_to_visual_coords`, `squre_forward_board`, `static_center_flip_board`, `try_find_king_position`, `visual_to_logical_coords`
// These function are ignored because they are on traits that is not defined in current crate (put an empty `#[frb]` on it to unignore): `assert_receiver_is_total_eq`, `eq`, `fmt`

// Rust type: RustOpaqueMoi<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<GameManager>>
abstract class GameManager implements RustOpaqueInterface {
  /// 检查将帅是否照面
  bool areKingsFacing();

  Player get currentPlayer;

  bool get needFlipForDisplay;

  set currentPlayer(Player currentPlayer);

  set needFlipForDisplay(bool needFlipForDisplay);

  static int boardColCount() =>
      RustLib.instance.api.crateChessGameManagerGameManagerBoardColCount();

  static int boardRowCount() =>
      RustLib.instance.api.crateChessGameManagerGameManagerBoardRowCount();

  void clearBoard();

  int countPlayerKings({required Player player});

  /// 交换棋盘上所有棋子的红黑属性。
  /// 例如，原位置的红车变为黑车，黑马变为红马...
  /// 注意：此方法仅交换棋子颜色，不改变棋盘的翻转状态，不切换当前玩家，但内部board仍需保证黑上红下
  Future<void> exchangePieces();

  /// 获取当前逻辑视角下的棋盘数组（不改变状态）。
  Future<U8Array256> getDisplayBoard();

  Future<String> getDisplayBoardFen();

  static U8Array256 getOrigBoard() =>
      RustLib.instance.api.crateChessGameManagerGameManagerGetOrigBoard();

  Future<PieceMove?> getRandomLegalMove();

  SidePieceType getSidePieceByIndex({required int pos});

  Future<String> iccsMoveToChineseMove({required String iccsMove});

  Future<bool> isCurrentPlayerLosing();

  Future<bool> isCurrentPlayerWinning();

  /// 根据输入的起始终止点位（视觉坐标），基于棋子规则判断是否合法（不考虑将军、困毙等特殊情况）
  Future<bool> isPieceValidMove(
      {required int visualSrcRow,
      required int visualSrcCol,
      required int visualDstRow,
      required int visualDstCol});

  /// 校验给定类型的棋子放置在指定位置（视觉坐标 `visual_row`, `visual_col）是否符合其基本区域规则`。
  /// `visual_row`: 1-10 (从上到下), `visual_col`: 1-9 (从左到右)
  Future<bool> isValidPiecePlacement(
      {required int visualRow,
      required int visualCol,
      required SidePieceType pieceType});

  bool isValidSetupForGameStart();

  void loadFen({required String fenStr});

  /// 根据ICCS坐标执行棋子移动，不进行规则验证，不切换玩家
  ///
  /// # 参数
  ///
  /// * `iccs_move` - ICCS格式的移动字符串，例如 "a0a1"（车九进一）
  ///
  /// # 说明
  ///
  /// 此函数根据ICCS坐标移动棋子，但不会：
  /// - 验证移动是否符合规则
  /// - 检查是否轮到当前玩家行棋
  /// - 自动切换当前玩家
  /// - 检查将军、将死或困毙状态
  ///
  /// 如果需要切换玩家，请在调用此函数后手动调用 `switch_player()`。
  ///
  /// # 示例
  ///
  /// ```
  /// let mut gm = GameManager::new();
  /// gm.make_move_by_iccs("a0a1"); // 车九进一
  /// gm.switch_player(); // 切换到黑方
  /// ```
  void makeMoveByIccs({required String iccsMove});

  factory GameManager() =>
      RustLib.instance.api.crateChessGameManagerGameManagerNew();

  PlacementValidity placePieceOnBoard(
      {required int visualRow,
      required int visualCol,
      required SidePieceType pieceToPlace});

  void reset();

  void switchPlayer();

  Future<void> updateBoard(
      {required int visualRow,
      required int visualCol,
      required int pieceIndex});

  /// 全面校验当前棋盘布局是否合规，用于退出摆谱模式等场景。
  /// 返回 Ok(()) 表示合规，Err(String) 表示不合规及其原因。
  void validateFullBoardLayout();
}

@freezed
sealed class PlacementValidity with _$PlacementValidity {
  const PlacementValidity._();

  const factory PlacementValidity.valid() = PlacementValidity_Valid;
  const factory PlacementValidity.invalidLocation(
    String field0,
  ) = PlacementValidity_InvalidLocation;
  const factory PlacementValidity.maxPiecesReached(
    String field0,
  ) = PlacementValidity_MaxPiecesReached;
  const factory PlacementValidity.kingsFacing(
    String field0,
  ) = PlacementValidity_KingsFacing;
}

class U8Array256 extends NonGrowableListView<int> {
  static const arraySize = 256;

  @internal
  Uint8List get inner => _inner;
  final Uint8List _inner;

  U8Array256(this._inner)
      : assert(_inner.length == arraySize),
        super(_inner);

  U8Array256.init() : this(Uint8List(arraySize));
}
