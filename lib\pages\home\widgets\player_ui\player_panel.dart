/*
 * <AUTHOR> 老董
 * @Date         : 2022-04-30 11:10:14
 * @LastEditors  : 老董
 * @LastEditTime : 2025-05-15 22:57:53
 * @Description  : 显示红黑双方剩余时间、引擎名称的状态条（红黑方各一个）
 */

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:meng_ru_ling_shi/pages/home/<USER>/timer/player_digital_clock.dart';
import 'package:meng_ru_ling_shi/rust/chess/piece_utils.dart' as chess_utils;

import '../../lib.dart'; // home_view 从此处导出，现在使用 chess_utils.Player
// 对于 getPlayerIconImagePath，确保其已正确导入或可访问
import 'engine_load_button.dart';

class PlayerPanel extends GetView<HomeController> {
  final chess_utils.Player player; // 使用前缀
  final _fontRatio = 16 / 58; // 字体大小与棋子大小的比例

  const PlayerPanel({required this.player, super.key});

  @override
  Widget build(BuildContext context) {
    const padSize = 5.0;
    final barHeight = padSize * 2 + controller.pieceSize;
    final iconSize = controller.pieceSize * 0.4 - padSize;
    const roundRadius = 20.0;

    return Card(
      shadowColor: getPlayerColor(),
      elevation: 20,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(roundRadius)),
      clipBehavior: Clip.antiAlias,
      child: Container(
        decoration: BoxDecoration(
            gradient: LinearGradient(
                colors: [getPlayerColor(), Colors.blueGrey], begin: Alignment.topLeft, end: Alignment.bottomRight)),
        height: barHeight,
        padding: const EdgeInsets.only(top: padSize, bottom: padSize),
        child: Row(
          children: [
            Expanded(
              flex: 3,
              child: SvgPicture.asset(
                getPlayerIconImagePath(player), // player 现在是 chess_utils.Player
                width: controller.pieceSize,
                height: controller.pieceSize,
              ),
            ),
            Expanded(
              flex: 7,
              child: Column(
                children: [
                  Expanded(
                    flex: 4,
                    child: Row(
                      children: [
                        Expanded(
                          flex: 3,
                          child: Padding(
                            padding: const EdgeInsets.only(left: 10.0),
                            child: EngineLoadButton(
                              player: player,
                              icon: Icons.computer, // Changed from iconData
                              iconSize: iconSize,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 9,
                          child: Container(
                            padding: const EdgeInsets.only(left: 5.0),
                            alignment: Alignment.topCenter,
                            child: Obx(
                              () => Text(
                                controller.getEngineName(player),
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                    color: Colors.grey,
                                    decoration: TextDecoration.underline,
                                    fontWeight: FontWeight.bold,
                                    fontSize: _fontRatio * controller.pieceSize),
                              ),
                            ),
                          ),
                        )
                      ],
                    ),
                  ),
                  Expanded(
                    flex: 6,
                    child: PlayerDigitalClock(player, roundRadius),
                  )
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  getPlayerColor() {
    return player == chess_utils.Player.red ? Colors.red : Colors.black; // 使用前缀
  }
}
