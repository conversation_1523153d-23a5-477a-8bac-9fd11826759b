import 'package:get/get.dart';
import 'package:pausable_timer/pausable_timer.dart';

enum TimerControlType {
  start,
  stop,
  pause,
}

class DigitTimeController {
  final _duration = Duration.zero.obs;

  get duration => _duration;
  get inSeconds => _duration.value.inSeconds;
  get inMinutes => _duration.value.inMinutes;
  get inHours => _duration.value.inHours;

  late final PausableTimer _timer;
  int _elapsedMSecs = 0;

  _setTimeElapsed(mSeconds) {
    _duration.value = Duration(milliseconds: mSeconds);
  }

  runTimer() {
    if (!_timer.isActive) {
      _timer.start();
    }
  }

  stopTimer() {
    if (_timer.isActive || _timer.isPaused) {
      _timer.pause();
    }
  }

  void resetTimer() {
    _timer.reset();
    _elapsedMSecs = 0;
    _setTimeElapsed(_elapsedMSecs); //更新ui显示
  }

  pauseTimer() {
    if (_timer.isActive) {
      _timer.pause();
    }
  }

  DigitTimeController() {
    // 定时器
    _timer = PausableTimer(
      const Duration(milliseconds: 1000),
      () {
        _elapsedMSecs += 1000;
        _setTimeElapsed(_elapsedMSecs);
        _timer
          ..reset()
          ..start();
      },
    );
  }
}
