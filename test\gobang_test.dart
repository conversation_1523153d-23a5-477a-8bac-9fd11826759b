import 'package:flutter_test/flutter_test.dart';
import 'package:test_gobang11/rust/api/gobang.dart';
import 'package:test_gobang11/rust/frb_generated.dart';

void main() {
  setUpAll(() async {
    await RustLib.init();
  });

  group('五子棋游戏测试', () {
    test('创建游戏', () {
      final game = createGobangGame();
      expect(game, isNotNull);
    });

    test('初始状态测试', () async {
      final game = createGobangGame();

      // 测试初始状态
      expect(await game.getCurrentPlayer(), PieceType.black);
      expect(await game.getGameState(), GameState.playing);
      expect(await game.getBoardSize(), 15);
      expect(await game.getMoveCount(), 0);

      // 测试空棋盘
      for (int i = 0; i < 15; i++) {
        for (int j = 0; j < 15; j++) {
          expect(await game.getPiece(row: i, col: j), PieceType.empty);
        }
      }
    });

    test('落子测试', () async {
      final game = createGobangGame();

      // 黑子先手
      expect(await game.getCurrentPlayer(), PieceType.black);

      // 落第一子
      final success1 = await game.makeMove(row: 7, col: 7);
      expect(success1, true);
      expect(await game.getPiece(row: 7, col: 7), PieceType.black);
      expect(await game.getCurrentPlayer(), PieceType.white);
      expect(await game.getMoveCount(), 1);

      // 白子落子
      final success2 = await game.makeMove(row: 7, col: 8);
      expect(success2, true);
      expect(await game.getPiece(row: 7, col: 8), PieceType.white);
      expect(await game.getCurrentPlayer(), PieceType.black);
      expect(await game.getMoveCount(), 2);
    });

    test('非法落子测试', () async {
      final game = createGobangGame();

      // 先落一子
      await game.makeMove(row: 7, col: 7);

      // 尝试在同一位置落子
      final success = await game.makeMove(row: 7, col: 7);
      expect(success, false);
      expect(await game.getMoveCount(), 1);
    });

    test('边界测试', () async {
      final game = createGobangGame();

      // 测试边界位置
      expect(await game.makeMove(row: 0, col: 0), true);
      expect(await game.makeMove(row: 14, col: 14), true);

      // 测试超出边界
      expect(await game.makeMove(row: -1, col: 0), false);
      expect(await game.makeMove(row: 0, col: -1), false);
      expect(await game.makeMove(row: 15, col: 0), false);
      expect(await game.makeMove(row: 0, col: 15), false);
    });

    test('胜利条件测试 - 水平', () async {
      final game = createGobangGame();

      // 黑子连成5子 (水平)
      for (int i = 0; i < 5; i++) {
        await game.makeMove(row: 7, col: i);
        if (i < 4) {
          await game.makeMove(row: 8, col: i); // 白子
        }
      }

      expect(await game.getGameState(), GameState.blackWin);
    });

    test('胜利条件测试 - 垂直', () async {
      final game = createGobangGame();

      // 黑子连成5子 (垂直)
      for (int i = 0; i < 5; i++) {
        await game.makeMove(row: i, col: 7);
        if (i < 4) {
          await game.makeMove(row: i, col: 8); // 白子
        }
      }

      expect(await game.getGameState(), GameState.blackWin);
    });

    test('胜利条件测试 - 对角线', () async {
      final game = createGobangGame();

      // 黑子连成5子 (主对角线)
      for (int i = 0; i < 5; i++) {
        await game.makeMove(row: i, col: i);
        if (i < 4) {
          await game.makeMove(row: i, col: i + 1); // 白子
        }
      }

      expect(await game.getGameState(), GameState.blackWin);
    });

    test('悔棋测试', () async {
      final game = createGobangGame();

      // 落几子
      await game.makeMove(row: 7, col: 7);
      await game.makeMove(row: 7, col: 8);
      await game.makeMove(row: 8, col: 7);

      expect(await game.getMoveCount(), 3);
      expect(await game.getCurrentPlayer(), PieceType.white);

      // 悔棋
      final undoSuccess = await game.undoMove();
      expect(undoSuccess, true);
      expect(await game.getMoveCount(), 2);
      expect(await game.getCurrentPlayer(), PieceType.black);
      expect(await game.getPiece(row: 8, col: 7), PieceType.empty);
    });

    test('重置游戏测试', () async {
      final game = createGobangGame();

      // 落几子
      await game.makeMove(row: 7, col: 7);
      await game.makeMove(row: 7, col: 8);

      // 重置
      await game.reset();

      expect(await game.getCurrentPlayer(), PieceType.black);
      expect(await game.getGameState(), GameState.playing);
      expect(await game.getMoveCount(), 0);
      expect(await game.getPiece(row: 7, col: 7), PieceType.empty);
      expect(await game.getPiece(row: 7, col: 8), PieceType.empty);
    });
  });
}
