// 软件主界面：左侧为棋盘UI，右侧为状态UI（棋谱、局势曲线等）

import 'dart:io';

import 'package:dashed_rect/dashed_rect.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:macos_ui/macos_ui.dart' as macos_ui; // MacosTabView所需的包
import 'package:meng_ru_ling_shi/common/widgets/move_history_panel.dart';
import 'package:meng_ru_ling_shi/rust/chess/piece_utils.dart' as chess_utils; // chess_utils前缀，避免命名冲突
import 'package:window_manager/window_manager.dart';

import '../../common/global.dart'; // global.dart内部已使用chess_utils.Player
import '../../common/widgets/float_tool.dart';
import '../../common/widgets/ios_dialog_widget.dart';
import 'ctrl.dart';
import 'widgets/board_arrow.dart';
import 'widgets/iccs_tooltip.dart';
import 'widgets/player_ui/player_panel.dart';
import 'widgets/setting_sheet.dart';

class HomeView extends GetView<HomeController> {
  const HomeView({super.key});

  // late double _width; // 移至build方法内部
  // late double _height; // 移至build方法内部
  // late double realTestRatio; // 移至build方法内部

  @override
  Widget build(BuildContext context) {
    final double width = MediaQuery.of(context).size.width;
    final double height = MediaQuery.of(context).size.height;

    // 将测试环境的尺寸按比例转换到当前实际屏幕尺寸
    final double realTestRatio = width / testWidth;
    controller.panelWidth = realTestRatio * testPanelWidth; // 浮动工具栏宽度
    controller.borderRadius = realTestRatio * testBorderRadius; // 浮动工具栏圆角
    controller.pieceSize = realTestRatio * testPieceSize; // 棋子尺寸
    controller.leftTopOffSet = Offset(realTestRatio * (testLeftTop1stPos.dx - testBoardLeftTopCornerPos.dx),
        realTestRatio * (testLeftTop1stPos.dy - testBoardLeftTopCornerPos.dy)); // 左上角第一个棋子中心点相对于棋盘左上角的偏移
    controller.pieceGap = realTestRatio * (testLeftTop2edPos.dx - testLeftTop1stPos.dx); // 相邻棋子中心点的间距 (X、Y轴相同)

    // 主UI布局
    final mainUi = Stack(
      alignment: AlignmentDirectional.centerEnd, // 浮动工具栏默认停靠在右侧中间
      children: [
        Row(
          children: [
            // 左侧：棋盘及相关组件
            _getWholeChessBoardView(context, height), // 传入屏幕高度
            // 右侧：状态组件 (根据模式切换)
            Expanded(
              child: Obx(() {
                if (controller.isArrangeMode) {
                  // 摆谱模式
                  return _buildArrangeModePanel(context, height); // 传入屏幕高度
                } else {
                  // 普通游戏模式
                  return _buildNormalModeStateWidgets();
                }
              }),
            ),
          ],
        ),
        // 浮动工具栏
        FloatBoxPanel(
          panelKey: ValueKey(controller.isArrangeMode), // 修改：直接使用 RxBool 对象作为 ValueKey 的参数
          panelWidthInput: controller.panelWidth, // 使用 panelWidthInput
          backgroundColor: const Color(0xFF222222),
          panelShape: PanelShape.rectangle,
          borderRadiusInput: BorderRadius.circular(controller.borderRadius), // 使用 borderRadiusInput
          dockType: DockType.outside,
          panelButtonColor: Colors.blueGrey,
          customButtonColor: Colors.grey,
          dockActivate: controller.dockActivate,
          // isButtonEnabled回调已移除，逻辑移至FloatBoxPanel内部通过HomeController处理
          buttons: const [
            CupertinoIcons.news, // 索引0: 新游戏
            CupertinoIcons.settings, // 索引1: 设置
            CupertinoIcons.arrow_up_arrow_down, // 索引2: 翻转棋盘
            CupertinoIcons.pencil_ellipsis_rectangle, // 索引3: 摆谱模式
            CupertinoIcons.link, // 索引4: 链接 (功能待定)
            CupertinoIcons.minus, // 索引5: 最小化窗口
            CupertinoIcons.xmark_circle // 索引6: 退出程序
          ],
          onPressed: (index) {
            // 在摆谱模式下，禁用部分按钮
            if (controller.isArrangeMode) {
              final allowedInArrangeMode = [1, 2, 5, 6]; // 允许的按钮索引: 设置, 翻转, 最小化, 关闭
              if (!allowedInArrangeMode.contains(index)) {
                debugPrint("按钮 $index 在摆谱模式下被禁用");
                return;
              }
            }

            // 正常处理逻辑
            switch (index) {
              case 0: // 新游戏
                controller.onToolButtonPressed(newGameBtnLog);
                break;
              case 1: // 设置
                getSettingSheet(context);
                break;
              case 2: // 翻转棋盘
                controller.flipBoard();
                break;
              case 3: // 摆谱模式
                controller.toggleArrangeMode();
                break;
              case 4: // 链接 (功能待定)
                controller.onToolButtonPressed(newLinkBtnLog);
                break;
              case 5: // 最小化窗口
                windowManager.minimize();
                break;
              case 6: // 退出程序
                showIosDialog(context, "提示", "是否退出程序？", onYesPressed: () {
                  exit(0);
                });
                break;
              default:
                debugPrint("未处理的按钮索引: $index");
            }
          },
        ),
      ],
    );

    // Scaffold提供了一些默认主题，所以不能去除
    final scaffold = Scaffold(
      body: GestureDetector(
        onTapUp: (details) {
          // 监听棋盘点击
          controller.onWindowClicked(details.localPosition);
        },
        behavior: HitTestBehavior.translucent, // 确保空白区域也能响应事件
        onPanStart: (details) {
          // 拖动窗口
          windowManager.startDragging();
        },
        child: mainUi,
      ),
    );

    return KeyboardListener(
      focusNode: controller.hotKeyFocusNode, // 确保已调用requestFocus()
      onKeyEvent: (KeyEvent event) {
        if (event is KeyDownEvent &&
            (HardwareKeyboard.instance.isControlPressed || HardwareKeyboard.instance.isMetaPressed)) {
          if (event.logicalKey == LogicalKeyboardKey.keyC) {
            // Ctrl/Cmd + C: 复制FEN到剪贴板
            controller.copyFenToClipboard();
          } else if (event.logicalKey == LogicalKeyboardKey.keyV) {
            // Ctrl/Cmd + V: 从剪贴板粘贴FEN到棋盘
            controller.copyClipboardFenToBoard();
          }
        }
      },
      child: scaffold,
    );
  }

  // 构建普通游戏模式下的右侧状态组件 (玩家信息、棋谱、日志)
  Widget _buildNormalModeStateWidgets() {
    const redPanel = PlayerPanel(player: chess_utils.Player.red); // 红方信息面板
    const blackPanel = PlayerPanel(player: chess_utils.Player.black); // 黑方信息面板
    const moveHistoryPanel = MoveHistoryPanel(); // 棋谱历史列表
    const logPanel = Center(child: Text('日志内容区')); // 日志Tab的空内容 (待实现)

    return macos_ui.MacosTheme(
      // 使用macOS风格主题
      data: macos_ui.MacosThemeData.light().copyWith(
        primaryColor: macos_ui.MacosColors.controlAccentColor,
        accentColor: macos_ui.AccentColor.blue,
      ),
      child: Container(
        color: backgroundStartColor, // 背景色
        child: Column(
          children: [
            redPanel,
            blackPanel,
            Expanded(
              // TabView占据剩余空间
              child: macos_ui.MacosTabView(
                controller: controller.macosTabController,
                position: macos_ui.MacosTabPosition.bottom, // Tab栏在底部
                padding: const EdgeInsets.fromLTRB(5, 0, 5, 3),
                tabs: const [
                  macos_ui.MacosTab(label: '棋谱'),
                  macos_ui.MacosTab(label: '日志'),
                ],
                children: const [
                  moveHistoryPanel,
                  logPanel,
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建摆谱模式下的右侧操作面板
  Widget _buildArrangeModePanel(BuildContext context, double height) {
    // 此面板包含棋子选择和摆谱操作按钮
    return Container(
      width: double.infinity, // 占据Expanded分配的所有宽度
      height: height, // 使用传入的高度
      color: Colors.blueGrey.withOpacity(0.9), // 半透明背景色
      padding: const EdgeInsets.all(16.0),
      child: SingleChildScrollView(
        // 内容可能超出，允许滚动
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const Text(
              '摆放棋子',
              style: TextStyle(color: Colors.white, fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 15),
            _buildPieceSelectionGrid(), // 棋子选择网格
            const SizedBox(height: 15),
            const Divider(color: Colors.white54),
            _buildPlayerTurnToggle(), // 执棋方切换按钮
            const Divider(color: Colors.white54),
            const Text(
              '棋盘操作',
              style: TextStyle(color: Colors.white, fontSize: 22, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 15),
            SizedBox(
              // 清空棋盘按钮
              width: double.infinity,
              child: ElevatedButton.icon(
                style: ElevatedButton.styleFrom(padding: const EdgeInsets.symmetric(vertical: 12)),
                icon: const Icon(CupertinoIcons.trash, color: Colors.redAccent),
                label: const Text('清空棋盘', style: TextStyle(fontSize: 16)),
                onPressed: () {
                  controller.clearBoardForArranging();
                },
              ),
            ),
            const SizedBox(height: 8),
            SizedBox(
              // 恢复初始按钮
              width: double.infinity,
              child: ElevatedButton.icon(
                style: ElevatedButton.styleFrom(padding: const EdgeInsets.symmetric(vertical: 12)),
                icon: const Icon(CupertinoIcons.arrow_2_circlepath, color: Colors.lightBlueAccent),
                label: const Text('恢复初始', style: TextStyle(fontSize: 16)),
                onPressed: () {
                  controller.resetBoardToInitialForArranging();
                },
              ),
            ),
            const SizedBox(height: 8),
            SizedBox(
              // 交换红黑按钮
              width: double.infinity,
              child: ElevatedButton.icon(
                style: ElevatedButton.styleFrom(padding: const EdgeInsets.symmetric(vertical: 12)),
                icon: const Icon(CupertinoIcons.shuffle, color: Colors.orangeAccent),
                label: const Text('交换红黑', style: TextStyle(fontSize: 16)),
                onPressed: () {
                  controller.exchangeAllPiecesOnBoard();
                },
              ),
            ),
            const SizedBox(height: 15),
            SizedBox(
              // 完成摆谱按钮
              width: double.infinity,
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green, padding: const EdgeInsets.symmetric(vertical: 14)),
                onPressed: () {
                  controller.toggleArrangeMode(); // 退出摆谱模式
                },
                child: const Text('完成摆谱',
                    style: TextStyle(color: Colors.white, fontSize: 18, fontWeight: FontWeight.bold)),
              ),
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  // 获取绘制箭头的Widget
  Widget _getArrowWidget() {
    return CustomPaint(
      painter: ArrowPainter(controller.arrowMoves, controller.leftTopOffSet, controller.pieceGap),
    );
  }

  // 构建左侧整个棋盘区域
  Widget _getWholeChessBoardView(BuildContext context, double height) {
    final boardImage = SvgPicture.asset(boardPath, height: height); // 棋盘背景图

    // 计算棋盘的实际显示尺寸 (用于ICCS提示的边界判断)
    final boardWidth = controller.pieceGap * 8 + controller.pieceSize; // 9列棋子形成的宽度
    final boardHeight = controller.pieceGap * 9 + controller.pieceSize; // 10行棋子形成的高度
    final boardSize = Size(boardWidth, boardHeight);

    return MouseRegion(
      cursor: controller.isArrangeMode && controller.selectedPieceForArrangement != null
          ? SystemMouseCursors.none // 摆谱模式且有棋子选中时隐藏默认光标，以显示棋子跟随
          : SystemMouseCursors.basic, // 其他情况使用默认光标
      onHover: (event) {
        // 鼠标悬停
        controller.onMouseHover(event.localPosition);
      },
      onExit: (_) {
        // 鼠标移出
        controller.clearMouseHover();
      },
      child: GestureDetector(
        onSecondaryTapUp: (details) {
          // 监听右键点击 (用于摆谱模式下移除棋子)
          if (controller.isArrangeMode) {
            controller.handleRightClickRemove(details.localPosition);
          }
        },
        child: Obx(
          // 使用Obx监听响应式变量的变化
          () {
            final children = <Widget>[
              boardImage, // 棋盘背景
              ...controller.boardPiecesView.pieces.map((piece) => _buildPieceWidget(piece)), // 绘制所有棋子
              _getArrowWidget(), // 绘制走法箭头
              // ICCS坐标提示
              IccsPositionTooltip(
                position: controller.hoverPosition,
                iccsText: controller.hoverIccsText,
                boardSize: boardSize,
                needFlipForDisplay: controller.gameManager.needFlipForDisplay,
              ),
            ];

            // 如果在摆谱模式，有选中的棋子，并且鼠标在棋盘上，则添加跟随鼠标的棋子图像
            if (controller.isArrangeMode &&
                controller.selectedPieceForArrangement != null &&
                controller.pieceFollowingMousePosition.value != null) {
              final selectedPieceType = controller.selectedPieceForArrangement!;
              final mousePos = controller.pieceFollowingMousePosition.value!;
              final pieceRadius = controller.pieceSize / 2;
              children.add(
                Positioned(
                  left: mousePos.dx - pieceRadius, // 使棋子中心对准鼠标
                  top: mousePos.dy - pieceRadius,
                  child: Opacity(
                    opacity: 0.7, // 半透明效果
                    child: SvgPicture.asset(
                      getPieceImagePath(selectedPieceType),
                      width: controller.pieceSize,
                      height: controller.pieceSize,
                    ),
                  ),
                ),
              );
            }
            return Stack(children: children); // 使用Stack层叠所有组件
          },
        ),
      ),
    );
  }

  // 构建单个棋子的Widget（包括mask框）
  Widget _buildPieceWidget(MaskedPiece piece) {
    final pieceRadius = (controller.pieceSize / 2);
    final pieceOffsetX = controller.leftTopOffSet.dx - pieceRadius; // X轴基准偏移
    final pieceOffsetY = controller.leftTopOffSet.dy - pieceRadius; // Y轴基准偏移

    // 计算棋子在UI上的像素位置，考虑棋盘是否翻转
    double xPixel, yPixel;

    xPixel = pieceOffsetX + (piece.col - 1) * (controller.pieceGap);
    yPixel = pieceOffsetY + (piece.row - 1) * (controller.pieceGap);

    final mask = piece.maskType(); // 获取棋子掩码类型 (选中、移动标记)
    final pieceType = piece.pieceType(); // 获取棋子类型

    return Positioned(
      left: xPixel,
      top: yPixel,
      child: DashedRect(
        // 使用虚线矩形作为选中框
        gap: (!controller.gameStarted && mask != MaskedType.moved) || mask == MaskedType.none
            ? 50 // 游戏未开始且非移动标记，或无标记时，不显示框 (gap很大)
            : mask == MaskedType.focused
                ? 0.08 // 选中标记，接近实线
                : 3, // 移动标记，虚线
        strokeWidth: 1.5,
        color: Colors.deepPurpleAccent,
        child: pieceType == chess_utils.SidePieceType.none // 如果是空棋子
            ? SizedBox(
                // 则显示一个同样大小的空SizedBox
                width: controller.pieceSize,
                height: controller.pieceSize,
              )
            : SvgPicture.asset(
                // 否则显示棋子图片
                getPieceImagePath(piece.pieceType()),
                width: controller.pieceSize,
                height: controller.pieceSize,
              ),
      ),
    );
  }

  // 获取白色边框 (未使用)
  Border getWhiteBorderCircle() {
    return Border.all(color: const Color.fromARGB(137, 5, 13, 107), width: 2, style: BorderStyle.solid);
  }

  // 获取绿色边框 (未使用)
  Border getGreenBorderCircle() {
    return Border.all(color: const Color.fromARGB(137, 2, 130, 51), width: 2, style: BorderStyle.solid);
  }

  // 构建摆谱模式下的棋子选择网格
  Widget _buildPieceSelectionGrid() {
    final allPieceTypes = [
      // 所有棋子类型列表
      chess_utils.SidePieceType.redKing,
      chess_utils.SidePieceType.redAdvisor,
      chess_utils.SidePieceType.redBishop,
      chess_utils.SidePieceType.redKnight,
      chess_utils.SidePieceType.redRook,
      chess_utils.SidePieceType.redCannon,
      chess_utils.SidePieceType.redPawn,
      chess_utils.SidePieceType.blackKing,
      chess_utils.SidePieceType.blackAdvisor,
      chess_utils.SidePieceType.blackBishop,
      chess_utils.SidePieceType.blackKnight,
      chess_utils.SidePieceType.blackRook,
      chess_utils.SidePieceType.blackCannon,
      chess_utils.SidePieceType.blackPawn,
    ];

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0), // 水平内边距
      child: GridView.count(
        crossAxisCount: 4, // 每行显示4个棋子
        shrinkWrap: true, // 使其在Column或SingleChildScrollView中正确布局
        physics: const NeverScrollableScrollPhysics(), // 禁用GridView自身的滚动
        mainAxisSpacing: 8.0, // 垂直间距
        crossAxisSpacing: 8.0, // 水平间距
        childAspectRatio: 1.0, // 子项宽高比 (正方形)
        children: [
          for (final pieceType in allPieceTypes)
            Obx(() {
              // 使用Obx监听选中状态
              final isSelected = controller.selectedPieceForArrangement == pieceType;
              return GestureDetector(
                onTap: () {
                  controller.selectPieceForArrangement(pieceType); // 点击选择/取消选择棋子
                },
                child: Container(
                  padding: const EdgeInsets.all(4.0),
                  decoration: BoxDecoration(
                    color: isSelected ? Colors.blueGrey.withOpacity(0.5) : Colors.transparent, // 选中时背景色
                    borderRadius: BorderRadius.circular(4.0),
                    border: Border.all(
                      color: isSelected ? Colors.blueAccent : Colors.white54, // 选中时边框色
                      width: 1.5,
                    ),
                  ),
                  child: SvgPicture.asset(
                    getPieceImagePath(pieceType),
                    width: controller.pieceSize * 0.55, // 棋子图片大小
                    height: controller.pieceSize * 0.55,
                  ),
                ),
              );
            }),
        ],
      ),
    );
  }

  // 构建摆谱模式下的执棋方切换UI
  Widget _buildPlayerTurnToggle() {
    return Obx(() {
      final player = controller.arrangeModePlayerToMove;
      final isRedToMove = player == chess_utils.Player.red;
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            '轮到: ${player.getName()}', // 显示当前执棋方
            style: const TextStyle(color: Colors.white, fontSize: 16),
          ),
          const SizedBox(width: 10),
          Transform.scale(
            // 调整CupertinoSwitch的大小
            scale: 0.8,
            child: CupertinoSwitch(
              value: isRedToMove, // 开关状态
              onChanged: (bool value) {
                controller.toggleArrangeModePlayerToMove(); // 点击切换执棋方
              },
              activeTrackColor: Colors.redAccent, // 红方激活时的轨道颜色
              inactiveTrackColor: Colors.black, // 黑方激活时的轨道颜色 (或红方非激活)
            ),
          ),
        ],
      );
    });
  }
}

// 根据掩码类型获取对应的SVG图片路径
String getMaskImagePath(MaskedType sidePieceType) {
  switch (sidePieceType) {
    case MaskedType.none:
      throw '查找mask图片时，发现类型为none'; // 不应为none类型请求图片
    case MaskedType.focused:
      return "${skinPath}mask1.svg"; // 选中框图片
    case MaskedType.moved:
      return "${skinPath}mask2.svg"; // 最新移动标记图片
  }
}

// 根据棋子类型获取对应的SVG图片路径
String getPieceImagePath(chess_utils.SidePieceType sidePieceType) {
  switch (sidePieceType) {
    case chess_utils.SidePieceType.redKing:
      return "${skinPath}rk.svg";
    case chess_utils.SidePieceType.redAdvisor:
      return "${skinPath}ra.svg";
    case chess_utils.SidePieceType.redBishop:
      return "${skinPath}rb.svg";
    case chess_utils.SidePieceType.redKnight:
      return "${skinPath}rn.svg";
    case chess_utils.SidePieceType.redRook:
      return "${skinPath}rr.svg";
    case chess_utils.SidePieceType.redCannon:
      return "${skinPath}rc.svg";
    case chess_utils.SidePieceType.redPawn:
      return "${skinPath}rp.svg";
    // 黑方棋子
    case chess_utils.SidePieceType.blackKing:
      return "${skinPath}bk.svg";
    case chess_utils.SidePieceType.blackAdvisor:
      return "${skinPath}ba.svg";
    case chess_utils.SidePieceType.blackBishop:
      return "${skinPath}bb.svg";
    case chess_utils.SidePieceType.blackKnight:
      return "${skinPath}bn.svg";
    case chess_utils.SidePieceType.blackRook:
      return "${skinPath}br.svg";
    case chess_utils.SidePieceType.blackCannon:
      return "${skinPath}bc.svg";
    case chess_utils.SidePieceType.blackPawn:
      return "${skinPath}bp.svg";
    default:
      throw '错误：未知棋子类型';
  }
}

// 根据玩家获取对应的头像图标路径
String getPlayerIconImagePath(chess_utils.Player player) {
  switch (player) {
    case chess_utils.Player.red:
      return "${playerIconPath}rk.svg"; // 红方图标
    case chess_utils.Player.black:
      return "${playerIconPath}bk.svg"; // 黑方图标
    default:
      throw Exception('Player is not Red or Black, Something went wrong!');
  }
}
