#[cfg(test)]
mod tests {
    use crate::chess::{
        <PERSON><PERSON><PERSON><PERSON>, PieceType, Player, SidePieceType, board_to_fen,
        get_array_256_index_from_board_row_col, get_unside_piece_by_side_piece,
    };

    /// 测试红方七路相(相七)在初始状态下的有效移动
    #[test]
    fn test_red_bishop_at_col_3_initial_moves() {
        let gm = GameManager::new();

        // 获取红方七路相(相七)的位置
        let bishop_pos = get_array_256_index_from_board_row_col(10, 3);

        // 1. 获取棋子类型并验证是相
        let piece = SidePieceType::RedBishop;
        let unside_piece = get_unside_piece_by_side_piece(piece);
        assert_eq!(unside_piece, PieceType::Bishop);

        // 2. 验证具体移动
        let expected_moves = [
            (
                "c0a2",
                "相七进九",
                "rnbakabnr/9/1c5c1/p1p1p1p1p/9/9/P1P1P1P1P/BC5C1/9/RN1AKABNR w - - 0 1",
            ),
            (
                "c0e2",
                "相七进五",
                "rnbakabnr/9/1c5c1/p1p1p1p1p/9/9/P1P1P1P1P/1C2B2C1/9/RN1AKABNR w - - 0 1",
            ),
        ];
        let valid_moves = gm.get_piece_all_valid_moves(bishop_pos);

        // 2.1 验证移动数量
        assert_eq!(valid_moves.len(), expected_moves.len());

        // 2.2 验证iccs是否正确
        for (iccs, _, _) in &expected_moves {
            assert!(valid_moves.contains(&(*iccs).to_string()));
        }

        // 2.3 验证iccs -> 中文记录法
        for (iccs, chinese, _) in &expected_moves {
            assert_eq!(gm.iccs_move_to_chinese_move(iccs), *chinese);
        }

        // 3. 验证移动后的棋盘状态
        for (iccs, _, expected_fen) in &expected_moves {
            // 创建新的GameManager实例进行测试
            let mut test_gm = GameManager::new();

            // 使用 GameManager 的 make_move_by_iccs 方法执行移动
            test_gm.make_move_by_iccs(iccs);

            // 获取移动后的FEN字符串
            // 注意：在实际游戏中，红方移动后应该轮到黑方行棋
            // 但在这个测试中，我们保持与预期FEN字符串一致的玩家参数（Player::Red）
            let actual_fen = board_to_fen(&test_gm.board, Player::Red);

            // 验证FEN字符串
            assert_eq!(actual_fen, *expected_fen);
        }
    }

    /// 测试红方三路相(相三)在初始状态下的有效移动
    #[test]
    fn test_red_bishop_at_col_7_initial_moves() {
        let gm = GameManager::new();

        // 获取红方三路相(相三)的位置
        let bishop_pos = get_array_256_index_from_board_row_col(10, 7);

        // 1. 获取棋子类型并验证是相
        let piece = SidePieceType::RedBishop;
        let unside_piece = get_unside_piece_by_side_piece(piece);
        assert_eq!(unside_piece, PieceType::Bishop);

        // 2. 验证具体移动
        let expected_moves = [
            (
                "g0e2",
                "相三进五",
                "rnbakabnr/9/1c5c1/p1p1p1p1p/9/9/P1P1P1P1P/1C2B2C1/9/RNBAKA1NR w - - 0 1",
            ),
            (
                "g0i2",
                "相三进一",
                "rnbakabnr/9/1c5c1/p1p1p1p1p/9/9/P1P1P1P1P/1C5CB/9/RNBAKA1NR w - - 0 1",
            ),
        ];
        let valid_moves = gm.get_piece_all_valid_moves(bishop_pos);

        // 2.1 验证移动数量
        assert_eq!(valid_moves.len(), expected_moves.len());

        // 2.2 验证iccs是否正确
        for (iccs, _, _) in &expected_moves {
            assert!(valid_moves.contains(&(*iccs).to_string()));
        }

        // 2.3 验证iccs -> 中文记录法
        for (iccs, chinese, _) in &expected_moves {
            assert_eq!(gm.iccs_move_to_chinese_move(iccs), *chinese);
        }

        // 3. 验证移动后的棋盘状态
        for (iccs, _, expected_fen) in &expected_moves {
            // 创建新的GameManager实例进行测试
            let mut test_gm = GameManager::new();

            // 使用 GameManager 的 make_move_by_iccs 方法执行移动
            test_gm.make_move_by_iccs(iccs);

            // 获取移动后的FEN字符串
            // 注意：在实际游戏中，红方移动后应该轮到黑方行棋
            // 但在这个测试中，我们保持与预期FEN字符串一致的玩家参数（Player::Red）
            let actual_fen = board_to_fen(&test_gm.board, Player::Red);

            // 验证FEN字符串
            assert_eq!(actual_fen, *expected_fen);
        }
    }
}
