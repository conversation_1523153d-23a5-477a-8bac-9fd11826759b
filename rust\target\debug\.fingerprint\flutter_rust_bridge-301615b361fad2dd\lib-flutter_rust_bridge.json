{"rustc": 16591470773350601817, "features": "[\"anyhow\", \"console_error_panic_hook\", \"dart-opaque\", \"default\", \"log\", \"portable-atomic\", \"rust-async\", \"thread-pool\", \"user-utils\", \"wasm-start\"]", "declared_features": "[\"anyhow\", \"backtrace\", \"chrono\", \"console_error_panic_hook\", \"dart-opaque\", \"default\", \"log\", \"portable-atomic\", \"rust-async\", \"thread-pool\", \"user-utils\", \"uuid\", \"wasm-start\"]", "target": 9045615548121237704, "profile": 12314186428942525113, "path": 10456272513680161971, "deps": [[1510982600281540773, "allo_isolate", false, 5394498299247729640], [2706460456408817945, "futures", false, 11291156515380782835], [3216452410829326882, "dart_sys", false, 3829297927382945755], [3712811570531045576, "byteorder", false, 1769337234427496576], [3958489542916937055, "portable_atomic", false, 13126418054550356644], [5986029879202738730, "log", false, 9782137404826498996], [6422440960550756682, "build_script_build", false, 7860978964683076264], [9538054652646069845, "tokio", false, 17301666111175008303], [11204906226375074320, "delegate_attr", false, 3249827665731120906], [13625485746686963219, "anyhow", false, 9490341966853886741], [14318411632997640213, "flutter_rust_bridge_macros", false, 238857927754753105], [14521117738091886193, "threadpool", false, 6908449073507246976], [17917672826516349275, "lazy_static", false, 7351823651728953683]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\flutter_rust_bridge-301615b361fad2dd\\dep-lib-flutter_rust_bridge", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}