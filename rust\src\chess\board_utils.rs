use flutter_rust_bridge::frb;
use num_traits::FromPrimitive;

use super::{FORT_MATRIX, IN_BOARD_MATRIX, Player, SidePieceType};

// 判断棋子是否在九宫中
pub const fn is_pos_in_fort(pos: u8) -> bool {
    FORT_MATRIX[pos as usize] != 0
}

pub const fn is_pos_in_board(pos: u8) -> bool {
    IN_BOARD_MATRIX[pos as usize] != 0
}

/// 将棋盘的行列坐标转换为内部256字节数组的索引
///
/// # 参数
///
/// * `row` - 棋盘上的行号(1-10) 由上往下数
/// * `col` - 棋盘上的列号(1-9) 由左往右数
///
/// # 返回值
///
/// 返回对应的内部256字节数组的索引(0-255)
///
/// # 示例
///
/// ```
/// let index = get_array_256_index_from_board_row_col(1, 1);
/// assert_eq!(index, 0x33); // 第一个棋子位置对应在16*16的数组中的索引
/// ```
#[frb(positional)]
#[frb(sync)]
pub fn get_array_256_index_from_board_row_col(row: u8, col: u8) -> u8 {
    assert!((1..=10).contains(&row));
    assert!((1..=9).contains(&col));
    (16 * (row + 2) + 3 + col) - 1
}

/// 将内部256字节数组的索引转换为棋盘的行列坐标
///
/// # 参数
///
/// * `index` - 内部256字节数组的索引(0-255)
///
/// # 返回值
///
/// 返回一个元组 `(row, col)`，其中:
/// - `row` 是棋盘上的行号(1-10) 由上往下数
/// - `col` 是棋盘上的列号(1-9) 由左往右数
///
/// # 示例
///
/// ```
/// let (row, col) = get_board_row_col_from_array_256_index(0x33);
/// assert_eq!((row, col), (1, 1)); // 第一个棋子位置
/// ```
pub(crate) const fn get_board_row_col_from_array_256_index(index: u8) -> (u8, u8) {
    let row = (index) / 16 - 2;
    let col = (index) % 16 - 2;
    assert!(row <= 10 && row >= 1);
    assert!(col <= 9 && col >= 1);
    (row, col)
}

#[frb(sync)]
#[allow(dead_code)]
#[frb(positional)]
pub fn convert_array_256_index_to_array_90_index(index_256: u8) -> Option<u8> {
    if (0x33..=0xcb).contains(&index_256) {
        let row = index_256 >> 4;
        let col = index_256 & 15;
        if (3..=11).contains(&col) {
            Some((row - 3) * 9 + (col - 3))
        } else {
            None
        }
    } else {
        None
    }
}

#[frb(sync)]
#[allow(dead_code)]
#[frb(positional)]
pub fn convert_array_90_index_to_array_256_index(index_90: u8) -> u8 {
    assert!(index_90 <= 89);
    let row = (index_90 / 9) + 1;
    let col = (index_90 % 9) + 1;
    get_array_256_index_from_board_row_col(row, col)
}

/// 将256数组棋盘转换为90数组棋盘
#[frb(sync)]
#[frb(positional)]
pub fn convert_board_256_to_board_90(board_256: &[u8; 256]) -> [u8; 90] {
    let mut board_90 = [0u8; 90];
    for row in 3..=12 {
        for col in 3..=11 {
            let index_256 = row * 16 + col;
            if let Some(index_90) = convert_array_256_index_to_array_90_index(index_256 as u8) {
                board_90[index_90 as usize] = board_256[index_256];
            }
        }
    }
    board_90
}

/// 将90数组棋盘转换为256数组棋盘
#[frb(sync)]
#[frb(positional)]
pub fn convert_board_90_to_board_256(board_90: &[u8; 90]) -> [u8; 256] {
    let mut board_256 = [0u8; 256];
    for (i, &piece) in board_90.iter().enumerate() {
        let index_256 = convert_array_90_index_to_array_256_index(i as u8) as usize;
        board_256[index_256] = piece;
    }
    board_256
}

/// 将棋盘数组转换为FEN字符串，既可以处理256数组，也可以处理90数组
///
/// # 参数
///
/// * `board` - 棋盘数组，可以是256长度的数组或90长度的数组
/// * `current_player` - 当前轮到哪一方行棋（红方或黑方）
///
/// # 返回值
///
/// 返回FEN格式的字符串，格式为：`<棋盘状态> <当前行棋方> - - 0 1`
/// 其中当前行棋方用'w'表示红方，'b'表示黑方
///
/// # 示例
///
/// ```
/// let fen = board_to_fen(&board, Player::Red);
/// // 返回类似 "rnbakabnr/9/1c5c1/p1p1p1p1p/9/9/P1P1P1P1P/1C5C1/9/RNBAKABNR w - - 0 1"
/// ```
///
/// # 注意
///
/// 在测试移动后的棋盘状态时，应该传入移动后轮到哪一方行棋的信息。
/// 例如，如果红方刚刚移动了棋子，那么下一步应该是黑方行棋，此时应传入 `Player::Black`。
/// 但在测试中，我们通常保持与预期FEN字符串一致的玩家参数。
#[frb(sync)]
#[frb(positional)]
pub fn board_to_fen(board: &[u8], current_player: Player) -> String {
    let fen = match board.len() {
        256 => board_256_to_fen(board),
        90 => {
            let mut board_256 = [0u8; 256];
            for (i, &piece) in board.iter().enumerate() {
                let index_256 = convert_array_90_index_to_array_256_index(i as u8) as usize;
                board_256[index_256] = piece;
            }
            board_256_to_fen(&board_256)
        }
        _ => panic!("无效的棋盘大小。期望256或90，实际得到{}", board.len()),
    };

    // 添加当前行棋方
    let player_char = match current_player {
        Player::Red => 'w',
        Player::Black => 'b',
        Player::Unknown => 'u',
    };
    format!("{} {} - - 0 1", fen, player_char)
}

/// 将256数组棋盘转换为FEN字符串
fn board_256_to_fen(board: &[u8]) -> String {
    let mut fen = String::new();
    let mut empty_count = 0;

    for row in 3..=12 {
        for col in 3..=11 {
            let index = row * 16 + col;
            let piece = board[index];
            let piece_type = SidePieceType::from_u8(piece).unwrap_or(SidePieceType::None);

            if piece_type == SidePieceType::None {
                empty_count += 1;
            } else {
                if empty_count > 0 {
                    fen.push_str(&empty_count.to_string());
                    empty_count = 0;
                }
                fen.push(piece_type_to_fen_char(piece_type));
            }
        }

        if empty_count > 0 {
            fen.push_str(&empty_count.to_string());
            empty_count = 0;
        }

        if row < 12 {
            fen.push('/');
        }
    }

    fen
}

fn piece_type_to_fen_char(piece_type: SidePieceType) -> char {
    match piece_type {
        SidePieceType::RedKing => 'K',
        SidePieceType::RedAdvisor => 'A',
        SidePieceType::RedBishop => 'B',
        SidePieceType::RedKnight => 'N',
        SidePieceType::RedRook => 'R',
        SidePieceType::RedCannon => 'C',
        SidePieceType::RedPawn => 'P',
        SidePieceType::BlackKing => 'k',
        SidePieceType::BlackAdvisor => 'a',
        SidePieceType::BlackBishop => 'b',
        SidePieceType::BlackKnight => 'n',
        SidePieceType::BlackRook => 'r',
        SidePieceType::BlackCannon => 'c',
        SidePieceType::BlackPawn => 'p',
        SidePieceType::None => panic!("不应该将空棋子转换为FEN字符"),
    }
}

#[frb(sync)]
#[frb(positional)]
pub fn fen_to_board(fen: &str) -> Result<([u8; 256], Player), String> {
    let mut board = [0u8; 256];
    let parts: Vec<&str> = fen.split_whitespace().collect();
    if parts.is_empty() {
        return Err("FEN字符串为空".to_string());
    }
    let board_fen = parts[0];
    let rows: Vec<&str> = board_fen.split('/').collect();
    if rows.len() != 10 {
        return Err(format!("FEN棋盘部分应有10行，实际为{}行", rows.len()));
    }

    let mut current_row_idx = 3; // Corresponds to board row 1 (black's back rank)

    for fen_row_str in rows {
        let mut current_col_idx = 3; // Reset for each new row. Moved declaration here.
        for ch in fen_row_str.chars() {
            if ch.is_digit(10) {
                let empty_squares = ch.to_digit(10).unwrap_or(0) as usize;
                if empty_squares == 0 || current_col_idx + empty_squares > 3 + 9 {
                    // 3 (offset) + 9 (cols)
                    return Err(format!(
                        "FEN行 '{}' 中的数字 '{}' 无效或导致列溢出",
                        fen_row_str, ch
                    ));
                }
                // No pieces are placed for empty squares, just advance the column index
                current_col_idx += empty_squares;
            } else {
                if current_col_idx >= 3 + 9 {
                    // Check before placing piece
                    return Err(format!(
                        "FEN行 '{}' 中的字符 '{}' 导致列溢出",
                        fen_row_str, ch
                    ));
                }
                let piece = fen_char_to_piece_type(ch)?; // Use ? to propagate error
                let board_array_index = current_row_idx * 16 + current_col_idx;
                board[board_array_index] = piece as u8;
                current_col_idx += 1;
            }
        }
        if current_col_idx != 3 + 9 {
            // After processing all chars in a fen_row_str, col should be at the end
            return Err(format!(
                "FEN行 '{}' 未填满9列 (当前列计数: {})",
                fen_row_str,
                current_col_idx - 3
            ));
        }
        current_row_idx += 1;
    }

    let player_str = parts
        .get(1)
        .ok_or_else(|| format!("FEN字符串 '{}' 缺少玩家标识部分", fen))?;
    let player = match *player_str {
        "w" | "r" => Player::Red,
        "b" => Player::Black,
        _ => {
            return Err(format!(
                "FEN字符串 '{}' 中存在无效的玩家标识符 '{}'，应为 'w'、'r' 或 'b'",
                fen, player_str
            ));
        }
    };

    // TODO: 校验其他FEN部分 (castling, en passant, halfmove clock, fullmove number)
    // 对于中国象棋，后面通常是 "- - 0 1"

    Ok((board, player))
}

fn fen_char_to_piece_type(ch: char) -> Result<SidePieceType, String> {
    match ch {
        'K' => Ok(SidePieceType::RedKing),
        'A' => Ok(SidePieceType::RedAdvisor),
        'B' => Ok(SidePieceType::RedBishop),
        'N' => Ok(SidePieceType::RedKnight),
        'R' => Ok(SidePieceType::RedRook),
        'C' => Ok(SidePieceType::RedCannon),
        'P' => Ok(SidePieceType::RedPawn),
        'k' => Ok(SidePieceType::BlackKing),
        'a' => Ok(SidePieceType::BlackAdvisor),
        'b' => Ok(SidePieceType::BlackBishop),
        'n' => Ok(SidePieceType::BlackKnight),
        'r' => Ok(SidePieceType::BlackRook),
        'c' => Ok(SidePieceType::BlackCannon),
        'p' => Ok(SidePieceType::BlackPawn),
        _ => Err(format!("无效的FEN字符: {ch}")),
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::chess::{GameManager, INITIAL_BOARD_ARRAY, INITIAL_BOARD_FEN}; // Removed PieceType

    #[test]
    fn test_convert_array_256_index_to_array_90_index() {
        // 测试有效的转换
        assert_eq!(convert_array_256_index_to_array_90_index(0x33), Some(0)); // 左上角
        assert_eq!(convert_array_256_index_to_array_90_index(0x3b), Some(8)); // 右上角
        assert_eq!(convert_array_256_index_to_array_90_index(0xc3), Some(81)); // 左下角
        assert_eq!(convert_array_256_index_to_array_90_index(0xcb), Some(89)); // 右下角

        // 测试无效的转换
        assert_eq!(convert_array_256_index_to_array_90_index(0x32), None); // 棋盘左上边界外的点
        assert_eq!(convert_array_256_index_to_array_90_index(0x3c), None); // 棋盘右上边界外的点
        assert_eq!(convert_array_256_index_to_array_90_index(0xc2), None); // 棋盘左下边界外的点
        assert_eq!(convert_array_256_index_to_array_90_index(0xcc), None); // 棋盘右下边界外的点
    }

    #[test]
    fn test_convert_array_90_index_to_array_256_index() {
        // 测试有效的转换
        assert_eq!(convert_array_90_index_to_array_256_index(0), 0x33); // 左上角
        assert_eq!(convert_array_90_index_to_array_256_index(8), 0x3b); // 右上角
        assert_eq!(convert_array_90_index_to_array_256_index(81), 0xc3); // 左下角
        assert_eq!(convert_array_90_index_to_array_256_index(89), 0xcb); // 右下角
    }

    #[test]
    fn test_get_array_256_index_from_board_row_col() {
        // 测试棋盘四个角落的位置
        assert_eq!(get_array_256_index_from_board_row_col(1, 1), 0x33);
        assert_eq!(get_array_256_index_from_board_row_col(1, 9), 0x3b);
        assert_eq!(get_array_256_index_from_board_row_col(10, 1), 0xc3);
        assert_eq!(get_array_256_index_from_board_row_col(10, 9), 0xcb);
    }

    #[test]
    fn test_get_board_row_col_from_array_256_index() {
        // 测试棋盘四个角落的位置
        assert_eq!(get_board_row_col_from_array_256_index(0x33), (1, 1));
        assert_eq!(get_board_row_col_from_array_256_index(0x3b), (1, 9));
        assert_eq!(get_board_row_col_from_array_256_index(0xc3), (10, 1));
        assert_eq!(get_board_row_col_from_array_256_index(0xcb), (10, 9));
    }

    #[test]
    fn test_bidirectional_conversion() {
        for row in 1..=10 {
            for col in 1..=9 {
                let index = get_array_256_index_from_board_row_col(row, col);
                let (converted_row, converted_col) = get_board_row_col_from_array_256_index(index);
                assert_eq!(
                    (row, col),
                    (converted_row, converted_col),
                    "转换失败：原始坐标 ({row}, {col})"
                );
            }
        }
    }

    #[test]
    fn test_convert_board_256_to_board_90() {
        let board_90 = convert_board_256_to_board_90(&INITIAL_BOARD_ARRAY);
        assert_eq!(board_90.len(), 90);
        // 测试四个角落
        assert_eq!(board_90[0], SidePieceType::BlackRook as u8);
        assert_eq!(board_90[8], SidePieceType::BlackRook as u8);
        assert_eq!(board_90[81], SidePieceType::RedRook as u8);
        assert_eq!(board_90[89], SidePieceType::RedRook as u8);
        // 测试双方将帅位置
        assert_eq!(board_90[4], SidePieceType::BlackKing as u8);
        assert_eq!(board_90[85], SidePieceType::RedKing as u8);
    }

    #[test]
    fn test_convert_board_90_to_board_256() {
        let board_90 = convert_board_256_to_board_90(&INITIAL_BOARD_ARRAY);
        let board_256 = convert_board_90_to_board_256(&board_90);
        assert_eq!(board_256.len(), 256);
        // 检查四个角落的棋子
        assert_eq!(board_256[0x33], SidePieceType::BlackRook as u8);
        assert_eq!(board_256[0x3b], SidePieceType::BlackRook as u8);
        assert_eq!(board_256[0xc3], SidePieceType::RedRook as u8);
        assert_eq!(board_256[0xcb], SidePieceType::RedRook as u8);
        // 检查双方将帅的位置
        assert_eq!(board_256[0x37], SidePieceType::BlackKing as u8);
        assert_eq!(board_256[0xc7], SidePieceType::RedKing as u8);
    }

    #[test]
    fn test_bidirectional_board_conversion() {
        let board_90 = convert_board_256_to_board_90(&INITIAL_BOARD_ARRAY);
        let board_256 = convert_board_90_to_board_256(&board_90);
        assert_eq!(board_256, INITIAL_BOARD_ARRAY);
    }

    #[test]
    fn test_board_256_to_fen() {
        let fen = board_to_fen(&INITIAL_BOARD_ARRAY, Player::Red);
        assert_eq!(fen, INITIAL_BOARD_FEN);

        let fen = board_to_fen(&INITIAL_BOARD_ARRAY, Player::Black);
        let fen_parts: Vec<&str> = fen.split_whitespace().collect();
        assert_eq!(
            fen_parts[0],
            "rnbakabnr/9/1c5c1/p1p1p1p1p/9/9/P1P1P1P1P/1C5C1/9/RNBAKABNR"
        );
        assert_eq!(fen_parts[1], "b");
    }

    #[test]
    fn test_board_90_to_fen() {
        let board_90 = convert_board_256_to_board_90(&INITIAL_BOARD_ARRAY);
        let fen = board_to_fen(&board_90, Player::Red);
        assert_eq!(fen, INITIAL_BOARD_FEN);

        let fen = board_to_fen(&board_90, Player::Black);
        let fen_parts: Vec<&str> = fen.split_whitespace().collect();
        assert_eq!(
            fen_parts[0],
            "rnbakabnr/9/1c5c1/p1p1p1p1p/9/9/P1P1P1P1P/1C5C1/9/RNBAKABNR"
        );
        assert_eq!(fen_parts[1], "b");
    }

    #[test]
    #[should_panic(expected = "无效的棋盘大小。期望256或90，实际得到100")]
    fn test_board_to_fen_invalid_size() {
        let invalid_board = vec![0; 100];
        board_to_fen(&invalid_board, Player::Red);
    }

    #[test]
    fn test_fen_to_board() {
        // 使用INITIAL_FEN获取棋盘
        let (board, player) =
            fen_to_board(INITIAL_BOARD_FEN).expect("INITIAL_FEN 应该能被正确解析");

        // 使用board_to_fen函数将棋盘转回FEN
        let generated_fen = board_to_fen(&board, player);

        // 检查生成的FEN是否与原始FEN相同
        assert_eq!(generated_fen, INITIAL_BOARD_FEN);
    }

    #[test]
    fn test_fen_to_board_invalid_fen_str() {
        assert!(fen_to_board("invalid fen string").is_err());
    }

    #[test]
    fn test_fen_to_board_invalid_player_char() {
        assert!(
            fen_to_board("rnbakabnr/9/1c5c1/p1p1p1p1p/9/9/P1P1P1P1P/1C5C1/9/RNBAKABNR x - - 0 1")
                .is_err()
        );
    }

    #[test]
    fn test_fen_to_board_incomplete_row() {
        assert!(
            fen_to_board("rnbakabnr/9/1c5c/p1p1p1p1p/9/9/P1P1P1P1P/1C5C1/9/RNBAKABNR w - - 0 1")
                .is_err()
        );
    }

    #[test]
    fn test_fen_to_board_too_many_rows() {
        assert!(
            fen_to_board("rnbakabnr/9/1c5c1/p1p1p1p1p/9/9/P1P1P1P1P/1C5C1/9/RNBAKABNR/9 w - - 0 1")
                .is_err()
        );
    }

    #[test]
    fn test_board_and_fen_conversion_correctnesss() {
        let random_but_valid_fen = "4k4/3P1P3/4P4/3P1P3/9/9/3p1p3/4p4/3p1p3/4K4 w - - 0 1";

        // 使用特殊布局的FEN字符串创建棋盘
        let (board, player) = fen_to_board(random_but_valid_fen).expect("Valid FEN should parse");

        // 创建使用特殊布局的GameManager
        let mut gm = GameManager::new();
        gm.board = board;
        gm.current_player = player;

        // 验证FEN字符串是否正确
        let actual_fen = board_to_fen(&gm.board, gm.current_player);
        assert_eq!(actual_fen, random_but_valid_fen);
    }
}
