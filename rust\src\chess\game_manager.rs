use flutter_rust_bridge::frb;
use num_traits::FromPrimitive;

use crate::chess::{PieceMove, get_src_dst_row_col_from_iccs_move};

use super::{
    BOARD_LEFT_UP_POS, BOARD_RIGHT_DOWN_POS, CC_ADVISOR_DELTA, CC_BISHOP_DELTA,
    CC_BISHOP_EYE_DELTA, CC_KING_DELTA, CC_KNIGHT_DELTA, CC_KNIGHT_FOOT_DELTA,
    FLIPPED_INITIAL_BOARD_ARRAY, INITIAL_BOARD_ARRAY, PieceType, Player, SidePieceType,
    get_array_256_index_from_board_row_col, get_board_row_col_from_array_256_index,
    get_iccs_move_str_from_pos, get_unside_piece_by_side_piece, is_black_piece, is_pos_in_board,
    is_pos_in_fort, is_red_piece,
};

#[frb]
#[derive(Debug, PartialEq, Eq)]
pub enum PlacementValidity {
    Valid,
    InvalidLocation(String),
    MaxPiecesReached(String),
    KingsFacing(String),
    // 移除了UnknownError，因为具体的错误类型更好
}

#[frb(opaque)]
pub struct GameManager {
    pub(crate) board: [u8; 256],
    pub current_player: Player,
    pub need_flip_for_display: bool,
}

impl GameManager {
    #[frb(sync)]
    pub fn new() -> Self {
        Self {
            board: INITIAL_BOARD_ARRAY,
            current_player: Player::Red,
            need_flip_for_display: false,
        }
    }

    #[frb(sync)]
    pub fn reset(&mut self) {
        self.board = INITIAL_BOARD_ARRAY;
        self.current_player = Player::Red;
        self.need_flip_for_display = false;
    }

    #[frb(sync)]
    pub fn load_fen(&mut self, fen_str: &str) -> Result<(), String> {
        // 步骤 a: 初步解析FEN
        let (temp_board, player_from_fen) = match super::board_utils::fen_to_board(fen_str) {
            Ok(result) => result,
            Err(e) => return Err(format!("FEN解析失败: {}", e)),
        };

        // 步骤 b: 国王有效性校验
        let mut red_king_pos: Option<u8> = None;
        let mut black_king_pos: Option<u8> = None;
        let mut red_king_count = 0;
        let mut black_king_count = 0;

        for i in BOARD_LEFT_UP_POS..=BOARD_RIGHT_DOWN_POS {
            if !is_pos_in_board(i) {
                continue;
            }
            let piece =
                SidePieceType::from_u8(temp_board[i as usize]).unwrap_or(SidePieceType::None);
            match piece {
                SidePieceType::RedKing => {
                    red_king_count += 1;
                    red_king_pos = Some(i);
                    if !is_pos_in_fort(i) {
                        return Err("红帅不在九宫内".to_string());
                    }
                    // 检查红帅是否在红方九宫（基于标准棋盘，帅在下半区）
                    let (r, _) = get_board_row_col_from_array_256_index(i);
                    if r < 6 { // 标准棋盘红方九宫行号为 8,9,10 (数组索引对应较大值)
                        // 如果此时帅的行号小，说明可能是翻转的FEN，暂时不报错，后续is_flipped会处理
                    }
                }
                SidePieceType::BlackKing => {
                    black_king_count += 1;
                    black_king_pos = Some(i);
                    if !is_pos_in_fort(i) {
                        return Err("黑将不在九宫内".to_string());
                    }
                    // 检查黑将是否在黑方九宫（基于标准棋盘，将在上半区）
                    let (r, _) = get_board_row_col_from_array_256_index(i);
                    if r > 5 { // 标准棋盘黑方九宫行号为 1,2,3 (数组索引对应较小值)
                        // 如果此时将的行号大，说明可能是翻转的FEN
                    }
                }
                _ => {}
            }
        }

        if red_king_count != 1 {
            return Err(format!("红帅数量错误，应为1，实际为{}", red_king_count));
        }
        if black_king_count != 1 {
            return Err(format!("黑将数量错误，应为1，实际为{}", black_king_count));
        }

        let rk_pos = red_king_pos.unwrap();
        let bk_pos = black_king_pos.unwrap();

        // 步骤 c: 判断FEN的原始方向
        let (red_king_row, _) = get_board_row_col_from_array_256_index(rk_pos);
        let (black_king_row, _) = get_board_row_col_from_array_256_index(bk_pos);

        let if_need_flip = red_king_row < black_king_row;

        // 步骤 d: 棋盘数据归一化 (如果需要)
        if if_need_flip {
            self.board = Self::static_center_flip_board(&temp_board);
        } else {
            self.board = temp_board;
        }

        // 再次校验国王是否在正确的九宫内（基于归一化后的棋盘）
        // 此时 self.board 已经是标准方向了
        let final_red_king_pos = self
            .try_find_king_position(Player::Red)
            .ok_or_else(|| "无法在最终棋盘找到红帅".to_string())?;
        let final_black_king_pos = self
            .try_find_king_position(Player::Black)
            .ok_or_else(|| "无法在最终棋盘找到黑将".to_string())?;

        let (frk_r, _) = get_board_row_col_from_array_256_index(final_red_king_pos);
        let (fbk_r, _) = get_board_row_col_from_array_256_index(final_black_king_pos);

        if !(8..=10).contains(&frk_r) || !is_pos_in_fort(final_red_king_pos) {
            return Err(format!("红帅最终位置({frk_r})或九宫状态不正确 (标准棋盘)"));
        }
        if !(1..=3).contains(&fbk_r) || !is_pos_in_fort(final_black_king_pos) {
            return Err(format!("黑将最终位置({fbk_r})或九宫状态不正确 (标准棋盘)"));
        }

        // 步骤 e: 更新GameManager状态
        self.current_player = player_from_fen;
        self.need_flip_for_display = if_need_flip;

        // 最终校验整个棋盘布局是否合规 (例如将帅照面等)
        // 注意：validate_full_board_layout 内部会调用 try_find_king_position，
        // 它现在会在已经归一化的 self.board 上操作。
        if let Err(e) = self.validate_full_board_layout() {
            // 如果校验失败，可能需要回滚到之前的状态或返回错误
            // 为简单起见，这里直接返回错误。可以考虑在调用load_fen前保存状态以便回滚。
            return Err(format!("FEN加载后棋盘布局校验失败: {}", e));
        }

        Ok(())
    }

    #[frb(sync)]
    pub fn clear_board(&mut self) {
        for i in 0..self.board.len() {
            if is_pos_in_board(i as u8) {
                self.board[i] = SidePieceType::None as u8;
            }
        }
        self.current_player = Player::Red;
    }

    #[frb(sync)]
    pub fn board_row_count() -> u8 {
        10
    }
    #[frb(sync)]
    pub fn board_col_count() -> u8 {
        9
    }

    #[frb(sync)]
    pub fn get_orig_board() -> [u8; 256] {
        INITIAL_BOARD_ARRAY
    }

    #[frb(sync)]
    pub fn switch_player(&mut self) {
        self.current_player = match self.current_player {
            Player::Red => Player::Black,
            Player::Black => Player::Red,
            Player::Unknown => Player::Unknown,
        };
    }

    /// 根据输入的起始终止点位（视觉坐标），基于棋子规则判断是否合法（不考虑将军、困毙等特殊情况）
    pub fn is_piece_valid_move(
        &self,
        visual_src_row: u8,
        visual_src_col: u8,
        visual_dst_row: u8,
        visual_dst_col: u8,
    ) -> bool {
        let (logical_src_row, logical_src_col) =
            self.visual_to_logical_coords(visual_src_row, visual_src_col);
        let (logical_dst_row, logical_dst_col) =
            self.visual_to_logical_coords(visual_dst_row, visual_dst_col);

        // 后续使用 logical 坐标
        let src_pos = get_array_256_index_from_board_row_col(logical_src_row, logical_src_col);
        let dst_pos = get_array_256_index_from_board_row_col(logical_dst_row, logical_dst_col);
        let valid_moves = self.get_piece_all_valid_moves(src_pos); // get_piece_all_valid_moves 使用逻辑坐标
        let move_str = get_iccs_move_str_from_pos(src_pos, dst_pos);

        valid_moves.contains(&move_str)
    }

    pub fn update_board(&mut self, visual_row: u8, visual_col: u8, piece_index: u8) {
        let (logical_row, logical_col) = self.visual_to_logical_coords(visual_row, visual_col);

        let index = get_array_256_index_from_board_row_col(logical_row, logical_col);
        self.board[index as usize] = piece_index;
    }

    /// 根据ICCS坐标执行棋子移动，不进行规则验证，不切换玩家
    ///
    /// # 参数
    ///
    /// * `iccs_move` - ICCS格式的移动字符串，例如 "a0a1"（车九进一）
    ///
    /// # 说明
    ///
    /// 此函数根据ICCS坐标移动棋子，但不会：
    /// - 验证移动是否符合规则
    /// - 检查是否轮到当前玩家行棋
    /// - 自动切换当前玩家
    /// - 检查将军、将死或困毙状态
    ///
    /// 如果需要切换玩家，请在调用此函数后手动调用 `switch_player()`。
    ///
    /// # 示例
    ///
    /// ```
    /// let mut gm = GameManager::new();
    /// gm.make_move_by_iccs("a0a1"); // 车九进一
    /// gm.switch_player(); // 切换到黑方
    /// ```
    #[frb(sync)]
    pub fn make_move_by_iccs(&mut self, iccs_move: &str) {
        let (src_row, src_col, dst_row, dst_col) = get_src_dst_row_col_from_iccs_move(iccs_move);
        let src_index = get_array_256_index_from_board_row_col(src_row, src_col);
        let dst_index = get_array_256_index_from_board_row_col(dst_row, dst_col);

        // 执行移动
        let piece_value = self.board[src_index as usize];
        self.board[dst_index as usize] = piece_value;
        self.board[src_index as usize] = SidePieceType::None as u8;
    }

    /// 校验给定类型的棋子放置在指定位置（视觉坐标 `visual_row`, `visual_col）是否符合其基本区域规则`。
    /// `visual_row`: 1-10 (从上到下), `visual_col`: 1-9 (从左到右)
    pub fn is_valid_piece_placement(
        &self,
        visual_row: u8,
        visual_col: u8,
        piece_type: SidePieceType,
    ) -> bool {
        let (logical_row, logical_col) = self.visual_to_logical_coords(visual_row, visual_col);

        // 后续使用 logical_row, logical_col
        let pos = get_array_256_index_from_board_row_col(logical_row, logical_col);

        if !is_pos_in_board(pos) {
            // is_pos_in_board 使用的是内部索引，基于标准棋盘
            return false;
        }

        // 不能放置空棋子
        if piece_type == SidePieceType::None {
            return false;
        }

        let unside_piece = get_unside_piece_by_side_piece(piece_type);
        let player = piece_type.get_side(); // 提前获取棋子所属方，多处需要

        match unside_piece {
            PieceType::King | PieceType::Advisor => {
                // 必须在九宫内，并且必须在己方半场
                is_pos_in_fort(pos) && self.is_pos_in_player_home_side(pos, player)
            }
            PieceType::Bishop => {
                // let player = piece_type.get_side(); // 已提前获取
                // 检查是否在己方半场
                if !self.is_pos_in_player_home_side(pos, player) {
                    return false;
                }
                // 检查是否在7个特定点上
                // logical_row: 1-10 (从上到下), logical_col: 1-9 (从左到右)
                // 红方底线为逻辑行10, 黑方底线为逻辑行1
                match player {
                    Player::Red => {
                        (logical_row == 10 && (logical_col == 3 || logical_col == 7))
                            || (logical_row == 8
                                && (logical_col == 1 || logical_col == 5 || logical_col == 9))
                            || (logical_row == 6 && (logical_col == 3 || logical_col == 7))
                    }
                    Player::Black => {
                        (logical_row == 1 && (logical_col == 3 || logical_col == 7))
                            || (logical_row == 3
                                && (logical_col == 1 || logical_col == 5 || logical_col == 9))
                            || (logical_row == 5 && (logical_col == 3 || logical_col == 7))
                    }
                    Player::Unknown => false,
                }
            }
            PieceType::Pawn => {
                // let player = piece_type.get_side(); // 已提前获取
                // 1. 检查是否越过初始线向己方底线移动 (基于逻辑行)
                let valid_logical_row = match piece_type {
                    SidePieceType::RedPawn => logical_row <= 7, // 红兵的逻辑行应 <= 7
                    SidePieceType::BlackPawn => logical_row >= 4, // 黑卒的逻辑行应 >= 4
                    _ => false, // Not a pawn, or invalid SidePieceType for Pawn
                };
                if !valid_logical_row {
                    return false;
                }

                // 2. 检查未过河时是否在初始列上
                // is_pos_in_player_home_side 使用内部索引 pos
                if self.is_pos_in_player_home_side(pos, player) {
                    // is_pos_in_player_home_side 使用逻辑pos
                    // 未过河，逻辑列必须是 1, 3, 5, 7, 9
                    if !(logical_col == 1
                        || logical_col == 3
                        || logical_col == 5
                        || logical_col == 7
                        || logical_col == 9)
                    {
                        return false;
                    }
                }
                // 如果过河了，则没有列限制（除了棋盘边界，已由 is_pos_in_board 检查）
                true
            }
            PieceType::Rook | PieceType::Knight | PieceType::Cannon => {
                // 车马炮只要在棋盘内即可，其数量由后续校验处理
                true
            }
            PieceType::None => {
                // get_unside_piece_by_side_piece should handle SidePieceType::None and return PieceType::None
                // This case is for completeness if unside_piece somehow becomes PieceType::None from a valid SidePieceType
                false
            }
        }
    }

    #[frb(sync)]
    pub fn place_piece_on_board(
        &mut self,
        visual_row: u8, // 1-10 (top to bottom) - visual coordinate
        visual_col: u8, // 1-9 (left to right) - visual coordinate
        piece_to_place: SidePieceType,
    ) -> PlacementValidity {
        // 0. 不能放置空棋子
        if piece_to_place == SidePieceType::None {
            return PlacementValidity::InvalidLocation("不能放置空棋子.".to_string());
        }

        let (logical_row, logical_col) = self.visual_to_logical_coords(visual_row, visual_col);

        let target_pos = get_array_256_index_from_board_row_col(logical_row, logical_col);
        let original_piece_at_target_type = self.get_side_piece_by_index(target_pos);

        // 新增：在摆谱模式下，对覆盖王棋子的逻辑进行细化
        let unside_original_at_target =
            get_unside_piece_by_side_piece(original_piece_at_target_type);
        let unside_piece_to_place = get_unside_piece_by_side_piece(piece_to_place);

        if unside_original_at_target == PieceType::King {
            // 如果目标位置是王
            if unside_piece_to_place != PieceType::King {
                // 并且尝试用非王棋子覆盖
                let player_of_original_king = original_piece_at_target_type.get_side();
                // 在尝试放置之前，棋盘尚未改变，所以直接计数当前棋盘上的王
                if player_of_original_king != Player::Unknown {
                    let king_count = self.count_player_kings(player_of_original_king);
                    if king_count <= 1 {
                        return PlacementValidity::InvalidLocation(
                            "不能用其他棋子覆盖最后一个将/帅.".to_string(),
                        );
                    }
                    // 如果king_count > 1，则允许非王棋子覆盖其中一个王
                }
            }
            // 如果是用王覆盖王 (unside_piece_to_place == PieceType::King)，则允许，后续会有数量和照面校验
        }

        // 1. 基础位置校验 (is_valid_piece_placement 现在也期望视觉坐标, 但我们已经转换为逻辑坐标了)
        // 因此，我们应该调用一个期望逻辑坐标的is_valid_piece_placement版本，或者直接在这里用逻辑坐标进行校验。
        // 为了避免API混乱，我们假设 is_valid_piece_placement 已经被修改为接收视觉坐标。
        // 但由于我们已经转换了，所以这里调用时要传 visual_row, visual_col
        // 或者，更好的方式是 is_valid_piece_placement 内部做转换，或者我们创建一个 private _is_valid_piece_placement_logical
        // 为简单起见，暂时直接使用转换后的 logical_row, logical_col 进行后续判断，并调整 is_valid_piece_placement 的调用
        // **修正：is_valid_piece_placement 应该接收视觉坐标，所以这里直接调用它**
        if !self.is_valid_piece_placement(visual_row, visual_col, piece_to_place) {
            return PlacementValidity::InvalidLocation(format!(
                "{} 不能放置在 ({}, {}).",
                piece_to_place.get_chinese_name(),
                visual_row, // 错误信息中使用用户看到的视觉坐标
                visual_col
            ));
        }

        // 2. 尝试性放置棋子 (使用逻辑坐标的 target_pos)
        let original_piece_at_target = self.board[target_pos as usize];
        self.board[target_pos as usize] = piece_to_place as u8;

        // 3. 数量校验
        let player_of_piece = if is_red_piece(piece_to_place) {
            Player::Red
        } else {
            Player::Black
        };
        let unside_piece_type = get_unside_piece_by_side_piece(piece_to_place);

        // 统计棋盘上已有的同类棋子数量 (在放置新棋子之前)
        // 为了准确计数，我们应该在“假设放置前”计数，或者从当前计数中排除新放的这一个
        // 简单起见，我们先计算当前棋盘上的数量，如果 piece_to_place 替换了同类棋子，数量不变。
        // 如果 piece_to_place 替换了不同类棋子或空位，则数量会增加。
        // 更准确的做法是：
        // 或者修改 count_pieces_on_board 以接受 board 引用。
        // 当前 count_pieces_on_board 使用 self.board，所以它会计算包含新放置棋子的数量。
        // 如果 PieceType 不是 Copy，则在将其传递给 count_pieces_on_board 后，其所有权会转移。
        // 为了确保 unside_piece_type 在后续的 match 语句中仍然可用，我们传递其引用。
        // 这要求 count_pieces_on_board 接受 &PieceType，并且 PieceType 实现 PartialEq (它已经派生了)。
        let current_count = self.count_pieces_on_board(player_of_piece, &unside_piece_type);

        // 使用 unside_piece_type.get_max_count() 获取正确的上限
        let max_allowed = unside_piece_type.get_max_count();

        if current_count > max_allowed {
            self.board[target_pos as usize] = original_piece_at_target; // 回滚
            // 使用 piece_to_place (SidePieceType) 的 get_chinese_name()，它通常是 Copy 或者易于获取。
            // SidePieceType::get_chinese_name() 返回的是带颜色的名称，例如“红兵”。
            return PlacementValidity::MaxPiecesReached(format!(
                "{} 数量超过上限 (当前: {}, 上限: {}).",
                piece_to_place.get_chinese_name(),
                current_count,
                max_allowed
            ));
        }

        // 如果放置的是帅/将，并且棋盘上已经有一个同色的帅/将 (current_count 会是2)
        // 这个逻辑已包含在上面的 current_count > max_allowed (1 for King)

        // 4. 新增：兵（卒）在己方阵地同列唯一性校验
        if unside_piece_type == PieceType::Pawn {
            let mut pawns_in_col_at_home_side = 0;
            // 遍历与新放置兵（卒）同列的所有行 (使用逻辑列 logical_col)
            for r_check in 1..=Self::board_row_count() {
                // r_check 是逻辑行
                let current_pos_in_col =
                    get_array_256_index_from_board_row_col(r_check, logical_col);
                let piece_at_current_pos = self.get_side_piece_by_index(current_pos_in_col);

                if piece_at_current_pos == piece_to_place {
                    // 确保是同类型的兵/卒
                    // 检查此兵/卒是否在己方阵地
                    if self.is_pos_in_player_home_side(current_pos_in_col, player_of_piece) {
                        pawns_in_col_at_home_side += 1;
                    }
                } else if get_unside_piece_by_side_piece(piece_at_current_pos) == PieceType::Pawn
                    && piece_at_current_pos.get_side() == player_of_piece
                {
                    // 检查棋盘上已存在的、与正在放置的兵/卒同色的其他兵/卒
                    if self.is_pos_in_player_home_side(current_pos_in_col, player_of_piece) {
                        pawns_in_col_at_home_side += 1;
                    }
                }
            }

            if pawns_in_col_at_home_side > 1 {
                self.board[target_pos as usize] = original_piece_at_target; // 回滚
                return PlacementValidity::InvalidLocation(format!(
                    "兵/卒在己方阵地时，同列不能超过一个 (列: {}, 己方阵地同列兵数: {}).",
                    visual_col,
                    pawns_in_col_at_home_side // 错误信息中使用视觉列
                ));
            }
        }

        // 5. 将帅照面校验
        if self.are_kings_facing() {
            self.board[target_pos as usize] = original_piece_at_target; // 回滚
            return PlacementValidity::KingsFacing("将帅照面，非法局面.".to_string());
        }

        // 如果所有校验通过,棋子已在 self.board[target_pos as usize] 更新
        PlacementValidity::Valid
    }

    // 用于测试随机选择一个棋子走法（不考虑将军、困毙等情况）
    pub fn get_random_legal_move(&self) -> Option<PieceMove> {
        let mut all_moves = Vec::new();
        for src_pos in BOARD_LEFT_UP_POS..=BOARD_RIGHT_DOWN_POS {
            let piece = self.get_side_piece_by_index(src_pos);
            if (self.current_player == Player::Red && is_red_piece(piece))
                || (self.current_player == Player::Black && is_black_piece(piece))
            {
                let valid_moves = self.get_piece_all_valid_moves(src_pos);
                all_moves.extend(valid_moves);
            }
        }

        let piece_moves: Vec<PieceMove> = all_moves
            .iter()
            .map(|m| PieceMove::from_iccs(m, self))
            .collect();

        // 按照棋子类型对中文走法进行排序
        let mut sorted_piece_moves = piece_moves.clone();
        sorted_piece_moves.sort_by(|a, b| {
            let a_piece = a.chinese.chars().next().unwrap();
            let b_piece = b.chinese.chars().next().unwrap();
            if a_piece == b_piece {
                a.chinese.cmp(&b.chinese)
            } else {
                a_piece.cmp(&b_piece)
            }
        });

        println!(
            "{}可选走法(共{}种): {:?}",
            self.current_player,
            sorted_piece_moves.len(),
            sorted_piece_moves
                .iter()
                .map(|m| format!("{}({})", m.chinese, m.iccs))
                .collect::<Vec<_>>()
        );

        if piece_moves.is_empty() {
            None
        } else {
            use rand::seq::SliceRandom;
            let mut rng = rand::thread_rng();
            piece_moves.choose(&mut rng).cloned()
        }
    }

    /// 统计棋盘上指定玩家的特定类型棋子的数量
    fn count_pieces_on_board(&self, player_to_check: Player, piece_to_count: &PieceType) -> u8 {
        let mut count = 0;
        for i in BOARD_LEFT_UP_POS..=BOARD_RIGHT_DOWN_POS {
            // 确保只检查棋盘内的点, is_pos_in_board 使用的是内部索引
            if !is_pos_in_board(i) {
                continue;
            }
            let side_piece = self.get_side_piece_by_index(i);
            if side_piece == SidePieceType::None {
                continue;
            }

            let current_piece_player = if is_red_piece(side_piece) {
                Player::Red
            } else {
                Player::Black
            };

            if current_piece_player == player_to_check
                && get_unside_piece_by_side_piece(side_piece) == *piece_to_count
            {
                // 解引用以与 PieceType 值比较
                count += 1;
            }
        }
        count
    }

    /// 尝试找到指定玩家的王，如果找不到则返回 None
    fn try_find_king_position(&self, player: Player) -> Option<u8> {
        let king_side_piece = match player {
            Player::Red => SidePieceType::RedKing,
            Player::Black => SidePieceType::BlackKing,
            Player::Unknown => return None,
        };
        (BOARD_LEFT_UP_POS..=BOARD_RIGHT_DOWN_POS).find(|&pos| {
            is_pos_in_board(pos) && self.get_side_piece_by_index(pos) == king_side_piece
        })
    }

    /// 检查将帅是否照面
    #[frb(sync)]
    pub fn are_kings_facing(&self) -> bool {
        let red_king_pos_opt = self.try_find_king_position(Player::Red);
        let black_king_pos_opt = self.try_find_king_position(Player::Black);

        if let (Some(red_king_pos), Some(black_king_pos)) = (red_king_pos_opt, black_king_pos_opt) {
            let (_red_king_row_idx, red_king_col_idx) =
                get_board_row_col_from_array_256_index(red_king_pos);
            let (_black_king_row_idx, black_king_col_idx) =
                get_board_row_col_from_array_256_index(black_king_pos);

            if red_king_col_idx != black_king_col_idx {
                return false;
            }

            let start_pos_check = if black_king_pos < red_king_pos {
                black_king_pos
            } else {
                red_king_pos
            };
            let end_pos_check = if black_king_pos < red_king_pos {
                red_king_pos
            } else {
                black_king_pos
            };

            let mut current_check_pos = start_pos_check + 16;
            while current_check_pos < end_pos_check {
                let piece_in_between = self.get_side_piece_by_index(current_check_pos);
                if piece_in_between != SidePieceType::None {
                    return false;
                }
                current_check_pos += 16;
            }
            true // 两个王都在，在同一列，且中间无子
        } else {
            false // 至少一个王不在棋盘上，不可能照面
        }
    }

    pub(crate) fn get_board_fen(&self) -> String {
        super::board_utils::board_to_fen(&self.board, self.current_player)
    }

    /// 获取当前逻辑视角下的棋盘数组（不改变状态）。
    pub fn get_display_board(&self) -> [u8; 256] {
        if self.need_flip_for_display {
            Self::static_center_flip_board(&self.board)
        } else {
            self.board // 返回副本
        }
    }

    pub fn get_display_board_fen(&self) -> String {
        super::board_utils::board_to_fen(&self.get_display_board(), self.current_player)
    }

    // 辅助方法用于坐标转换
    const fn visual_to_logical_coords(&self, visual_row: u8, visual_col: u8) -> (u8, u8) {
        if self.need_flip_for_display {
            (11 - visual_row, 10 - visual_col)
        } else {
            (visual_row, visual_col)
        }
    }

    const fn logical_to_visual_coords(&self, logical_row: u8, logical_col: u8) -> (u8, u8) {
        if self.need_flip_for_display {
            (11 - logical_row, 10 - logical_col)
        } else {
            (logical_row, logical_col)
        }
    }
}

impl GameManager {
    /// 本函数一次仅检查一个方向的位置
    fn is_pos_stuck(&self, src_pos: u8, stuck_offset: i32) -> bool {
        let each_stuck_pos = (i32::from(src_pos) + stuck_offset) as u8;
        let piece = self.get_side_piece_by_index(each_stuck_pos);
        if piece != SidePieceType::None {
            return true;
        }
        false
    }

    /// 格子水平镜像---即根据红黑方"前进"一格的16进制表示模式下的index
    fn squre_forward_board(&self, src_pos: u8) -> u8 {
        if self.current_player == Player::Unknown {
            panic!("出现了未知的player")
        } else {
            let player_num = self.current_player as u8;
            src_pos - 16 + (player_num << 5)
        }
    }

    /// 检查目标位置的棋子是否与源棋子属于同一方
    fn is_same_side_piece(&self, src_piece: SidePieceType, dst_pos: u8) -> bool {
        let dst_piece = self.get_side_piece_by_index(dst_pos);
        if dst_piece == SidePieceType::None {
            return false;
        }
        (is_red_piece(src_piece) && is_red_piece(dst_piece))
            || (is_black_piece(src_piece) && is_black_piece(dst_piece))
    }

    /// 进而返回红黑标记(红子返回8，黑子返回16)
    fn get_piece_offset_tag(&self) -> u8 {
        let player_index = match self.current_player {
            Player::Red => 0,
            Player::Black => 1,
            Player::Unknown => panic!("玩家标识符为Unkown！"),
        };
        8 + (player_index << 3)
    }

    /// 根据位置返回其所在位置棋子的所有合法走法，以ICCS表示
    pub(crate) fn get_piece_all_valid_moves(
        &self,
        src_pos: u8, //被查棋子所在的位置
    ) -> Vec<String> {
        let mut valid_move_vec = Vec::new();
        let src_piece = self.get_side_piece_by_index(src_pos);

        // 如果位置上没有棋子，直接返回空列表
        if src_piece == SidePieceType::None {
            return valid_move_vec;
        }

        let unside_src_piece = get_unside_piece_by_side_piece(src_piece);
        match unside_src_piece {
            PieceType::King => {
                for offset in CC_KING_DELTA {
                    // 检查是否在九宫格内
                    let dst_pos_to_check = (i32::from(src_pos) + offset) as u8;
                    if !is_pos_in_fort(dst_pos_to_check) {
                        continue;
                    }
                    // 检查目标位置是否为自身棋，是的话肯定不合理
                    if !self.is_same_side_piece(src_piece, dst_pos_to_check) {
                        let move_str = get_iccs_move_str_from_pos(src_pos, dst_pos_to_check);
                        valid_move_vec.push(move_str);
                    }
                }
            }
            PieceType::Advisor => {
                for offset in CC_ADVISOR_DELTA {
                    // 检查是否在九宫格内
                    let dst_pos_to_check = (i32::from(src_pos) + offset) as u8;
                    if !is_pos_in_fort(dst_pos_to_check) {
                        continue;
                    }
                    // 检查目标位置是否为自身棋子，是的话肯定不合理
                    if !self.is_same_side_piece(src_piece, dst_pos_to_check) {
                        let move_str = get_iccs_move_str_from_pos(src_pos, dst_pos_to_check);
                        valid_move_vec.push(move_str);
                    }
                }
            }
            PieceType::Bishop => {
                // 确定棋子所属的玩家
                let piece_player = if is_red_piece(src_piece) {
                    Player::Red
                } else if is_black_piece(src_piece) {
                    Player::Black
                } else {
                    panic!("无法确定象/相棋子的所属方")
                };

                for (offset, eye_stuck_offset) in
                    CC_BISHOP_DELTA.iter().zip(CC_BISHOP_EYE_DELTA.iter())
                {
                    // 是否在棋盘中
                    let dst_pos_to_check = (i32::from(src_pos) + offset) as u8;
                    if !is_pos_in_board(dst_pos_to_check) {
                        continue;
                    }
                    // 是否过河了 - 使用棋子所属方而不是当前玩家
                    if !self.is_pos_in_player_home_side(dst_pos_to_check, piece_player) {
                        continue;
                    }
                    // 是否塞象眼
                    if self.is_pos_stuck(src_pos, *eye_stuck_offset) {
                        continue;
                    }
                    // 检查目标位置是否为自身棋子，是的话肯定不合理
                    if !self.is_same_side_piece(src_piece, dst_pos_to_check) {
                        let move_str = get_iccs_move_str_from_pos(src_pos, dst_pos_to_check);
                        valid_move_vec.push(move_str);
                    }
                }
            }
            PieceType::Knight => {
                for (each_offset_pair, knight_stuck_offset) in
                    CC_KNIGHT_DELTA.iter().zip(CC_KNIGHT_FOOT_DELTA.iter())
                {
                    // 是否在棋盘中
                    for each_offset in each_offset_pair {
                        let dst_pos_to_check = (i32::from(src_pos) + each_offset) as u8;
                        if !is_pos_in_board(dst_pos_to_check) {
                            continue;
                        }
                        // 是否瘪马脚
                        if self.is_pos_stuck(src_pos, *knight_stuck_offset) {
                            continue;
                        }
                        // 检查目标位置是否为自身棋子，是的话肯定不合理
                        if !self.is_same_side_piece(src_piece, dst_pos_to_check) {
                            let move_str = get_iccs_move_str_from_pos(src_pos, dst_pos_to_check);
                            valid_move_vec.push(move_str);
                        }
                    }
                }
            }
            PieceType::Rook => {
                for offset in CC_KING_DELTA {
                    let mut dst_pos_to_check = (i32::from(src_pos) + offset) as u8;

                    while is_pos_in_board(dst_pos_to_check) {
                        let dst_piece = self.get_side_piece_by_index(dst_pos_to_check);
                        if dst_piece == SidePieceType::None {
                            let move_str = get_iccs_move_str_from_pos(src_pos, dst_pos_to_check);
                            valid_move_vec.push(move_str);
                        } else {
                            // 如果目标位置有棋子，检查是否是同一方的棋子
                            if !self.is_same_side_piece(src_piece, dst_pos_to_check) {
                                let move_str =
                                    get_iccs_move_str_from_pos(src_pos, dst_pos_to_check);
                                valid_move_vec.push(move_str);
                            }
                            break;
                        }
                        dst_pos_to_check = (i32::from(dst_pos_to_check) + offset) as u8;
                    }
                }
            }
            PieceType::Cannon => {
                for offset in CC_KING_DELTA {
                    let mut dst_pos_to_check = (i32::from(src_pos) + offset) as u8;

                    // 校验仅平移时的可行招法
                    while is_pos_in_board(dst_pos_to_check) {
                        let dst_piece = self.get_side_piece_by_index(dst_pos_to_check);
                        if dst_piece == SidePieceType::None {
                            let move_str = get_iccs_move_str_from_pos(src_pos, dst_pos_to_check);
                            valid_move_vec.push(move_str);
                        } else {
                            break;
                        }
                        dst_pos_to_check = (i32::from(dst_pos_to_check) + offset) as u8;
                    }

                    // 校验隔子打炮的可行招法
                    dst_pos_to_check = (i32::from(dst_pos_to_check) + offset) as u8;
                    while is_pos_in_board(dst_pos_to_check) {
                        let dst_piece = self.get_side_piece_by_index(dst_pos_to_check);
                        if dst_piece != SidePieceType::None {
                            // 如果目标位置有棋子，检查是否是同一方的棋子
                            if !self.is_same_side_piece(src_piece, dst_pos_to_check) {
                                let move_str =
                                    get_iccs_move_str_from_pos(src_pos, dst_pos_to_check);
                                valid_move_vec.push(move_str);
                            }
                            break;
                        }
                        dst_pos_to_check = (i32::from(dst_pos_to_check) + offset) as u8;
                    }
                }
            }
            PieceType::Pawn => {
                // 确定棋子所属的玩家
                let piece_player = if is_red_piece(src_piece) {
                    Player::Red
                } else if is_black_piece(src_piece) {
                    Player::Black
                } else {
                    panic!("无法确定兵/卒棋子的所属方")
                };

                // 未过河的招法判断 - 根据棋子所属方确定前进方向
                let mut dst_pos_to_check = if piece_player == Player::Red {
                    src_pos - 16 // 红方向上移动
                } else {
                    src_pos + 16 // 黑方向下移动
                };

                if is_pos_in_board(dst_pos_to_check)
                    && !self.is_same_side_piece(src_piece, dst_pos_to_check)
                {
                    let move_str = get_iccs_move_str_from_pos(src_pos, dst_pos_to_check);
                    valid_move_vec.push(move_str);
                }

                // 过河情况的左右2个方向判断 - 使用棋子所属方判断是否过河
                if !self.is_pos_in_player_home_side(src_pos, piece_player) {
                    for horizontal_offset in [-1i32, 1] {
                        dst_pos_to_check = (i32::from(src_pos) + horizontal_offset) as u8;
                        if is_pos_in_board(dst_pos_to_check)
                            && !self.is_same_side_piece(src_piece, dst_pos_to_check)
                        {
                            let move_str = get_iccs_move_str_from_pos(src_pos, dst_pos_to_check);
                            valid_move_vec.push(move_str);
                        }
                    }
                }
            }
            _ => panic!("未知的棋子类型"),
        }

        valid_move_vec
    }

    fn is_pos_in_home_side(&self, pos: u8) -> bool {
        if self.current_player == Player::Unknown {
            panic!("出现了未知的player")
        } else {
            let player_num = self.current_player as u8;
            (pos & 0x80) != (player_num << 7)
        }
    }

    /// 检查位置是否在指定玩家的己方区域内
    fn is_pos_in_player_home_side(&self, pos: u8, player: Player) -> bool {
        if player == Player::Unknown {
            panic!("出现了未知的player")
        } else {
            let player_num = player as u8;
            (pos & 0x80) != (player_num << 7)
        }
    }

    #[frb(sync)]
    pub fn get_side_piece_by_index(&self, pos: u8) -> SidePieceType {
        SidePieceType::from_u8(self.board[pos as usize]).unwrap_or(SidePieceType::None)
    }

    #[frb(sync)]
    pub fn count_player_kings(&self, player: Player) -> u8 {
        let king_to_count = match player {
            Player::Red => SidePieceType::RedKing,
            Player::Black => SidePieceType::BlackKing,
            Player::Unknown => return 0,
        };
        let mut count = 0;
        for pos_idx in BOARD_LEFT_UP_POS..=BOARD_RIGHT_DOWN_POS {
            if is_pos_in_board(pos_idx) && self.get_side_piece_by_index(pos_idx) == king_to_count {
                count += 1;
            }
        }
        count
    }
}

// 给棋谱记录法单独开个块
impl GameManager {
    /// 检查是否是特殊布局下的兵卒，并返回特殊编号
    ///
    /// 特殊布局下的兵卒编号规则：
    /// 1. 从己方视角右到左处理各列
    /// 2. 对于每一列，从靠近敌方的兵/卒开始编号
    /// 3. 最右列靠近敌方的兵(卒)称为"一兵(卒)"
    /// 4. 同列中靠近己方的兵(卒)称为"二兵(卒)"（如果有）
    /// 5. 然后是次右列靠近敌方的兵(卒)称为"三兵(卒)"（如果有）
    /// 6. 依此类推
    fn get_special_pawn_number(
        &self,
        src_row: u8,
        src_col: u8,
        piece: SidePieceType,
    ) -> Option<String> {
        // 只处理兵和卒
        if piece != SidePieceType::RedPawn && piece != SidePieceType::BlackPawn {
            return None;
        }

        // 检查是否有多列兵/卒的特殊布局
        let mut pawn_positions = Vec::new();
        let mut col_pawn_count = [0u8; 10]; // 用于统计每列的兵/卒数量

        // 收集所有兵/卒的位置并统计每列的数量
        for pos in super::BOARD_LEFT_UP_POS..=super::BOARD_RIGHT_DOWN_POS {
            let current_piece = self.get_side_piece_by_index(pos);
            if current_piece == piece {
                let (row, col) = super::get_board_row_col_from_array_256_index(pos);
                pawn_positions.push((row, col));
                col_pawn_count[col as usize] += 1;
            }
        }

        // 如果只有5个兵/卒且都在初始位置，则不是特殊布局
        if pawn_positions.len() <= 5 {
            let is_initial_position = pawn_positions.iter().all(|(row, _)| {
                if piece == SidePieceType::RedPawn {
                    *row == 3 // 红兵初始行
                } else {
                    *row == 6 // 黑卒初始行
                }
            });

            if is_initial_position {
                return None;
            }
        }

        // 如果当前列只有一个兵/卒，则使用常规命名方式
        if col_pawn_count[src_col as usize] == 1 {
            return None;
        }

        // 筛选出需要特殊编号的兵/卒位置（即同一列有多个兵/卒的情况）
        let special_pawn_positions: Vec<(u8, u8)> = pawn_positions
            .iter()
            .filter(|(_, col)| col_pawn_count[*col as usize] > 1)
            .copied()
            .collect();

        // 对需要特殊编号的兵/卒位置进行排序
        // 1. 按列排序（从己方视角的右到左）
        // 2. 同列内按行排序（从靠近敌方到靠近己方）
        let mut sorted_special_pawns = special_pawn_positions;
        sorted_special_pawns.sort_by(|(row1, col1), (row2, col2)| {
            if piece == SidePieceType::RedPawn {
                // 红方视角：从右到左（列号从大到小），同列从上到下（行号从小到大）
                if col1 == col2 {
                    row1.cmp(row2) // 从上到下
                } else {
                    col2.cmp(col1) // 从右到左
                }
            } else {
                // 黑方视角：从右到左（列号从小到大），同列从下到上（行号从大到小）
                if col1 == col2 {
                    row2.cmp(row1) // 从下到上
                } else {
                    col1.cmp(col2) // 从右到左
                }
            }
        });

        // 查找当前棋子在排序后的位置
        let mut pawn_number = 0;
        for (i, (row, col)) in sorted_special_pawns.iter().enumerate() {
            if *row == src_row && *col == src_col {
                pawn_number = i + 1; // 编号从1开始
                break;
            }
        }

        if pawn_number == 0 {
            return None; // 没找到，说明当前棋子不需要特殊编号
        }

        // 返回特殊编号
        let chinese_numbers = ["一", "二", "三", "四", "五", "六", "七", "八", "九"];
        if pawn_number <= 9 {
            Some(chinese_numbers[pawn_number - 1].to_string())
        } else {
            None // 编号超出范围，不应该发生
        }
    }

    pub fn iccs_move_to_chinese_move(&self, iccs_move: &str) -> String {
        let (src_row, src_col, dst_row, dst_col) = get_src_dst_row_col_from_iccs_move(iccs_move);

        let board_index = get_array_256_index_from_board_row_col(src_row, src_col);
        let piece = self.get_side_piece_by_index(board_index);
        let piece_name = piece.get_chinese_name();

        let chinese_numbers = ["一", "二", "三", "四", "五", "六", "七", "八", "九"];

        // 检查是否是特殊布局下的兵卒
        let special_pawn_number = self.get_special_pawn_number(src_row, src_col, piece);

        let src_col_char: String = if let Some(special_number) = special_pawn_number.clone() {
            // 使用特殊编号
            special_number
        } else if is_red_piece(piece) {
            // 普通红方棋子
            chinese_numbers[9 - src_col as usize].into()
        } else {
            // 普通黑方棋子
            src_col.to_string()
        };

        let (direction, dst) = if src_row == dst_row {
            // 水平移动
            (
                "平",
                if is_red_piece(piece) {
                    chinese_numbers[9 - dst_col as usize].to_string()
                } else {
                    dst_col.to_string()
                },
            )
        } else if (is_red_piece(piece) && src_row > dst_row)
            || (!is_red_piece(piece) && src_row < dst_row)
        {
            // 向前移动
            (
                "进",
                if piece == SidePieceType::RedKnight
                    || piece == SidePieceType::BlackKnight
                    || piece == SidePieceType::RedAdvisor
                    || piece == SidePieceType::BlackAdvisor
                    || piece == SidePieceType::RedBishop
                    || piece == SidePieceType::BlackBishop
                {
                    // 马、士、相的目标位置用列号表示
                    if is_red_piece(piece) {
                        chinese_numbers[9 - dst_col as usize].to_string()
                    } else {
                        dst_col.to_string()
                    }
                } else if is_red_piece(piece) {
                    // 红方其他棋子（车、炮、兵）的目标位置用步数表示
                    chinese_numbers[(src_row - dst_row) as usize - 1].to_string()
                } else {
                    // 黑方其他棋子（车、炮、卒）的目标位置用步数表示
                    (dst_row - src_row).to_string()
                },
            )
        } else {
            // 向后移动
            (
                "退",
                if piece == SidePieceType::RedKnight
                    || piece == SidePieceType::BlackKnight
                    || piece == SidePieceType::RedAdvisor
                    || piece == SidePieceType::BlackAdvisor
                    || piece == SidePieceType::RedBishop
                    || piece == SidePieceType::BlackBishop
                {
                    // 马、士、相的目标位置用列号表示
                    if is_red_piece(piece) {
                        chinese_numbers[9 - dst_col as usize].to_string()
                    } else {
                        dst_col.to_string()
                    }
                } else if is_red_piece(piece) {
                    // 红方其他棋子（车、炮、兵）的目标位置用步数表示
                    chinese_numbers[(dst_row - src_row) as usize - 1].to_string()
                } else {
                    // 黑方其他棋子（车、炮、卒）的目标位置用步数表示
                    (src_row - dst_row).to_string()
                },
            )
        };

        // 对于特殊布局下的兵卒，需要在棋子名称前加上编号
        if let Some(special_number) = special_pawn_number {
            format!("{special_number}{piece_name}{direction}{dst}")
        } else {
            format!("{piece_name}{src_col_char}{direction}{dst}")
        }
    }
}

impl GameManager {
    pub fn is_current_player_winning(&self) -> bool {
        // 检查当前玩家是否有任何棋子可以直接吃掉对方的将/帅
        let anti_side_player = self.current_player.opposite();
        let opponent_king_pos = self.find_king_position(anti_side_player);
        for src_pos in BOARD_LEFT_UP_POS..=BOARD_RIGHT_DOWN_POS {
            let piece = self.get_side_piece_by_index(src_pos);
            if (self.current_player == Player::Red && is_red_piece(piece))
                || (self.current_player == Player::Black && is_black_piece(piece))
            {
                let current_player_valid_moves = self.get_piece_all_valid_moves(src_pos);

                for move_str in current_player_valid_moves {
                    let (_, _, dst_row, dst_col) = get_src_dst_row_col_from_iccs_move(&move_str);
                    let dst_pos = get_array_256_index_from_board_row_col(dst_row, dst_col);

                    if dst_pos == opponent_king_pos {
                        return true; // 当前玩家可以直接吃掉对方的将/帅，获胜
                    }
                }
            }
        }

        // 检查对方玩家是否被将死或困毙
        self.is_the_player_king_being_killed_or_trapped(anti_side_player)
    }

    pub fn is_current_player_losing(&self) -> bool {
        // 注意：这个循环既可检查当前玩家是否被将死，也可检查是否被困毙
        self.is_the_player_king_being_killed_or_trapped(self.current_player)
    }

    /// 检查`player`玩家是否被将死或困毙
    fn is_the_player_king_being_killed_or_trapped(&self, player: Player) -> bool {
        for src_pos in BOARD_LEFT_UP_POS..=BOARD_RIGHT_DOWN_POS {
            let piece = self.get_side_piece_by_index(src_pos);
            if (player == Player::Red && is_red_piece(piece))
                || (player == Player::Black && is_black_piece(piece))
            {
                let player_valid_moves = self.get_piece_all_valid_moves(src_pos);
                for move_str in player_valid_moves {
                    let (_, _, dst_row, dst_col) = get_src_dst_row_col_from_iccs_move(&move_str);
                    let dst_pos = get_array_256_index_from_board_row_col(dst_row, dst_col);

                    // 模拟移动
                    let mut temp_board = self.board;
                    temp_board[dst_pos as usize] = temp_board[src_pos as usize];
                    temp_board[src_pos as usize] = SidePieceType::None as u8;

                    // 检查移动后是否仍然被将军
                    if !self.is_position_under_attack_with_custom_board(
                        self.find_king_position(player),
                        player,
                        &temp_board,
                    ) {
                        return false; // 找到一个合法移动，游戏未结束
                    }
                }
            }
        }
        true
    }

    fn find_king_position(&self, player: Player) -> u8 {
        let king_side_piece = match player {
            Player::Red => SidePieceType::RedKing,
            Player::Black => SidePieceType::BlackKing,
            Player::Unknown => panic!("找不到将帅棋子"),
        };

        for pos in BOARD_LEFT_UP_POS..=BOARD_RIGHT_DOWN_POS {
            if self.get_side_piece_by_index(pos) == king_side_piece {
                return pos;
            }
        }
        panic!("棋盘上找不到将/帅");
    }

    /// 由于有时需要用到临时的board，所以参数`board`不能省略
    fn is_position_under_attack_with_custom_board(
        &self,
        pos: u8,
        defender: Player,
        board: &[u8; 256],
    ) -> bool {
        let attacker = match defender {
            Player::Red => Player::Black,
            Player::Black => Player::Red,
            Player::Unknown => panic!("未知玩家"),
        };

        for src_pos in BOARD_LEFT_UP_POS..=BOARD_RIGHT_DOWN_POS {
            let piece =
                SidePieceType::from_u8(board[src_pos as usize]).unwrap_or(SidePieceType::None);
            if (attacker == Player::Red && is_red_piece(piece))
                || (attacker == Player::Black && is_black_piece(piece))
            {
                let attacker_valid_moves = self.get_piece_all_valid_moves(src_pos);
                for move_str in attacker_valid_moves {
                    let (_, _, dst_row, dst_col) = get_src_dst_row_col_from_iccs_move(&move_str);
                    let dst_pos = get_array_256_index_from_board_row_col(dst_row, dst_col);
                    if dst_pos == pos {
                        return true;
                    }
                }
            }
        }
        false
    }

    #[frb(sync)]
    pub fn is_valid_setup_for_game_start(&self) -> bool {
        let red_king_exists = self.try_find_king_position(Player::Red).is_some();
        let black_king_exists = self.try_find_king_position(Player::Black).is_some();
        red_king_exists && black_king_exists
    }

    /// 全面校验当前棋盘布局是否合规，用于退出摆谱模式等场景。
    /// 返回 Ok(()) 表示合规，Err(String) 表示不合规及其原因。
    #[frb(sync)]
    pub fn validate_full_board_layout(&self) -> Result<(), String> {
        // 1. 确保双方将帅存在 (这是游戏开始的基础)
        if !self.is_valid_setup_for_game_start() {
            return Err("棋盘上必须包含双方将帅".to_string());
        }

        // 2. 检查将帅是否照面
        if self.are_kings_facing() {
            return Err("将帅照面，非法局面".to_string());
        }

        // 3. 遍历棋盘上的所有棋子，进行个体校验和数量统计
        let mut piece_counts: std::collections::HashMap<SidePieceType, u8> =
            std::collections::HashMap::new();
        let mut red_pawns_at_home_cols: std::collections::HashMap<u8, u8> =
            std::collections::HashMap::new();
        let mut black_pawns_at_home_cols: std::collections::HashMap<u8, u8> =
            std::collections::HashMap::new();

        for pos_idx in BOARD_LEFT_UP_POS..=BOARD_RIGHT_DOWN_POS {
            if !is_pos_in_board(pos_idx) {
                continue;
            }
            let piece = self.get_side_piece_by_index(pos_idx);
            if piece == SidePieceType::None {
                continue;
            }

            let (row, col) = get_board_row_col_from_array_256_index(pos_idx);

            // 3.1 单个棋子位置合规性校验
            let (visual_row_to_check, visual_col_to_check) =
                self.logical_to_visual_coords(row, col);
            if !self.is_valid_piece_placement(visual_row_to_check, visual_col_to_check, piece) {
                return Err(format!(
                    "{} 不能放置在 ({}, {})",
                    piece.get_chinese_name(),
                    visual_row_to_check, // 使用视觉坐标报告错误
                    visual_col_to_check
                ));
            }

            // 3.2 统计棋子数量
            *piece_counts.entry(piece).or_insert(0) += 1;

            // 3.3 统计兵/卒在己方阵地同列数量
            let unside_piece = get_unside_piece_by_side_piece(piece);
            if unside_piece == PieceType::Pawn {
                let player = piece.get_side();
                if self.is_pos_in_player_home_side(pos_idx, player) {
                    if player == Player::Red {
                        *red_pawns_at_home_cols.entry(col).or_insert(0) += 1;
                    } else {
                        *black_pawns_at_home_cols.entry(col).or_insert(0) += 1;
                    }
                }
            }
        }

        // 3.4 校验棋子数量上限
        for (piece, count) in piece_counts {
            let unside_piece = get_unside_piece_by_side_piece(piece);
            let max_allowed = unside_piece.get_max_count();
            if count > max_allowed {
                return Err(format!(
                    "{} 数量超过上限 (当前: {}, 上限: {})",
                    piece.get_chinese_name(),
                    count,
                    max_allowed
                ));
            }
        }

        // 3.5 校验兵/卒在己方阵地同列唯一性
        for (col, count) in red_pawns_at_home_cols {
            if count > 1 {
                return Err(format!(
                    "红兵在己方阵地的第 {} 列数量超过一个 (共 {} 个)",
                    col, count
                ));
            }
        }
        for (col, count) in black_pawns_at_home_cols {
            if count > 1 {
                return Err(format!(
                    "黑卒在己方阵地的第 {} 列数量超过一个 (共 {} 个)",
                    col, count
                ));
            }
        }

        Ok(())
    }

    /// 交换棋盘上所有棋子的红黑属性。
    /// 例如，原位置的红车变为黑车，黑马变为红马...
    /// 注意：此方法仅交换棋子颜色，不改变棋盘的翻转状态，不切换当前玩家，但内部board仍需保证黑上红下
    pub fn exchange_pieces(&mut self) {
        for i in 0..self.board.len() {
            let piece_u8 = self.board[i];
            if piece_u8 == 0 {
                continue; // 跳过空位 (SidePieceType::None as u8)
            }

            if let Some(piece_type) = SidePieceType::from_u8(piece_u8) {
                let exchanged_piece_u8 = match piece_type {
                    SidePieceType::RedKing => SidePieceType::BlackKing as u8,
                    SidePieceType::RedAdvisor => SidePieceType::BlackAdvisor as u8,
                    SidePieceType::RedBishop => SidePieceType::BlackBishop as u8,
                    SidePieceType::RedKnight => SidePieceType::BlackKnight as u8,
                    SidePieceType::RedRook => SidePieceType::BlackRook as u8,
                    SidePieceType::RedCannon => SidePieceType::BlackCannon as u8,
                    SidePieceType::RedPawn => SidePieceType::BlackPawn as u8,
                    SidePieceType::BlackKing => SidePieceType::RedKing as u8,
                    SidePieceType::BlackAdvisor => SidePieceType::RedAdvisor as u8,
                    SidePieceType::BlackBishop => SidePieceType::RedBishop as u8,
                    SidePieceType::BlackKnight => SidePieceType::RedKnight as u8,
                    SidePieceType::BlackRook => SidePieceType::RedRook as u8,
                    SidePieceType::BlackCannon => SidePieceType::RedCannon as u8,
                    SidePieceType::BlackPawn => SidePieceType::RedPawn as u8,
                    SidePieceType::None => unreachable!("不该触及这里"), // 应该被 piece_u8 == 0 捕获
                };
                self.board[i] = exchanged_piece_u8;
            }
        }

        // 交换棋子后，棋盘会变成“红上黑下”的情况，所以作为内部字段board需要翻转下
        self.board = Self::static_center_flip_board(&self.board);
    }

    // 新增静态辅助函数
    fn static_center_flip_board(board_to_flip: &[u8; 256]) -> [u8; 256] {
        let mut flipped_board = [0u8; 256];
        // Initialize flipped_board, preserving borders and clearing playable area
        for i in 0..256 {
            if is_pos_in_board(i as u8) {
                flipped_board[i] = SidePieceType::None as u8;
            } else {
                // Copy border/out-of-bounds markers from the input board
                flipped_board[i] = board_to_flip[i];
            }
        }

        for r_orig in 1..=10 {
            // Iterate over rows of the original board_to_flip
            for c_orig in 1..=9 {
                // Iterate over columns
                let pos_orig = get_array_256_index_from_board_row_col(r_orig, c_orig);
                let piece = board_to_flip[pos_orig as usize];
                if piece != SidePieceType::None as u8 {
                    let r_flipped = 11 - r_orig;
                    let c_flipped = 10 - c_orig;
                    let pos_flipped = get_array_256_index_from_board_row_col(r_flipped, c_flipped);
                    flipped_board[pos_flipped as usize] = piece;
                }
            }
        }
        flipped_board
    }
}

#[cfg(test)]
mod tests {
    use crate::chess::{
        FLIPPED_INITIAL_BOARD_ARRAY, FLIPPED_INITIAL_BOARD_FEN, GameManager, INITIAL_BOARD_ARRAY,
        INITIAL_BOARD_FEN, Player, SidePieceType, board_to_fen, fen_to_board,
        get_array_256_index_from_board_row_col,
    };

    #[test]
    fn test_exchange_pieces_and_player_and_flip_for_initial_board() {
        let mut gm = GameManager::new();
        gm.exchange_pieces();

        // 交换后的检测关键字段
        assert_eq!(gm.current_player, Player::Red); // 玩家不变
        assert_eq!(gm.need_flip_for_display, false); // 翻转状态不变

        // 检测内部的board
        assert_eq!(gm.board, INITIAL_BOARD_ARRAY);

        // 检测展示的board和fen
        assert_eq!(gm.get_display_board(), INITIAL_BOARD_ARRAY);
        assert_eq!(gm.get_display_board_fen(), INITIAL_BOARD_FEN);
    }

    #[test]
    fn test_exchange_pieces_and_player_and_flip_for_special_board() {
        let mut gm = GameManager::new();
        gm.load_fen("4K4/9/9/9/9/9/3n1c3/4P4/3r1N3/4k4 b - - 0 1")
            .unwrap();

        // 交换前的检测关键字段
        assert_eq!(gm.current_player, Player::Black);
        assert_eq!(gm.need_flip_for_display, true); // 这个原始局面是黑方在下，所以为true

        gm.exchange_pieces();

        // 交换后的检测关键字段
        assert_eq!(gm.current_player, Player::Black); // 不变
        assert_eq!(gm.need_flip_for_display, true); // 不变

        // 检测内部的fen
        let expected_fen = "4k4/9/9/9/9/9/3N1C3/4p4/3R1n3/4K4 b - - 0 1";
        assert_eq!(gm.get_board_fen(), expected_fen);

        // 检测展示的fen
        let expected_fen = "4K4/3n1R3/4p4/3C1N3/9/9/9/9/9/4k4 b - - 0 1";
        assert_eq!(gm.get_display_board_fen(), expected_fen);
    }

    #[test]
    fn test_load_fen_with_normal_orientation() {
        let mut gm = GameManager::new();
        let fen = "5k3/9/9/9/9/9/9/9/9/4K4 w - - 0 1"; // 黑将k在第一部分，红帅K在最后，标准方向
        let load_result = gm.load_fen(fen);
        assert!(
            load_result.is_ok(),
            "FEN '{}' 应该能被成功加载，错误: {:?}",
            fen,
            load_result.err()
        );
        assert!(
            !gm.need_flip_for_display,
            "对于FEN '{fen}'，棋盘不应该被标记为需要翻转"
        );
        assert_eq!(
            gm.current_player,
            Player::Red,
            "对于FEN '{fen}'，走子方应该是红方"
        );

        // 验证棋子位置 (基于FEN，k在棋盘第1行第5列，K在第10行第5列)
        let black_king_pos = get_array_256_index_from_board_row_col(1, 6);
        let red_king_pos = get_array_256_index_from_board_row_col(10, 5);

        assert_eq!(
            gm.board[black_king_pos as usize],
            SidePieceType::BlackKing as u8,
            "黑将位置不正确"
        );
        assert_eq!(
            gm.board[red_king_pos as usize],
            SidePieceType::RedKing as u8,
            "红帅位置不正确"
        );
    }

    #[test]
    fn test_load_fen_with_flipped_orientation() {
        let mut gm = GameManager::new();
        // 这个FEN是红上黑下: 红帅K在第一部分，黑将k在最后
        let fen = "4K4/9/9/9/9/9/9/9/9/3k5 w - - 0 1";
        let load_result = gm.load_fen(fen);
        assert!(
            load_result.is_ok(),
            "FEN '{}' 应该能被成功加载，错误: {:?}",
            fen,
            load_result.err()
        );
        assert!(
            gm.need_flip_for_display,
            "对于FEN '{fen}'，棋盘不应该被标记为需要翻转"
        );
        assert_eq!(
            gm.current_player,
            Player::Red,
            "对于FEN '{fen}'，走子方应该是红方"
        );

        // 验证gm内部的棋盘(board)是否正确翻转了
        let gened_fen = board_to_fen(&gm.board, Player::Red);
        assert_eq!(gened_fen, "5k3/9/9/9/9/9/9/9/9/4K4 w - - 0 1");

        // 验证棋子的视觉位置
        let red_king_pos = get_array_256_index_from_board_row_col(1, 5);
        let black_king_pos = get_array_256_index_from_board_row_col(10, 4);

        let visual_board = gm.get_display_board();
        assert_eq!(
            visual_board[black_king_pos as usize],
            SidePieceType::BlackKing as u8,
            "黑将位置不正确 (归一化后)"
        );
        assert_eq!(
            visual_board[red_king_pos as usize],
            SidePieceType::RedKing as u8,
            "红帅位置不正确 (归一化后)"
        );
    }

    #[test]
    fn test_load_fen_invalid_king_placement() {
        let mut gm = GameManager::new();
        // 无王
        assert!(
            gm.load_fen("9/9/9/9/9/9/9/9/9/9 w - - 0 1").is_err(),
            "无王的FEN应加载失败"
        );
        // 两个红帅在河两边
        assert!(
            gm.load_fen("4K4/9/9/9/9/9/9/9/9/3K5 w - - 0 1").is_err(),
            "两个红帅在河两边的FEN应加载失败"
        );
        // 两个黑将在河两边
        assert!(
            gm.load_fen("3k5/9/9/9/9/9/9/9/9/5k3 w - - 0 1").is_err(),
            "两个黑将在河两边的FEN应加载失败"
        );
        // 两个红帅在一个九宫格内
        assert!(
            gm.load_fen("9/3K1K3/9/9/9/9/9/9/4k4/9 w - - 0 1").is_err(),
            "两个红帅在一个九宫格内的FEN应加载失败"
        );
        // 两个黑将在一个九宫格内
        assert!(
            gm.load_fen("3kk4/9/9/9/9/9/9/9/9/5K3 w - - 0 1").is_err(),
            "两个黑将在一个九宫格内的FEN应加载失败"
        );
        // 红帅不在九宫格内
        assert!(
            gm.load_fen("5k3/9/9/9/9/9/9/9/9/2K6 w - - 0 1").is_err(),
            "红帅不在九宫的FEN应加载失败"
        );
        // 黑将不在九宫格内
        assert!(
            gm.load_fen("9/9/4K4/9/9/9/9/9/6k2/9 w - - 0 1").is_err(),
            "黑将不在九宫的FEN应加载失败"
        );
    }

    #[test]
    fn test_load_fen_kings_facing_after_normalization() {
        let mut gm = GameManager::new();
        // 标准方向，但照面
        let fen_standard_facing = "4k4/9/9/9/9/9/9/9/4K4/9 w - - 0 1";
        assert!(
            gm.load_fen(fen_standard_facing).is_err(),
            "标准方向但照面的FEN应加载失败"
        );
        // 反方向，但照面
        let fen_standard_facing = "9/5K3/9/9/9/9/9/5k3/9/9 w - - 0 1";
        assert!(
            gm.load_fen(fen_standard_facing).is_err(),
            "反方向但照面的FEN应加载失败"
        );
    }
}
