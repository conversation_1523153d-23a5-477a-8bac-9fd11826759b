{"rustc": 16591470773350601817, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"channel\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 12506755554509207344, "path": 3303257178207977090, "deps": [[5103565458935487, "futures_io", false, 12553233269147750534], [1615478164327904835, "pin_utils", false, 12679970637977357511], [1811549171721445101, "futures_channel", false, 17901671957562981587], [1906322745568073236, "pin_project_lite", false, 1657052395122887890], [3129130049864710036, "memchr", false, 4412818592863735658], [6955678925937229351, "slab", false, 15472069815737934390], [7013762810557009322, "futures_sink", false, 5296059976274781063], [7620660491849607393, "futures_core", false, 10772555710479215973], [10565019901765856648, "futures_macro", false, 9988375525308825125], [16240732885093539806, "futures_task", false, 10685509633135793549]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-util-438cbe203c44ae46\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}