import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:overlay_support/overlay_support.dart';
import 'package:window_manager/window_manager.dart';

import 'common/global.dart';
import 'common/widgets/ios_dialog_widget.dart';
import 'common/route/route.dart';
import 'rust/frb_generated.dart';

void main() async {
  await RustLib.init();

  WidgetsFlutterBinding.ensureInitialized();

  if (Platform.isWindows) {
    await WindowManager.instance.ensureInitialized();
    windowManager.waitUntilReadyToShow().then((_) async {
      await windowManager.setTitleBarStyle(
        TitleBarStyle.hidden,
        windowButtonVisibility: false,
      );
      await windowManager.setTitle("梦入零式");
      await windowManager.setSize(const Size(appWidth, appHeight));
      await windowManager.setAspectRatio(appWidth / appHeight);
      await windowManager.setMinimizable(true);
      await windowManager.setMinimumSize(const Size(appWidth * minSizeScale, appHeight * minSizeScale));
      await windowManager.setMaximumSize(const Size(appWidth * maxSizeScale, appHeight * maxSizeScale));
      await windowManager.setPreventClose(true);
      await windowManager.setSkipTaskbar(false);
      await windowManager.setResizable(true);

      await windowManager.center();
      await windowManager.focus();
      await windowManager.show();

      // 添加窗口事件监听器
      windowManager.addListener(_MyWindowListener());

      runApp(
        OverlaySupport.global(
          child: GetMaterialApp(
            getPages: AppPages.pages,
            initialRoute: Routes.home,
          ),
        ),
      );
    });
  }
}

class _MyWindowListener with WindowListener {
  @override
  void onWindowClose() async {
    await windowManager.show();
    await windowManager.focus();

    if (Get.context != null) {
      showIosDialog(
        Get.context!,
        "提示",
        "是否退出程序？",
        onYesPressed: () {
          exit(0);
        },
      );
    } else {
      exit(0);
    }
  }

  @override
  void onWindowBlur() {}

  @override
  void onWindowDocked() {}

  @override
  void onWindowEnterFullScreen() {}

  @override
  void onWindowFocus() {}

  @override
  void onWindowLeaveFullScreen() {}

  @override
  void onWindowMaximize() {}

  @override
  void onWindowMinimize() {}

  @override
  void onWindowMove() {}

  @override
  void onWindowMoved() {}

  @override
  void onWindowResize() {}

  @override
  void onWindowResized() {}

  @override
  void onWindowRestore() {}

  @override
  void onWindowUndocked() {}

  @override
  void onWindowUnmaximize() {}
}
