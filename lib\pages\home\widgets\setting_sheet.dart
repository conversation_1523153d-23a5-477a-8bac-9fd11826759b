import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:meng_ru_ling_shi/pages/home/<USER>';

// 用于管理设置面板临时状态的辅助类
class _SettingSheetTemporaryState {
  final HomeController _homeController;
  late final RxBool showIccsTooltipTemp;
  late final RxBool enableFenCopyPasteTemp;
  late final RxInt computerMoveDelaySecTemp;
  late final RxBool flipBoardOnExchangeTemp; // 新增：交换红黑时是否翻转棋盘

  _SettingSheetTemporaryState(this._homeController) {
    showIccsTooltipTemp = RxBool(_homeController.showIccsTooltip);
    enableFenCopyPasteTemp = RxBool(_homeController.enableFenCopyPaste);
    computerMoveDelaySecTemp = RxInt(_homeController.computerMoveDelaySec);
    flipBoardOnExchangeTemp = RxBool(_homeController.flipBoardOnExchange); // 初始化
  }

  void saveBackToController() {
    // computerMoveDelaySecTemp 假定已由 _SettingSheetContentState
    // 通过其 TextEditingController 的侦听器或 onEditingComplete 更新。
    int valueToSave = computerMoveDelaySecTemp.value;

    // 在显示 toast 之前检查是否有任何设置实际更改。
    bool settingsChanged = _homeController.showIccsTooltip != showIccsTooltipTemp.value ||
        _homeController.enableFenCopyPaste != enableFenCopyPasteTemp.value ||
        _homeController.computerMoveDelaySec != valueToSave ||
        _homeController.flipBoardOnExchange != flipBoardOnExchangeTemp.value; // 新增：检查新设置

    // 更新 HomeController 的属性
    _homeController.showIccsTooltip = showIccsTooltipTemp.value;
    _homeController.enableFenCopyPaste = enableFenCopyPasteTemp.value;
    _homeController.updateComputerMoveDelaySec(valueToSave);
    _homeController.flipBoardOnExchange = flipBoardOnExchangeTemp.value; // 新增：保存新设置

    if (settingsChanged) {
      _homeController.settingsUpdatedToast();
    }
  }
}

void getSettingSheet(BuildContext context) {
  final HomeController homeController = Get.find<HomeController>();
  final temporaryState = _SettingSheetTemporaryState(homeController);

  showModalBottomSheet(
    context: context,
    isScrollControlled: true, // 允许内容滚动
    builder: (BuildContext bc) {
      // 将临时状态传递给内容小部件
      return _SettingSheetContent(temporaryState: temporaryState);
    },
  ).then((_) {
    // 当面板关闭时执行此块。
    temporaryState.saveBackToController();
  });
}

// _SettingSheetContent 变成一个 StatefulWidget 来管理自己的 TextEditingController
class _SettingSheetContent extends StatefulWidget {
  final _SettingSheetTemporaryState temporaryState;

  const _SettingSheetContent({required this.temporaryState});

  @override
  _SettingSheetContentState createState() => _SettingSheetContentState();
}

class _SettingSheetContentState extends State<_SettingSheetContent> {
  late final TextEditingController _delayEditingController;

  @override
  void initState() {
    super.initState();
    _delayEditingController =
        TextEditingController(text: widget.temporaryState.computerMoveDelaySecTemp.value.toString());
    // 添加侦听器以保持 temporaryState.computerMoveDelaySecTemp 更新
    _delayEditingController.addListener(_onDelayTextChanged);
  }

  void _onDelayTextChanged() {
    final seconds = int.tryParse(_delayEditingController.text);
    if (seconds != null) {
      if (widget.temporaryState.computerMoveDelaySecTemp.value != seconds) {
        widget.temporaryState.computerMoveDelaySecTemp.value = seconds;
      }
    }
    // 如果文本无效，computerMoveDelaySecTemp 将保留其最后一个有效值。
    // onEditingComplete 将处理在输入无效时恢复文本字段的操作。
  }

  @override
  void dispose() {
    _delayEditingController.removeListener(_onDelayTextChanged);
    _delayEditingController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 使用 Obx 包裹内容，使其对 temporaryState 中 Rx 变量的更改做出反应
    // 注意：GetView<HomeController> 已移除，因为这现在是一个 StatefulWidget。
    // 如果需要 HomeController，请使用 Get.find<HomeController>() 或传递它。
    return Obx(() => Container(
          padding: const EdgeInsets.all(16),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Center(
                  child: Text(
                    '全局设置',
                    style: TextStyle(color: Colors.blue, fontWeight: FontWeight.bold, fontSize: 30),
                  ),
                ),
                const SizedBox(height: 20),
                const Text(
                  '界面设置',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                ),
                const SizedBox(height: 10),
                SwitchListTile(
                  title: const Text('显示ICCS坐标提示'),
                  subtitle: const Text('鼠标悬停时显示棋盘坐标'),
                  value: widget.temporaryState.showIccsTooltipTemp.value,
                  onChanged: (value) {
                    widget.temporaryState.showIccsTooltipTemp.value = value;
                  },
                ),
                SwitchListTile(
                  title: const Text('启用FEN复制/粘贴'),
                  subtitle: const Text('允许从剪贴板复制和粘贴FEN字符串'),
                  value: widget.temporaryState.enableFenCopyPasteTemp.value,
                  onChanged: (value) {
                    widget.temporaryState.enableFenCopyPasteTemp.value = value;
                  },
                ),
                SwitchListTile(
                  title: const Text('摆谱模式下交换红黑时翻转棋盘'),
                  subtitle: const Text('在摆谱模式下点击“交换红黑”按钮时，同时翻转棋盘'),
                  value: widget.temporaryState.flipBoardOnExchangeTemp.value,
                  onChanged: (value) {
                    widget.temporaryState.flipBoardOnExchangeTemp.value = value;
                  },
                ),
                const SizedBox(height: 10),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Expanded(
                        flex: 3,
                        child: Tooltip(
                          message: '设置当红黑双方都由电脑托管时，电脑走棋前的延迟时间（单位：秒）。\n设置为0则不延迟。',
                          child: Text(
                            '电脑互打延迟(秒)',
                            style: TextStyle(fontSize: 16),
                          ),
                        ),
                      ),
                      SizedBox(
                        width: 60,
                        child: TextFormField(
                          controller: _delayEditingController, // 使用状态的控制器
                          keyboardType: TextInputType.number,
                          inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                          textAlign: TextAlign.center,
                          decoration: const InputDecoration(
                            isDense: true,
                            contentPadding: EdgeInsets.symmetric(vertical: 8.0),
                          ),
                          onEditingComplete: () {
                            final seconds = int.tryParse(_delayEditingController.text);
                            if (seconds != null) {
                              // 侦听器已经更新了此值，但最好确保一致性
                              widget.temporaryState.computerMoveDelaySecTemp.value = seconds;
                            } else {
                              // 如果输入无效，则将 TextField 恢复为最后一个有效/初始值
                              _delayEditingController.text =
                                  widget.temporaryState.computerMoveDelaySecTemp.value.toString();
                              _delayEditingController.selection =
                                  TextSelection.fromPosition(TextPosition(offset: _delayEditingController.text.length));
                            }
                            FocusScope.of(context).unfocus(); // 关闭键盘
                          },
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 20),
                // 如果需要，可以在此处添加用于显式保存的 ElevatedButton
              ],
            ),
          ),
        ));
  }
}
