#[cfg(test)]
mod tests {
    use crate::chess::{
        <PERSON><PERSON><PERSON><PERSON>, PieceType, Player, SidePieceType, board_to_fen,
        get_array_256_index_from_board_row_col, get_unside_piece_by_side_piece,
    };

    /// 测试红方八路炮(炮八)在初始状态下的有效移动
    #[test]
    fn test_red_cannon_at_col_2_initial_moves() {
        let gm = GameManager::new();

        // 获取红方八路炮(炮八)的位置
        let cannon_pos = get_array_256_index_from_board_row_col(8, 2);

        // 1. 获取棋子类型并验证是炮
        let piece = SidePieceType::RedCannon;
        let unside_piece = get_unside_piece_by_side_piece(piece);
        assert_eq!(unside_piece, PieceType::Cannon);

        // 2. 验证具体移动
        let expected_moves = [
            (
                "b2b3",
                "炮八进一",
                "rnbakabnr/9/1c5c1/p1p1p1p1p/9/9/PCP1P1P1P/7C1/9/RNBAKABNR w - - 0 1",
            ),
            (
                "b2b4",
                "炮八进二",
                "rnbakabnr/9/1c5c1/p1p1p1p1p/9/1C7/P1P1P1P1P/7C1/9/RNBAKABNR w - - 0 1",
            ),
            (
                "b2b5",
                "炮八进三",
                "rnbakabnr/9/1c5c1/p1p1p1p1p/1C7/9/P1P1P1P1P/7C1/9/RNBAKABNR w - - 0 1",
            ),
            (
                "b2b6",
                "炮八进四",
                "rnbakabnr/9/1c5c1/pCp1p1p1p/9/9/P1P1P1P1P/7C1/9/RNBAKABNR w - - 0 1",
            ),
            (
                "b2b9",
                "炮八进七",
                "rCbakabnr/9/1c5c1/p1p1p1p1p/9/9/P1P1P1P1P/7C1/9/RNBAKABNR w - - 0 1",
            ),
            (
                "b2b1",
                "炮八退一",
                "rnbakabnr/9/1c5c1/p1p1p1p1p/9/9/P1P1P1P1P/7C1/1C7/RNBAKABNR w - - 0 1",
            ),
            (
                "b2a2",
                "炮八平九",
                "rnbakabnr/9/1c5c1/p1p1p1p1p/9/9/P1P1P1P1P/C6C1/9/RNBAKABNR w - - 0 1",
            ),
            (
                "b2c2",
                "炮八平七",
                "rnbakabnr/9/1c5c1/p1p1p1p1p/9/9/P1P1P1P1P/2C4C1/9/RNBAKABNR w - - 0 1",
            ),
            (
                "b2d2",
                "炮八平六",
                "rnbakabnr/9/1c5c1/p1p1p1p1p/9/9/P1P1P1P1P/3C3C1/9/RNBAKABNR w - - 0 1",
            ),
            (
                "b2e2",
                "炮八平五",
                "rnbakabnr/9/1c5c1/p1p1p1p1p/9/9/P1P1P1P1P/4C2C1/9/RNBAKABNR w - - 0 1",
            ),
            (
                "b2f2",
                "炮八平四",
                "rnbakabnr/9/1c5c1/p1p1p1p1p/9/9/P1P1P1P1P/5C1C1/9/RNBAKABNR w - - 0 1",
            ),
            (
                "b2g2",
                "炮八平三",
                "rnbakabnr/9/1c5c1/p1p1p1p1p/9/9/P1P1P1P1P/6CC1/9/RNBAKABNR w - - 0 1",
            ),
        ];

        let valid_moves = gm.get_piece_all_valid_moves(cannon_pos);

        // 2.1 验证移动数量
        assert_eq!(valid_moves.len(), expected_moves.len());

        // 2.2 验证iccs是否正确
        for (iccs, _, _) in &expected_moves {
            assert!(valid_moves.contains(&(*iccs).to_string()));
        }

        // 2.3 验证iccs -> 中文记录法
        for (iccs, chinese, _) in &expected_moves {
            assert_eq!(gm.iccs_move_to_chinese_move(iccs), *chinese);
        }

        // 3. 验证移动后的棋盘状态
        for (iccs, _, expected_fen) in &expected_moves {
            // 创建新的GameManager实例进行测试
            let mut test_gm = GameManager::new();

            // 使用 GameManager 的 make_move_by_iccs 方法执行移动
            test_gm.make_move_by_iccs(iccs);

            // 获取移动后的FEN字符串
            // 注意：在实际游戏中，红方移动后应该轮到黑方行棋
            // 但在这个测试中，我们保持与预期FEN字符串一致的玩家参数（Player::Red）
            let actual_fen = board_to_fen(&test_gm.board, Player::Red);

            // 验证FEN字符串
            assert_eq!(actual_fen, *expected_fen);
        }
    }

    /// 测试红方二路炮(炮二)在初始状态下的有效移动
    #[test]
    fn test_red_cannon_at_col_8_initial_moves() {
        let gm = GameManager::new();

        // 获取红方二路炮(炮二)的位置
        let cannon_pos = get_array_256_index_from_board_row_col(8, 8);

        // 1. 获取棋子类型并验证是炮
        let piece = SidePieceType::RedCannon;
        let unside_piece = get_unside_piece_by_side_piece(piece);
        assert_eq!(unside_piece, PieceType::Cannon);

        // 2. 验证具体移动
        let expected_moves = [
            (
                "h2h3",
                "炮二进一",
                "rnbakabnr/9/1c5c1/p1p1p1p1p/9/9/P1P1P1PCP/1C7/9/RNBAKABNR w - - 0 1",
            ),
            (
                "h2h4",
                "炮二进二",
                "rnbakabnr/9/1c5c1/p1p1p1p1p/9/7C1/P1P1P1P1P/1C7/9/RNBAKABNR w - - 0 1",
            ),
            (
                "h2h5",
                "炮二进三",
                "rnbakabnr/9/1c5c1/p1p1p1p1p/7C1/9/P1P1P1P1P/1C7/9/RNBAKABNR w - - 0 1",
            ),
            (
                "h2h6",
                "炮二进四",
                "rnbakabnr/9/1c5c1/p1p1p1pCp/9/9/P1P1P1P1P/1C7/9/RNBAKABNR w - - 0 1",
            ),
            (
                "h2h9",
                "炮二进七",
                "rnbakabCr/9/1c5c1/p1p1p1p1p/9/9/P1P1P1P1P/1C7/9/RNBAKABNR w - - 0 1",
            ),
            (
                "h2h1",
                "炮二退一",
                "rnbakabnr/9/1c5c1/p1p1p1p1p/9/9/P1P1P1P1P/1C7/7C1/RNBAKABNR w - - 0 1",
            ),
            (
                "h2c2",
                "炮二平七",
                "rnbakabnr/9/1c5c1/p1p1p1p1p/9/9/P1P1P1P1P/1CC6/9/RNBAKABNR w - - 0 1",
            ),
            (
                "h2d2",
                "炮二平六",
                "rnbakabnr/9/1c5c1/p1p1p1p1p/9/9/P1P1P1P1P/1C1C5/9/RNBAKABNR w - - 0 1",
            ),
            (
                "h2e2",
                "炮二平五",
                "rnbakabnr/9/1c5c1/p1p1p1p1p/9/9/P1P1P1P1P/1C2C4/9/RNBAKABNR w - - 0 1",
            ),
            (
                "h2f2",
                "炮二平四",
                "rnbakabnr/9/1c5c1/p1p1p1p1p/9/9/P1P1P1P1P/1C3C3/9/RNBAKABNR w - - 0 1",
            ),
            (
                "h2g2",
                "炮二平三",
                "rnbakabnr/9/1c5c1/p1p1p1p1p/9/9/P1P1P1P1P/1C4C2/9/RNBAKABNR w - - 0 1",
            ),
            (
                "h2i2",
                "炮二平一",
                "rnbakabnr/9/1c5c1/p1p1p1p1p/9/9/P1P1P1P1P/1C6C/9/RNBAKABNR w - - 0 1",
            ),
        ];

        let valid_moves = gm.get_piece_all_valid_moves(cannon_pos);

        // 2.1 验证移动数量
        assert_eq!(valid_moves.len(), expected_moves.len());

        // 2.2 验证iccs是否正确
        for (iccs, _, _) in &expected_moves {
            assert!(valid_moves.contains(&(*iccs).to_string()));
        }

        // 2.3 验证iccs -> 中文记录法
        for (iccs, chinese, _) in &expected_moves {
            assert_eq!(gm.iccs_move_to_chinese_move(iccs), *chinese);
        }

        // 3. 验证移动后的棋盘状态
        for (iccs, _, expected_fen) in &expected_moves {
            // 创建新的GameManager实例进行测试
            let mut test_gm = GameManager::new();

            // 使用 GameManager 的 make_move_by_iccs 方法执行移动
            test_gm.make_move_by_iccs(iccs);

            // 获取移动后的FEN字符串
            // 注意：在实际游戏中，红方移动后应该轮到黑方行棋
            // 但在这个测试中，我们保持与预期FEN字符串一致的玩家参数（Player::Red）
            let actual_fen = board_to_fen(&test_gm.board, Player::Red);

            // 验证FEN字符串
            assert_eq!(actual_fen, *expected_fen);
        }
    }
}
