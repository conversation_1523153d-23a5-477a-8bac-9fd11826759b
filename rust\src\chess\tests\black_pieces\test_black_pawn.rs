#[cfg(test)]
mod tests {
    use crate::chess::{
        <PERSON><PERSON><PERSON><PERSON>, PieceType, Player, SidePieceType, board_to_fen, fen_to_board,
        get_array_256_index_from_board_row_col, get_unside_piece_by_side_piece,
    };

    /// 测试黑方九路卒(卒9)在初始状态下的有效移动
    #[test]
    fn test_black_pawn_at_col_9_initial_moves() {
        let gm = GameManager::new();

        // 获取黑方九路卒(卒9)的位置
        let pawn_pos = get_array_256_index_from_board_row_col(4, 9);

        // 1. 获取棋子类型并验证是卒
        let piece = SidePieceType::BlackPawn;
        let unside_piece = get_unside_piece_by_side_piece(piece);
        assert_eq!(unside_piece, PieceType::Pawn);

        // 2. 验证具体移动
        let expected_moves = [(
            "i6i5",
            "卒9进1",
            "rnbakabnr/9/1c5c1/p1p1p1p2/8p/9/P1P1P1P1P/1C5C1/9/RNBAKABNR b - - 0 1",
        )];
        let valid_moves = gm.get_piece_all_valid_moves(pawn_pos);

        // 2.1 验证移动数量
        assert_eq!(valid_moves.len(), expected_moves.len());

        // 2.2 验证iccs是否正确
        for (iccs, _, _) in &expected_moves {
            assert!(valid_moves.contains(&(*iccs).to_string()));
        }

        // 2.3 验证iccs -> 中文记录法
        for (iccs, chinese, _) in &expected_moves {
            assert_eq!(gm.iccs_move_to_chinese_move(iccs), *chinese);
        }

        // 3. 验证移动后的棋盘状态
        for (iccs, _, expected_fen) in &expected_moves {
            // 创建新的GameManager实例进行测试
            let mut test_gm = GameManager::new();

            // 使用 GameManager 的 make_move_by_iccs 方法执行移动
            test_gm.make_move_by_iccs(iccs);

            // 获取移动后的FEN字符串
            // 注意：在实际游戏中，黑方移动后应该轮到红方行棋
            // 但在这个测试中，我们保持与预期FEN字符串一致的玩家参数（Player::Black）
            let actual_fen = board_to_fen(&test_gm.board, Player::Black);

            // 验证FEN字符串
            assert_eq!(actual_fen, *expected_fen);
        }
    }

    /// 测试黑方七路卒(卒7)在初始状态下的有效移动
    #[test]
    fn test_black_pawn_at_col_7_initial_moves() {
        let gm = GameManager::new();

        // 获取黑方七路卒(卒7)的位置
        let pawn_pos = get_array_256_index_from_board_row_col(4, 7);

        // 1. 获取棋子类型并验证是卒
        let piece = SidePieceType::BlackPawn;
        let unside_piece = get_unside_piece_by_side_piece(piece);
        assert_eq!(unside_piece, PieceType::Pawn);

        // 2. 验证具体移动
        let expected_moves = [(
            "g6g5",
            "卒7进1",
            "rnbakabnr/9/1c5c1/p1p1p3p/6p2/9/P1P1P1P1P/1C5C1/9/RNBAKABNR b - - 0 1",
        )];
        let valid_moves = gm.get_piece_all_valid_moves(pawn_pos);

        // 2.1 验证移动数量
        assert_eq!(valid_moves.len(), expected_moves.len());

        // 2.2 验证iccs是否正确
        for (iccs, _, _) in &expected_moves {
            assert!(valid_moves.contains(&(*iccs).to_string()));
        }

        // 2.3 验证iccs -> 中文记录法
        for (iccs, chinese, _) in &expected_moves {
            assert_eq!(gm.iccs_move_to_chinese_move(iccs), *chinese);
        }

        // 3. 验证移动后的棋盘状态
        for (iccs, _, expected_fen) in &expected_moves {
            // 创建新的GameManager实例进行测试
            let mut test_gm = GameManager::new();

            // 使用 GameManager 的 make_move_by_iccs 方法执行移动
            test_gm.make_move_by_iccs(iccs);

            // 获取移动后的FEN字符串
            let actual_fen = board_to_fen(&test_gm.board, Player::Black);

            // 验证FEN字符串
            assert_eq!(actual_fen, *expected_fen);
        }
    }

    /// 测试黑方五路卒(卒5)在初始状态下的有效移动
    #[test]
    fn test_black_pawn_at_col_5_initial_moves() {
        let gm = GameManager::new();

        // 获取黑方五路卒(卒5)的位置
        let pawn_pos = get_array_256_index_from_board_row_col(4, 5);

        // 1. 获取棋子类型并验证是卒
        let piece = SidePieceType::BlackPawn;
        let unside_piece = get_unside_piece_by_side_piece(piece);
        assert_eq!(unside_piece, PieceType::Pawn);

        // 2. 验证具体移动
        let expected_moves = [(
            "e6e5",
            "卒5进1",
            "rnbakabnr/9/1c5c1/p1p3p1p/4p4/9/P1P1P1P1P/1C5C1/9/RNBAKABNR b - - 0 1",
        )];
        let valid_moves = gm.get_piece_all_valid_moves(pawn_pos);

        // 2.1 验证移动数量
        assert_eq!(valid_moves.len(), expected_moves.len());

        // 2.2 验证iccs是否正确
        for (iccs, _, _) in &expected_moves {
            assert!(valid_moves.contains(&(*iccs).to_string()));
        }

        // 2.3 验证iccs -> 中文记录法
        for (iccs, chinese, _) in &expected_moves {
            assert_eq!(gm.iccs_move_to_chinese_move(iccs), *chinese);
        }

        // 3. 验证移动后的棋盘状态
        for (iccs, _, expected_fen) in &expected_moves {
            // 创建新的GameManager实例进行测试
            let mut test_gm = GameManager::new();

            // 使用 GameManager 的 make_move_by_iccs 方法执行移动
            test_gm.make_move_by_iccs(iccs);

            // 获取移动后的FEN字符串
            let actual_fen = board_to_fen(&test_gm.board, Player::Black);

            // 验证FEN字符串
            assert_eq!(actual_fen, *expected_fen);
        }
    }

    /// 测试黑方三路卒(卒3)在初始状态下的有效移动
    #[test]
    fn test_black_pawn_at_col_3_initial_moves() {
        let gm = GameManager::new();

        // 获取黑方三路卒(卒3)的位置
        let pawn_pos = get_array_256_index_from_board_row_col(4, 3);

        // 1. 获取棋子类型并验证是卒
        let piece = SidePieceType::BlackPawn;
        let unside_piece = get_unside_piece_by_side_piece(piece);
        assert_eq!(unside_piece, PieceType::Pawn);

        // 2. 验证具体移动
        let expected_moves = [(
            "c6c5",
            "卒3进1",
            "rnbakabnr/9/1c5c1/p3p1p1p/2p6/9/P1P1P1P1P/1C5C1/9/RNBAKABNR b - - 0 1",
        )];
        let valid_moves = gm.get_piece_all_valid_moves(pawn_pos);

        // 2.1 验证移动数量
        assert_eq!(valid_moves.len(), expected_moves.len());

        // 2.2 验证iccs是否正确
        for (iccs, _, _) in &expected_moves {
            assert!(valid_moves.contains(&(*iccs).to_string()));
        }

        // 2.3 验证iccs -> 中文记录法
        for (iccs, chinese, _) in &expected_moves {
            assert_eq!(gm.iccs_move_to_chinese_move(iccs), *chinese);
        }

        // 3. 验证移动后的棋盘状态
        for (iccs, _, expected_fen) in &expected_moves {
            // 创建新的GameManager实例进行测试
            let mut test_gm = GameManager::new();

            // 使用 GameManager 的 make_move_by_iccs 方法执行移动
            test_gm.make_move_by_iccs(iccs);

            // 获取移动后的FEN字符串
            let actual_fen = board_to_fen(&test_gm.board, Player::Black);

            // 验证FEN字符串
            assert_eq!(actual_fen, *expected_fen);
        }
    }

    /// 测试黑方一路卒(卒1)在初始状态下的有效移动
    #[test]
    fn test_black_pawn_at_col_1_initial_moves() {
        let gm = GameManager::new();

        // 获取黑方一路卒(卒1)的位置
        let pawn_pos = get_array_256_index_from_board_row_col(4, 1);

        // 1. 获取棋子类型并验证是卒
        let piece = SidePieceType::BlackPawn;
        let unside_piece = get_unside_piece_by_side_piece(piece);
        assert_eq!(unside_piece, PieceType::Pawn);

        // 2. 验证具体移动
        let expected_moves = [(
            "a6a5",
            "卒1进1",
            "rnbakabnr/9/1c5c1/2p1p1p1p/p8/9/P1P1P1P1P/1C5C1/9/RNBAKABNR b - - 0 1",
        )];
        let valid_moves = gm.get_piece_all_valid_moves(pawn_pos);

        // 2.1 验证移动数量
        assert_eq!(valid_moves.len(), expected_moves.len());

        // 2.2 验证iccs是否正确
        for (iccs, _, _) in &expected_moves {
            assert!(valid_moves.contains(&(*iccs).to_string()));
        }

        // 2.3 验证iccs -> 中文记录法
        for (iccs, chinese, _) in &expected_moves {
            assert_eq!(gm.iccs_move_to_chinese_move(iccs), *chinese);
        }

        // 3. 验证移动后的棋盘状态
        for (iccs, _, expected_fen) in &expected_moves {
            // 创建新的GameManager实例进行测试
            let mut test_gm = GameManager::new();

            // 使用 GameManager 的 make_move_by_iccs 方法执行移动
            test_gm.make_move_by_iccs(iccs);

            // 获取移动后的FEN字符串
            let actual_fen = board_to_fen(&test_gm.board, Player::Black);

            // 验证FEN字符串
            assert_eq!(actual_fen, *expected_fen);
        }
    }

    /// 测试特殊布局下黑方兵的编号和移动
    /// `这个测试使用xiangqi_note.md中"黑方特殊情况示例"中的FEN对应的布局`
    /// FEN: 4k4/9/9/9/9/9/3p1p3/4p4/3p1p3/4K4 b - - 0 1
    #[test]
    fn test_special_layout_fen_with_5_black_pawns_in_dice_5_pattern() {
        let fen_string = "4k4/9/9/9/9/9/3p1p3/4p4/3p1p3/4K4 b - - 0 1";

        let expected_moves = [
            // (ICCS Move, Chinese Name, Expected FEN after move)
            // Expected FENs use 'b' for player and original move counters, to mimic red_pawn test style.
            (
                "d1e1",
                "一卒平5",
                "4k4/9/9/9/9/9/3p1p3/4p4/4pp3/4K4 b - - 0 1",
            ),
            (
                "d3e3",
                "二卒平5",
                "4k4/9/9/9/9/9/4pp3/4p4/3p1p3/4K4 b - - 0 1",
            ),
            (
                "e2e1",
                "卒5进1",
                "4k4/9/9/9/9/9/3p1p3/9/3ppp3/4K4 b - - 0 1",
            ),
            (
                "f1e1",
                "三卒平5",
                "4k4/9/9/9/9/9/3p1p3/4p4/3pp4/4K4 b - - 0 1",
            ),
            (
                "f3e3",
                "四卒平5",
                "4k4/9/9/9/9/9/3pp4/4p4/3p1p3/4K4 b - - 0 1",
            ),
        ];

        for (iccs_move, chinese_name, expected_fen_str) in &expected_moves {
            // Setup GameManager for Chinese conversion (needs board state for context)
            let (initial_board_for_conversion, initial_player_for_conversion) =
                fen_to_board(fen_string)
                    .unwrap_or_else(|_| panic!("测试FEN '{fen_string}' 解析应成功"));
            let mut gm_for_conversion = GameManager::new(); // Create a new GameManager
            gm_for_conversion.board = initial_board_for_conversion;
            gm_for_conversion.current_player = initial_player_for_conversion;

            let actual_chinese_move = gm_for_conversion.iccs_move_to_chinese_move(iccs_move);
            assert_eq!(
                actual_chinese_move, *chinese_name,
                "Chinese move mismatch for ICCS: {iccs_move}. Expected: \"{chinese_name}\", Got: \"{actual_chinese_move}\""
            );

            // Setup GameManager for move execution and FEN validation
            let (initial_board_for_move, initial_player_for_move) = fen_to_board(fen_string)
                .unwrap_or_else(|_| panic!("测试FEN '{fen_string}' 解析应成功"));
            let mut gm_for_move = GameManager::new(); // Create a new GameManager
            gm_for_move.board = initial_board_for_move;
            gm_for_move.current_player = initial_player_for_move;

            gm_for_move.make_move_by_iccs(iccs_move);

            // Mimicking red_pawn_test: use Player::Black because current player was Black before move.
            // The expected_fen_str also has 'b' and original move counters.
            let actual_fen = board_to_fen(&gm_for_move.board, Player::Black);
            assert_eq!(
                actual_fen, *expected_fen_str,
                "FEN mismatch for ICCS move: {iccs_move}. Expected: \"{expected_fen_str}\", Got: \"{actual_fen}\""
            );
        }
    }
}
