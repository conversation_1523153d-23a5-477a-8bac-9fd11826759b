{"rustc": 16591470773350601817, "features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"full\", \"parsing\", \"printing\", \"proc-macro\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 8285369720897779924, "path": 1947319005565387555, "deps": [[1988483478007900009, "unicode_ident", false, 15887168175168753732], [3060637413840920116, "proc_macro2", false, 16794246657031976756], [17990358020177143287, "quote", false, 8273918329785368987]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\syn-3c1c8bad93fa97cc\\dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}