{"rustc": 16591470773350601817, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[13625485746686963219, "build_script_build", false, 5172416845255835301]], "local": [{"RerunIfChanged": {"output": "debug\\build\\anyhow-45ccffff902c2810\\output", "paths": ["src/nightly.rs"]}}, {"RerunIfEnvChanged": {"var": "RUSTC_BOOTSTRAP", "val": null}}], "rustflags": ["--cfg", "frb_expand"], "config": 0, "compile_kind": 0}