#[cfg(test)]
mod tests {
    use crate::chess::{
        <PERSON><PERSON><PERSON><PERSON>, PieceType, Player, SidePieceType, board_to_fen, fen_to_board,
        get_array_256_index_from_board_row_col, get_unside_piece_by_side_piece,
    };

    /// 测试红方九路兵(兵九)在初始状态下的有效移动
    #[test]
    fn test_red_pawn_at_col_1_initial_moves() {
        let gm = GameManager::new();

        // 获取红方九路兵(兵九)的位置
        let pawn_pos = get_array_256_index_from_board_row_col(7, 1);

        // 1. 获取棋子类型并验证是兵
        let piece = SidePieceType::RedPawn;
        let unside_piece = get_unside_piece_by_side_piece(piece);
        assert_eq!(unside_piece, PieceType::Pawn);

        // 2. 验证具体移动
        let expected_moves = [(
            "a3a4",
            "兵九进一",
            "rnbakabnr/9/1c5c1/p1p1p1p1p/9/P8/2P1P1P1P/1C5C1/9/RNBAKABNR w - - 0 1",
        )];
        let valid_moves = gm.get_piece_all_valid_moves(pawn_pos);

        // 2.1 验证移动数量
        assert_eq!(valid_moves.len(), expected_moves.len());

        // 2.2 验证iccs是否正确
        for (iccs, _, _) in &expected_moves {
            assert!(valid_moves.contains(&(*iccs).to_string()));
        }

        // 2.3 验证iccs -> 中文记录法
        for (iccs, chinese, _) in &expected_moves {
            assert_eq!(gm.iccs_move_to_chinese_move(iccs), *chinese);
        }

        // 3. 验证移动后的棋盘状态
        for (iccs, _, expected_fen) in &expected_moves {
            // 创建新的GameManager实例进行测试
            let mut test_gm = GameManager::new();

            // 使用 GameManager 的 make_move_by_iccs 方法执行移动
            test_gm.make_move_by_iccs(iccs);

            // 获取移动后的FEN字符串
            // 注意：在实际游戏中，红方移动后应该轮到黑方行棋
            // 但在这个测试中，我们保持与预期FEN字符串一致的玩家参数（Player::Red）
            let actual_fen = board_to_fen(&test_gm.board, Player::Red);

            // 验证FEN字符串
            assert_eq!(actual_fen, *expected_fen);
        }
    }

    /// 测试红方七路兵(兵七)在初始状态下的有效移动
    #[test]
    fn test_red_pawn_at_col_3_initial_moves() {
        let gm = GameManager::new();

        // 获取红方七路兵(兵七)的位置
        let pawn_pos = get_array_256_index_from_board_row_col(7, 3);

        // 1. 获取棋子类型并验证是兵
        let piece = SidePieceType::RedPawn;
        let unside_piece = get_unside_piece_by_side_piece(piece);
        assert_eq!(unside_piece, PieceType::Pawn);

        // 2. 验证具体移动
        let expected_moves = [(
            "c3c4",
            "兵七进一",
            "rnbakabnr/9/1c5c1/p1p1p1p1p/9/2P6/P3P1P1P/1C5C1/9/RNBAKABNR w - - 0 1",
        )];
        let valid_moves = gm.get_piece_all_valid_moves(pawn_pos);

        // 2.1 验证移动数量
        assert_eq!(valid_moves.len(), expected_moves.len());

        // 2.2 验证iccs是否正确
        for (iccs, _, _) in &expected_moves {
            assert!(valid_moves.contains(&(*iccs).to_string()));
        }

        // 2.3 验证iccs -> 中文记录法
        for (iccs, chinese, _) in &expected_moves {
            assert_eq!(gm.iccs_move_to_chinese_move(iccs), *chinese);
        }

        // 3. 验证移动后的棋盘状态
        for (iccs, _, expected_fen) in &expected_moves {
            // 创建新的GameManager实例进行测试
            let mut test_gm = GameManager::new();

            // 使用 GameManager 的 make_move_by_iccs 方法执行移动
            test_gm.make_move_by_iccs(iccs);

            // 获取移动后的FEN字符串
            let actual_fen = board_to_fen(&test_gm.board, Player::Red);

            // 验证FEN字符串
            assert_eq!(actual_fen, *expected_fen);
        }
    }

    /// 测试红方五路兵(兵五)在初始状态下的有效移动
    #[test]
    fn test_red_pawn_at_col_5_initial_moves() {
        let gm = GameManager::new();

        // 获取红方五路兵(兵五)的位置
        let pawn_pos = get_array_256_index_from_board_row_col(7, 5);

        // 1. 获取棋子类型并验证是兵
        let piece = SidePieceType::RedPawn;
        let unside_piece = get_unside_piece_by_side_piece(piece);
        assert_eq!(unside_piece, PieceType::Pawn);

        // 2. 验证具体移动
        let expected_moves = [(
            "e3e4",
            "兵五进一",
            "rnbakabnr/9/1c5c1/p1p1p1p1p/9/4P4/P1P3P1P/1C5C1/9/RNBAKABNR w - - 0 1",
        )];
        let valid_moves = gm.get_piece_all_valid_moves(pawn_pos);

        // 2.1 验证移动数量
        assert_eq!(valid_moves.len(), expected_moves.len());

        // 2.2 验证iccs是否正确
        for (iccs, _, _) in &expected_moves {
            assert!(valid_moves.contains(&(*iccs).to_string()));
        }

        // 2.3 验证iccs -> 中文记录法
        for (iccs, chinese, _) in &expected_moves {
            assert_eq!(gm.iccs_move_to_chinese_move(iccs), *chinese);
        }

        // 3. 验证移动后的棋盘状态
        for (iccs, _, expected_fen) in &expected_moves {
            // 创建新的GameManager实例进行测试
            let mut test_gm = GameManager::new();

            // 使用 GameManager 的 make_move_by_iccs 方法执行移动
            test_gm.make_move_by_iccs(iccs);

            // 获取移动后的FEN字符串
            let actual_fen = board_to_fen(&test_gm.board, Player::Red);

            // 验证FEN字符串
            assert_eq!(actual_fen, *expected_fen);
        }
    }

    /// 测试红方三路兵(兵三)在初始状态下的有效移动
    #[test]
    fn test_red_pawn_at_col_7_initial_moves() {
        let gm = GameManager::new();

        // 获取红方三路兵(兵三)的位置
        let pawn_pos = get_array_256_index_from_board_row_col(7, 7);

        // 1. 获取棋子类型并验证是兵
        let piece = SidePieceType::RedPawn;
        let unside_piece = get_unside_piece_by_side_piece(piece);
        assert_eq!(unside_piece, PieceType::Pawn);

        // 2. 验证具体移动
        let expected_moves = [(
            "g3g4",
            "兵三进一",
            "rnbakabnr/9/1c5c1/p1p1p1p1p/9/6P2/P1P1P3P/1C5C1/9/RNBAKABNR w - - 0 1",
        )];
        let valid_moves = gm.get_piece_all_valid_moves(pawn_pos);

        // 2.1 验证移动数量
        assert_eq!(valid_moves.len(), expected_moves.len());

        // 2.2 验证iccs是否正确
        for (iccs, _, _) in &expected_moves {
            assert!(valid_moves.contains(&(*iccs).to_string()));
        }

        // 2.3 验证iccs -> 中文记录法
        for (iccs, chinese, _) in &expected_moves {
            assert_eq!(gm.iccs_move_to_chinese_move(iccs), *chinese);
        }

        // 3. 验证移动后的棋盘状态
        for (iccs, _, expected_fen) in &expected_moves {
            // 创建新的GameManager实例进行测试
            let mut test_gm = GameManager::new();

            // 使用 GameManager 的 make_move_by_iccs 方法执行移动
            test_gm.make_move_by_iccs(iccs);

            // 获取移动后的FEN字符串
            let actual_fen = board_to_fen(&test_gm.board, Player::Red);

            // 验证FEN字符串
            assert_eq!(actual_fen, *expected_fen);
        }
    }

    /// 测试红方一路兵(兵一)在初始状态下的有效移动
    #[test]
    fn test_red_pawn_at_col_9_initial_moves() {
        let gm = GameManager::new();

        // 获取红方一路兵(兵一)的位置
        let pawn_pos = get_array_256_index_from_board_row_col(7, 9);

        // 1. 获取棋子类型并验证是兵
        let piece = SidePieceType::RedPawn;
        let unside_piece = get_unside_piece_by_side_piece(piece);
        assert_eq!(unside_piece, PieceType::Pawn);

        // 2. 验证具体移动
        let expected_moves = [(
            "i3i4",
            "兵一进一",
            "rnbakabnr/9/1c5c1/p1p1p1p1p/9/8P/P1P1P1P2/1C5C1/9/RNBAKABNR w - - 0 1",
        )];
        let valid_moves = gm.get_piece_all_valid_moves(pawn_pos);

        // 2.1 验证移动数量
        assert_eq!(valid_moves.len(), expected_moves.len());

        // 2.2 验证iccs是否正确
        for (iccs, _, _) in &expected_moves {
            assert!(valid_moves.contains(&(*iccs).to_string()));
        }

        // 2.3 验证iccs -> 中文记录法
        for (iccs, chinese, _) in &expected_moves {
            assert_eq!(gm.iccs_move_to_chinese_move(iccs), *chinese);
        }

        // 3. 验证移动后的棋盘状态
        for (iccs, _, expected_fen) in &expected_moves {
            // 创建新的GameManager实例进行测试
            let mut test_gm = GameManager::new();

            // 使用 GameManager 的 make_move_by_iccs 方法执行移动
            test_gm.make_move_by_iccs(iccs);

            // 获取移动后的FEN字符串
            let actual_fen = board_to_fen(&test_gm.board, Player::Red);

            // 验证FEN字符串
            assert_eq!(actual_fen, *expected_fen);
        }
    }

    /// 测试特殊布局下红方兵的编号和移动
    /// `这个测试使用xiangqi_note.md中"特殊布局下的兵卒编号示意图"中的布局`
    #[test]
    fn test_special_layout_fen_with_5_red_pawns_in_dice_5_pattern() {
        // 特殊布局的FEN字符串，5个红方兵呈骰子5点状
        // 使用特殊布局的FEN字符串创建棋盘
        let fen_str = "4k4/3P1P3/4P4/3P1P3/9/9/9/9/9/4K4 w - - 0 1";
        let (board, player) =
            fen_to_board(fen_str).unwrap_or_else(|_| panic!("测试FEN '{fen_str}' 解析应成功"));

        // 创建使用特殊布局的GameManager
        let mut gm = GameManager::new();
        gm.board = board;
        gm.current_player = player;

        // 测试红方特殊布局下的兵的编号和移动

        // 1. 测试红方一兵平移至五路：`一兵平五`（对应iccs坐标格式"F8-E8"，红方视角最右侧的兵）
        let iccs_move = "f8e8";
        let chinese_move = gm.iccs_move_to_chinese_move(iccs_move);
        assert_eq!(chinese_move, "一兵平五");

        // 执行移动并验证FEN
        let mut test_gm = GameManager::new();
        test_gm.board = board;
        test_gm.current_player = player;
        test_gm.make_move_by_iccs(iccs_move);
        let expected_fen = "4k4/3PP4/4P4/3P1P3/9/9/9/9/9/4K4 w - - 0 1";
        let actual_fen = board_to_fen(&test_gm.board, Player::Red);
        assert_eq!(actual_fen, expected_fen);

        // 2. 测试红方二兵平移至五路：`二兵平五`（对应iccs坐标格式"F6-E6"，红方视角右侧第二列的兵）
        let iccs_move = "f6e6";
        let chinese_move = gm.iccs_move_to_chinese_move(iccs_move);
        assert_eq!(chinese_move, "二兵平五");

        // 执行移动并验证FEN
        let mut test_gm = GameManager::new();
        test_gm.board = board;
        test_gm.current_player = player;
        test_gm.make_move_by_iccs(iccs_move);
        let expected_fen = "4k4/3P1P3/4P4/3PP4/9/9/9/9/9/4K4 w - - 0 1";
        let actual_fen = board_to_fen(&test_gm.board, Player::Red);
        assert_eq!(actual_fen, expected_fen);

        // 3. 测试红方五路兵前进一步：`兵五进一`（对应iccs坐标格式"E7-E8"，位于红方视角五路的兵）
        let iccs_move = "e7e8";
        let chinese_move = gm.iccs_move_to_chinese_move(iccs_move);
        assert_eq!(chinese_move, "兵五进一");

        // 执行移动并验证FEN
        let mut test_gm = GameManager::new();
        test_gm.board = board;
        test_gm.current_player = player;
        test_gm.make_move_by_iccs(iccs_move);
        let expected_fen = "4k4/3PPP3/9/3P1P3/9/9/9/9/9/4K4 w - - 0 1";
        let actual_fen = board_to_fen(&test_gm.board, Player::Red);
        assert_eq!(actual_fen, expected_fen);

        // 4. 测试红方三兵平移至五路：`三兵平五`（对应iccs坐标格式"D8-E8"，红方视角右侧第三列的兵）
        let iccs_move = "d8e8";
        let chinese_move = gm.iccs_move_to_chinese_move(iccs_move);
        assert_eq!(chinese_move, "三兵平五");

        // 执行移动并验证FEN
        let mut test_gm = GameManager::new();
        test_gm.board = board;
        test_gm.current_player = player;
        test_gm.make_move_by_iccs(iccs_move);
        let expected_fen = "4k4/4PP3/4P4/3P1P3/9/9/9/9/9/4K4 w - - 0 1";
        let actual_fen = board_to_fen(&test_gm.board, Player::Red);
        assert_eq!(actual_fen, expected_fen);

        // 5. 测试红方四兵平移至五路：`四兵平五`（对应iccs坐标格式"D6-E6"，红方视角右侧第四列的兵）
        let iccs_move = "d6e6";
        let chinese_move = gm.iccs_move_to_chinese_move(iccs_move);
        assert_eq!(chinese_move, "四兵平五");

        // 执行移动并验证FEN
        let mut test_gm = GameManager::new();
        test_gm.board = board;
        test_gm.current_player = player;
        test_gm.make_move_by_iccs(iccs_move);
        let expected_fen = "4k4/3P1P3/4P4/4PP3/9/9/9/9/9/4K4 w - - 0 1";
        let actual_fen = board_to_fen(&test_gm.board, Player::Red);
        assert_eq!(actual_fen, expected_fen);
    }
}
