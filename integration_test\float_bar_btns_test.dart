import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:integration_test/integration_test.dart';
import 'package:meng_ru_ling_shi/main.dart' as app;
import 'package:meng_ru_ling_shi/pages/home/<USER>';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized(); // 确保绑定已初始化

  setUpAll(() {
    app.main(); // 初始化应用
    Get.testMode = true; // 为所有测试设置GetX测试模式
  });

  // 在每个测试之后清理GetX状态
  tearDown(() {
    Get.reset();
  });

  group('浮动工具栏功能测试', () {
    testWidgets('点击新对局按钮后，棋盘应初始化到标准开局布局FEN', (WidgetTester tester) async {
      await tester.pumpAndSettle(const Duration(milliseconds: 100));

      // 2. 确保工具栏已展开
      await ensureToolbarExpanded(tester); // 此函数内部会处理 pumpAndSettle

      // 3. 找到“新对局”按钮 (使用Key)
      final newGameButtonFinder = find.byKey(const Key('float_button_0_true'));
      expect(newGameButtonFinder, findsOneWidget, reason: '浮动工具栏展开后，未找到“新对局”按钮 (Key: float_button_0_true)');

      // 4. 模拟用户点击“新对局”按钮
      await tester.tap(newGameButtonFinder);
      await tester.pumpAndSettle(const Duration(seconds: 1)); // 等待棋盘更新逻辑

      // 5. 获取HomeController实例并验证FEN字符串
      final HomeController homeCtrl = Get.find<HomeController>(); // 此时应该获取到新的或重置的实例
      final String currentFen = await homeCtrl.gameManager.getDisplayBoardFen();
      const String initialFen = "rnbakabnr/9/1c5c1/p1p1p1p1p/9/9/P1P1P1P1P/1C5C1/9/RNBAKABNR w - - 0 1";
      expect(currentFen, equals(initialFen), reason: '点击新对局后的FEN与标准初始FEN不匹配');
    });

    testWidgets('点击设置按钮后，应弹出设置面板', (WidgetTester tester) async {
      await tester.pumpAndSettle(const Duration(milliseconds: 100));

      // 1. 确保工具栏已展开
      await ensureToolbarExpanded(tester);

      // 2. 找到“设置”按钮 (使用Key)
      // 根据 home/view.dart, 设置按钮的索引是1
      final settingsButtonFinder = find.byKey(const Key('float_button_1_true'));
      expect(settingsButtonFinder, findsOneWidget, reason: '浮动工具栏展开后，未找到“设置”按钮 (Key: float_button_1_true)');

      // 3. 模拟用户点击“设置”按钮
      await tester.tap(settingsButtonFinder);
      await tester.pumpAndSettle(const Duration(seconds: 1)); // 等待底部面板弹出动画

      // 4. 验证设置面板是否已显示
      // 根据 setting_sheet.dart, 面板标题是 "全局设置"
      expect(find.text('全局设置'), findsOneWidget, reason: '点击设置按钮后，未找到标题为“全局设置”的设置面板');
    });
  });
}

// 辅助函数：确保浮动工具栏已展开
Future<void> ensureToolbarExpanded(WidgetTester tester) async {
  final collapseButtonFinder = find.byIcon(CupertinoIcons.minus_circle_fill);

  // 检查是否已经展开
  // 在查找前pump一下，确保状态最新
  await tester.pump(const Duration(milliseconds: 50)); // 短暂pump，不是pumpAndSettle，避免不必要的等待
  if (tester.any(collapseButtonFinder)) {
    await tester.pumpAndSettle(const Duration(milliseconds: 50)); // 确认稳定
    expect(collapseButtonFinder, findsOneWidget, reason: '确认折叠按钮存在时，工具栏已展开 (ensureToolbarExpanded)');
    return; // 已展开，无需操作
  }

  // 如果未展开，则查找展开按钮并点击
  final expandButtonFinder = find.byIcon(Icons.add);
  // 在断言前再次pump，确保查找准确性
  await tester.pumpAndSettle(const Duration(milliseconds: 50)); // 稳定后查找
  expect(expandButtonFinder, findsOneWidget, reason: '未找到浮动工具栏的展开按钮 (Icons.add) - 尝试展开时 (ensureToolbarExpanded)');

  final Rect buttonRect = tester.getRect(expandButtonFinder);
  final Offset tapPoint = Offset(buttonRect.left + buttonRect.width / 4, buttonRect.center.dy);
  await tester.tapAt(tapPoint);
  await tester.pumpAndSettle(const Duration(milliseconds: 500)); // 等待展开动画/状态更新

  // 验证是否成功展开
  expect(find.byIcon(CupertinoIcons.minus_circle_fill), findsOneWidget,
      reason: '浮动工具栏未成功展开 (未找到折叠图标 CupertinoIcons.minus_circle_fill) - 展开后检查 (ensureToolbarExpanded)');
}
