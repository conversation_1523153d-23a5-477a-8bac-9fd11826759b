import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../pages/home/<USER>';
import '../../rust/chess/piece_utils.dart'; // 导入piece_utils

class MoveHistoryPanel extends GetView<HomeController> {
  const MoveHistoryPanel({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4.0, vertical: 2.0), // 减少内边距
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            decoration: BoxDecoration(
              color: Theme.of(context).canvasColor, // 确保背景颜色以显示阴影
              borderRadius: BorderRadius.circular(8.0), // 添加圆角
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.15),
                  spreadRadius: 1,
                  blurRadius: 3,
                  offset: const Offset(0, 2), // 改变阴影位置
                ),
              ],
            ),
            child: Obx(() => Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround, // 均匀分布按钮
                  children: [
                    IconButton(
                      icon: const Icon(Icons.skip_previous),
                      tooltip: "到开头",
                      onPressed: controller.moveHistory.isEmpty ? null : () => controller.goBackToBoardAfterIndex(-1),
                    ),
                    IconButton(
                      icon: const Icon(Icons.fast_rewind),
                      tooltip: "后退一步",
                      onPressed: (controller.boardPiecesView.activeMoveIndex == -1)
                          ? null
                          : () {
                              controller.goBackToBoardAfterIndex(controller.boardPiecesView.activeMoveIndex - 1);
                            },
                    ),
                    IconButton(
                      icon: const Icon(Icons.fast_forward),
                      tooltip: "前进一步",
                      onPressed: (controller.boardPiecesView.activeMoveIndex == controller.moveHistory.length - 1 ||
                              controller.moveHistory.isEmpty)
                          ? null
                          : () {
                              controller.goBackToBoardAfterIndex(controller.boardPiecesView.activeMoveIndex + 1);
                            },
                    ),
                    IconButton(
                      icon: const Icon(Icons.skip_next),
                      tooltip: "到结尾",
                      onPressed: controller.moveHistory.isEmpty
                          ? null
                          : () => controller.goBackToBoardAfterIndex(controller.moveHistory.length - 1),
                    ),
                  ],
                )),
          ),
          Expanded(
            child: Obx(() {
              final scrollController = controller.moveHistoryScrollController; // 使用controller中的ScrollController
              final activeMoveIdx = controller.boardPiecesView.activeMoveIndex;
              final moveHistoryNotEmpty = controller.moveHistory.isNotEmpty;

              // 计算itemCount的函数，必须在addPostFrameCallback中使用前定义
              int calculateItemCount() {
                final N = controller.moveHistory.length;
                if (N == 0) return 0;

                final bool blackStartedGame = controller.moveHistory.first.player == Player.black;
                final int k = N - 1; // 最后一步棋的索引
                // 确保在访问controller.moveHistory[k]之前历史记录不为空
                final Player lastPlayer = controller.moveHistory[k].player;

                if (blackStartedGame) {
                  if (lastPlayer == Player.black) {
                    // 最后一步是黑棋
                    return k ~/ 2 + 1;
                  } else {
                    // 最后一步是红棋
                    return (k + 1) ~/ 2 + 1;
                  }
                } else {
                  // 红棋先手
                  if (lastPlayer == Player.red) {
                    // 最后一步是红棋
                    return k ~/ 2 + 1;
                  } else {
                    // 最后一步是黑棋
                    return (k + 1) ~/ 2;
                  }
                }
              }

              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (scrollController.hasClients && moveHistoryNotEmpty) {
                  int targetListIndex;
                  final bool blackStartedGame = controller.moveHistory.first.player == Player.black;

                  if (activeMoveIdx == -1) {
                    targetListIndex = 0;
                  } else {
                    if (blackStartedGame) {
                      if (activeMoveIdx == 0) {
                        targetListIndex = 0;
                      } else {
                        targetListIndex = (activeMoveIdx + 1) ~/ 2;
                      }
                    } else {
                      targetListIndex = activeMoveIdx ~/ 2;
                    }
                  }

                  final itemCount = calculateItemCount(); // 确保calculateItemCount已定义或可访问
                  if (itemCount > 0) {
                    targetListIndex = targetListIndex.clamp(0, itemCount - 1);
                    // 估算行高（可能需要调整或动态计算）
                    // 基于 Padding(vertical: 4.0) * 2 + Text (fontSize: 16)
                    // fontSize 16的典型行高可能在20-24左右。所以，8 + 24 = 32。
                    // 为安全起见，用户已设置为30.0。
                    double estimatedRowHeight = 30.0;
                    double rawOffset = targetListIndex * estimatedRowHeight;

                    // 调整偏移量，使目标项略高于视口顶部
                    // 尝试将其定位在距离顶部约0.5行处，或尽可能靠近中心。
                    // 为简单起见，我们尝试将其向上移动0.5倍行高，
                    // 但不能小于0。
                    double adjustedOffset = rawOffset - (estimatedRowHeight * 0.4);
                    if (adjustedOffset < 0) adjustedOffset = 0;

                    // 确保偏移量在可滚动范围内
                    adjustedOffset = adjustedOffset.clamp(0.0, scrollController.position.maxScrollExtent);

                    scrollController.animateTo(
                      adjustedOffset,
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeOut,
                    );
                  }
                } else if (scrollController.hasClients && !moveHistoryNotEmpty) {
                  // 如果历史记录为空，则滚动到顶部
                  scrollController.animateTo(
                    0.0,
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeOut,
                  );
                }
              });

              return ListView.builder(
                controller: scrollController,
                itemCount: calculateItemCount(), // 使用新的计算函数
                itemBuilder: (context, index) {
                  // index是行号
                  PieceMove? redMoveForDisplay;
                  PieceMove? blackMoveForDisplay;
                  int? redMoveActualHistoryIndex;
                  int? blackMoveActualHistoryIndex;

                  if (controller.moveHistory.isNotEmpty) {
                    // Player枚举应可通过controller或直接导入获得。
                    // 假设代码生成后controller.moveHistory.first.player是有效的。
                    final bool blackStartedGame = controller.moveHistory.first.player == Player.black;

                    if (blackStartedGame) {
                      // 黑棋先手
                      if (index == 0) {
                        // 第一行：红方为"-"，黑方为history[0]
                        redMoveForDisplay = null;
                        if (controller.moveHistory.length > 0) {
                          blackMoveForDisplay = controller.moveHistory[0];
                          blackMoveActualHistoryIndex = 0;
                        }
                      } else {
                        // 黑棋先手时的后续行
                        // 红方着法是history[index*2 - 1]
                        // 黑方着法是history[index*2]
                        redMoveActualHistoryIndex = index * 2 - 1;
                        if (redMoveActualHistoryIndex >= 0 &&
                            redMoveActualHistoryIndex < controller.moveHistory.length) {
                          redMoveForDisplay = controller.moveHistory[redMoveActualHistoryIndex];
                        }
                        blackMoveActualHistoryIndex = index * 2;
                        if (blackMoveActualHistoryIndex >= 0 &&
                            blackMoveActualHistoryIndex < controller.moveHistory.length) {
                          blackMoveForDisplay = controller.moveHistory[blackMoveActualHistoryIndex];
                        }
                      }
                    } else {
                      // 红棋先手（或历史记录为空，已通过初始null值处理）
                      redMoveActualHistoryIndex = index * 2;
                      if (redMoveActualHistoryIndex < controller.moveHistory.length) {
                        redMoveForDisplay = controller.moveHistory[redMoveActualHistoryIndex];
                      }
                      blackMoveActualHistoryIndex = index * 2 + 1;
                      if (blackMoveActualHistoryIndex < controller.moveHistory.length) {
                        blackMoveForDisplay = controller.moveHistory[blackMoveActualHistoryIndex];
                      }
                    }
                  }

                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: 4.0),
                    child: Row(
                      children: [
                        Text(
                          '${index + 1}.',
                          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: redMoveForDisplay != null
                              ? GestureDetector(
                                  onDoubleTap: () async {
                                    if (redMoveActualHistoryIndex != null) {
                                      await controller.goBackToBoardAfterIndex(redMoveActualHistoryIndex);
                                    }
                                  },
                                  child: Tooltip(
                                    message: redMoveForDisplay.iccs,
                                    child: Center(
                                      // 将实际的着法文本居中显示
                                      child: Text(
                                        redMoveForDisplay.chinese,
                                        style: TextStyle(
                                          fontSize: 16,
                                          color: (redMoveActualHistoryIndex != null &&
                                                  redMoveActualHistoryIndex >
                                                      controller.boardPiecesView.activeMoveIndex)
                                              ? Colors.red.withValues(alpha: 0.5)
                                              : Colors.red,
                                        ),
                                      ),
                                    ),
                                  ),
                                )
                              : const Center(child: Text("-", style: TextStyle(fontSize: 16, color: Colors.red))),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: blackMoveForDisplay != null
                              ? GestureDetector(
                                  onDoubleTap: () async {
                                    if (blackMoveActualHistoryIndex != null) {
                                      await controller.goBackToBoardAfterIndex(blackMoveActualHistoryIndex);
                                    }
                                  },
                                  child: Tooltip(
                                    message: blackMoveForDisplay.iccs,
                                    child: Center(
                                      // 将实际的着法文本居中显示
                                      child: Text(
                                        blackMoveForDisplay.chinese,
                                        style: TextStyle(
                                          fontSize: 16,
                                          color: (blackMoveActualHistoryIndex != null &&
                                                  blackMoveActualHistoryIndex >
                                                      controller.boardPiecesView.activeMoveIndex)
                                              ? Colors.black.withValues(alpha: 0.5)
                                              : Colors.black,
                                        ),
                                      ),
                                    ),
                                  ),
                                )
                              : const Center(child: Text("-", style: TextStyle(fontSize: 16, color: Colors.black))),
                        ),
                      ],
                    ),
                  );
                },
              );
            }),
          ),
        ],
      ),
    );
  }
}
