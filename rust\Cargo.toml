[package]
    name   ="rust_lib_meng_ru_ling_shi"
    version="0.1.0"
    edition="2024"

[lib]
    crate-type=["cdylib", "staticlib"]

[profile.release]
    lto          =true
    codegen-units=1
    strip        ="debuginfo"

[profile.dev.package."*"]
    opt-level=3

[dev-dependencies]

[dependencies]
    anyhow             ="*"
    flutter_rust_bridge={ features = [], version = "=2.10.0" }
    once_cell          ="*"
    parking_lot        ="0.12.1"
    phf                ={version="*", features=["macros"]}
    num-traits         ="*"
    num-derive         ="*"

    tokio         ={version="1", features=["full"]}
    futures-util  ="0.3"
    process-stream={git="https://github.com/kkharji/process-stream"}

    rand="0.8.5"

    # 日志模块
    log   ="0.4"
    fern  ={version="0.6.0", features=["date-based", "colored"]}
    atty  ="0.2.14"
    chrono="0.4"
