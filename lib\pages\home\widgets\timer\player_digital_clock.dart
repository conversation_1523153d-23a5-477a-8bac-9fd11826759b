import 'package:flutter/material.dart';
import 'package:meng_ru_ling_shi/common/widgets/time_controller.dart';
import 'package:meng_ru_ling_shi/rust/chess/piece_utils.dart' as chess_utils;

import '../../lib.dart';
import 'digital_font/digital_colon.dart';
import 'digital_font/digital_number.dart';

class PlayerDigitalClock extends GetView<HomeController> {
  final double outerRoundRadius; // Made final
  final double innerRoundRadius; // Made final
  static const double _innerOuterRadiusRatio = 10 / 15; // Made static const

  late final Rx<DigitTimeController> _timeController;

  PlayerDigitalClock(chess_utils.Player player, this.outerRoundRadius, {super.key})
      : innerRoundRadius = _innerOuterRadiusRatio * outerRoundRadius {
    // Initialize in initializer list
    // 使用前缀
    // UI 半径
    // innerRoundRadius = _innerOuterRadiusRatio * outerRoundRadius; // Moved to initializer list

    switch (player) {
      case chess_utils.Player.red: // 使用前缀
        _timeController = controller.redTimeController;
        break;
      case chess_utils.Player.black: // 使用前缀
        _timeController = controller.blackTimeController;
        break;
      default:
        throw Exception('Player is not Red or Black, Something went wrong!');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 145,
      // 外部白色容器
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(outerRoundRadius),
      ),
      // 数字绿色背景
      child: Center(
        child: LayoutBuilder(
          builder: (context, constraints) => Container(
            height: constraints.maxHeight * 0.87,
            width: constraints.maxWidth * 0.95,
            decoration: BoxDecoration(
              gradient:
                  const LinearGradient(colors: [Color.fromRGBO(203, 211, 196, 1), Color.fromRGBO(176, 188, 163, 1)]),
              borderRadius: BorderRadius.circular(innerRoundRadius),
              border: Border.all(
                color: const Color.fromRGBO(168, 168, 168, 1),
                width: 2,
              ),
            ),
            child: Obx(
              () => DigitalClock(
                height: constraints.maxHeight,
                width: constraints.maxWidth,
                seconds: _timeController.value.inSeconds,
                minutes: _timeController.value.inMinutes,
                hours: _timeController.value.inHours,
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class DigitalClock extends StatelessWidget {
  const DigitalClock({
    super.key,
    required this.height,
    required this.width,
    this.hours = 0,
    this.minutes = 0,
    this.seconds = 0,
  });

  final num height;
  final num width;
  final int hours;
  final int minutes;
  final int seconds;

  @override
  Widget build(BuildContext context) {
    List<DigitalNumberWithBG> hourNumber = createNumberTime(hours);
    List<DigitalNumberWithBG> minuteNumber = createNumberTime(minutes);
    List<DigitalNumberWithBG> secondNumber = createNumberTime(seconds);
    return Center(
      child: SizedBox(
        height: height * 1.0, // 0.47,
        width: width * 0.6,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            ...hourNumber,
            DigitalColon(height: height * 0.50, color: Colors.black87),
            ...minuteNumber,
            DigitalColon(height: height * 0.50, color: Colors.black87),
            ...secondNumber,
          ],
        ),
      ),
    );
  }

  List<DigitalNumberWithBG> createNumberTime(int numberTime) {
    final parsedNumberTime = numberTime % 60;
    final isNumberTimeTwoDigits = isNumberTwoDigits(parsedNumberTime);
    final firstNumber = firstDigit(parsedNumberTime);
    final tenDigit = isNumberTimeTwoDigits ? firstNumber : 0;
    final digit = isNumberTimeTwoDigits ? int.parse(parsedNumberTime.toString()[1]) : firstNumber;

    return [
      DigitalNumberWithBG(
        height: height * 0.35,
        value: tenDigit,
      ),
      DigitalNumberWithBG(
        height: height * 0.35,
        value: digit,
      ),
    ];
  }
}

class DigitalNumberWithBG extends StatelessWidget {
  const DigitalNumberWithBG({
    super.key,
    this.value = 0,
    required this.height,
    this.backgroundValue = 8,
  });

  final int value;
  final int backgroundValue;
  final double height;

  final numberScaleRatio = 1.4;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: <Widget>[
        //Foreground(数字本身的颜色)
        DigitalNumber(
          value: value,
          color: Colors.black,
          height: height * numberScaleRatio,
        ),

        // 背景
        DigitalNumber(
          value: backgroundValue,
          color: Colors.black12,
          height: height * numberScaleRatio,
        ),
      ],
    );
  }
}

bool isNumberTwoDigits(int number) {
  return number.toString().length == 2;
}

int firstDigit(int number) {
  return int.parse(number.toString()[0]);
}
