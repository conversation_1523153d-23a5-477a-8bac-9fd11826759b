# 自制基于明确逻辑的引擎思路

一回合（OneRound）：指从当前下棋一方开始的走完交替的两步棋，并不要求红先黑后
可走法（ValidMove）：即排除了走后被叫将的走法

## 新思路

针对每个我方所有可走法，在假设每个走法后的局面，再枚举出对方每个可走法后的局面(设为BoardState)进行“评分”，取“我方视角”最低分为该我方走法的评分；
将该评分作为我方每个走法的评分，排序我方走法并最后选出最高分的走法为该局面我方最优走法。

**评分细节**：当前局面下棋方的各子评分的累加。

**循环计算每个ChessValidMove的score**:
各子评分：针对该子的可走法，记录以下几个字段（如：struct ChessValidMove）：
  - `calc` (此标记似乎不完整，保留原样)
  1. `isChecked`: 是否被将军
  2. `controlPoints`: 掌控点数（即走后我方所有棋子可控制的棋盘中的交叉点数，重叠的的点数需累计，且其中叫将的点数基为2）
  3. `antiControlPoints`: 被掌控点数（即走后对方所有棋子可控制的棋盘中的交叉点数，重叠的的点数需累计，且其中叫将的点数基为2）

对每个走法计算评分（score）：对每个BoardState计算（被叫将为0，否则为1）* 掌控点数；

若没BoardState，说明对方无走法，则评分为max（则直接break）；
否则加入scores容器；

排序scores容器，选出最高分的走法；

## 老思路

1.  枚举出我方所有“有效”走法；
    - 有效是指走了后“本回合中”对方不存在某步棋可直接吃掉我方将的走法。
2.  遍历1中的我方所有走法，针对每个走法，枚举出对方所有“有效”走法；
    1.  若当前局面下我方无“有效”子可下，则直接认输；
    2.  将我方的所有效走法（即列表中的对象）按照其之后对方可选有效走法的数量排序---越少越优先；
    3.  遍历2.2中排序后的我方走法，若是某个走法下对方无有效走法，则直接返回这个（即绝杀或困毙的赢棋走法）；
    4.  遍历2.2中排序后的我方走法，针对【我方本回合叫将】且【对方本回合可吃掉该叫将走子（只可能是我方“单子”叫将）或对方反将】的走法，若接下来的回合我方可连叫（符合棋规情况下）直至绝杀的话，则直接返回该我方走法，否则剔除该走法；
    5.  遍历2.4中排序后的我方走法，剔除掉对方可连叫（符合棋规情况下）直至绝杀的我方走法；
3.  对2.5之后剩下的我方候选走法，将“本回合后”的局面按以下标准按优先级（以下数字越小越优先）排序，选出最高分的走法：
    1.  排序优先级1的指标：本回合后，设所有我方（除了老将）的某棋子到对方将的直线距离为d，该棋子所有”有效“走法的走法数为c（这个c须剔除掉走后对方可吃掉该棋子的走法），则该棋子得分为s=(1/d)*c，我方这手棋的最低得分为依据对方不同走子后的局面得分后求Min(s)---将此最低得分作为这手棋的指标；
    2.  排序优先级2的指标：本回合仅我方走后，对方可选走法的数量（即2.2中所用的属性）；
    3.  排序优先级3的指标：将本回合我方叫将的走法设置优先；
    4.  排序优先级4的指标：将本回合后我方没有被将军的局面设置优先；
