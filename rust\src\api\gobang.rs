use flutter_rust_bridge::frb;

/// 棋子类型
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq)]
pub enum PieceType {
    Empty,
    Black,
    White,
}

/// 游戏模式
#[derive(Debug, <PERSON>lone, <PERSON>py, PartialEq, Eq)]
pub enum GameMode {
    HumanVsHuman,
    HumanVsAI, // 为未来扩展预留
    AIVsAI,    // 为未来扩展预留
}

/// 游戏状态
#[derive(Debu<PERSON>, <PERSON><PERSON>, Co<PERSON>, PartialEq, Eq)]
pub enum GameState {
    Playing,
    BlackWin,
    WhiteWin,
    Draw,
}

/// 棋盘位置
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub struct Position {
    pub row: i32,
    pub col: i32,
}

/// 五子棋游戏核心
#[frb(opaque)]
#[derive(Debug, Clone)]
pub struct GobangGame {
    board: Vec<Vec<PieceType>>,
    current_player: PieceType,
    game_state: GameState,
    game_mode: GameMode,
    move_history: Vec<Position>,
    board_size: usize,
}

impl Default for GobangGame {
    fn default() -> Self {
        Self::new(15, GameMode::HumanVsHuman)
    }
}

impl GobangGame {
    /// 创建新游戏
    pub fn new(board_size: usize, game_mode: GameMode) -> Self {
        let board = vec![vec![PieceType::Empty; board_size]; board_size];
        Self {
            board,
            current_player: PieceType::Black,
            game_state: GameState::Playing,
            game_mode,
            move_history: Vec::new(),
            board_size,
        }
    }

    /// 获取棋盘大小
    pub fn get_board_size(&self) -> i32 {
        self.board_size as i32
    }

    /// 获取指定位置的棋子
    pub fn get_piece(&self, row: i32, col: i32) -> PieceType {
        if self.is_valid_position(row, col) {
            self.board[row as usize][col as usize]
        } else {
            PieceType::Empty
        }
    }

    /// 获取当前玩家
    pub fn get_current_player(&self) -> PieceType {
        self.current_player
    }

    /// 获取游戏状态
    pub fn get_game_state(&self) -> GameState {
        self.game_state
    }

    /// 获取游戏模式
    pub fn get_game_mode(&self) -> GameMode {
        self.game_mode
    }

    /// 获取移动历史数量
    pub fn get_move_count(&self) -> i32 {
        self.move_history.len() as i32
    }

    /// 检查位置是否有效
    fn is_valid_position(&self, row: i32, col: i32) -> bool {
        row >= 0 && row < self.board_size as i32 && col >= 0 && col < self.board_size as i32
    }

    /// 尝试落子
    pub fn make_move(&mut self, row: i32, col: i32) -> bool {
        if self.game_state != GameState::Playing {
            return false;
        }

        if !self.is_valid_position(row, col) {
            return false;
        }

        if self.board[row as usize][col as usize] != PieceType::Empty {
            return false;
        }

        // 落子
        self.board[row as usize][col as usize] = self.current_player;
        self.move_history.push(Position { row, col });

        // 检查胜负
        if self.check_win(row, col) {
            self.game_state = match self.current_player {
                PieceType::Black => GameState::BlackWin,
                PieceType::White => GameState::WhiteWin,
                PieceType::Empty => GameState::Playing, // 不应该发生
            };
        } else if self.move_history.len() == self.board_size * self.board_size {
            self.game_state = GameState::Draw;
        }

        // 切换玩家
        if self.game_state == GameState::Playing {
            self.current_player = match self.current_player {
                PieceType::Black => PieceType::White,
                PieceType::White => PieceType::Black,
                PieceType::Empty => PieceType::Black, // 不应该发生
            };
        }

        true
    }

    /// 检查是否获胜（无禁手规则）
    fn check_win(&self, row: i32, col: i32) -> bool {
        let piece = self.board[row as usize][col as usize];
        if piece == PieceType::Empty {
            return false;
        }

        // 四个方向：水平、垂直、主对角线、副对角线
        let directions = [(0, 1), (1, 0), (1, 1), (1, -1)];

        for (dr, dc) in directions {
            let mut count = 1; // 包含当前棋子

            // 正方向计数
            let mut r = row + dr;
            let mut c = col + dc;
            while self.is_valid_position(r, c) && self.board[r as usize][c as usize] == piece {
                count += 1;
                r += dr;
                c += dc;
            }

            // 反方向计数
            r = row - dr;
            c = col - dc;
            while self.is_valid_position(r, c) && self.board[r as usize][c as usize] == piece {
                count += 1;
                r -= dr;
                c -= dc;
            }

            if count >= 5 {
                return true;
            }
        }

        false
    }

    /// 重置游戏
    pub fn reset(&mut self) {
        self.board = vec![vec![PieceType::Empty; self.board_size]; self.board_size];
        self.current_player = PieceType::Black;
        self.game_state = GameState::Playing;
        self.move_history.clear();
    }

    /// 悔棋
    pub fn undo_move(&mut self) -> bool {
        if self.move_history.is_empty() {
            return false;
        }

        let last_move = self.move_history.pop().unwrap();
        self.board[last_move.row as usize][last_move.col as usize] = PieceType::Empty;

        // 恢复游戏状态
        self.game_state = GameState::Playing;

        // 切换回上一个玩家
        self.current_player = match self.current_player {
            PieceType::Black => PieceType::White,
            PieceType::White => PieceType::Black,
            PieceType::Empty => PieceType::Black, // 不应该发生
        };

        true
    }
}

/// Flutter接口函数
#[frb(sync)]
pub fn create_gobang_game() -> GobangGame {
    GobangGame::default()
}

#[frb(sync)]
pub fn game_make_move(game: &mut GobangGame, row: i32, col: i32) -> bool {
    game.make_move(row, col)
}

#[frb(sync)]
pub fn game_get_piece(game: &GobangGame, row: i32, col: i32) -> PieceType {
    game.get_piece(row, col)
}

#[frb(sync)]
pub fn game_get_current_player(game: &GobangGame) -> PieceType {
    game.get_current_player()
}

#[frb(sync)]
pub fn game_get_state(game: &GobangGame) -> GameState {
    game.get_game_state()
}

#[frb(sync)]
pub fn game_reset(game: &mut GobangGame) {
    game.reset()
}

#[frb(sync)]
pub fn game_undo(game: &mut GobangGame) -> bool {
    game.undo_move()
}

#[frb(sync)]
pub fn game_get_board_size(game: &GobangGame) -> i32 {
    game.get_board_size()
}

#[frb(sync)]
pub fn game_get_move_count(game: &GobangGame) -> i32 {
    game.get_move_count()
}
