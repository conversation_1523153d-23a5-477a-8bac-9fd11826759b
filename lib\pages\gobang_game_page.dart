import 'package:flutter/material.dart';
import 'package:xly/xly.dart';

import '../widgets/game_info_panel.dart';
import '../widgets/gobang_board.dart';

/// 五子棋游戏主页面
class GobangGamePage extends StatelessWidget {
  const GobangGamePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5DC), // 米色背景
      body: SafeArea(
        child: LayoutBuilder(
          builder: (context, constraints) {
            // 计算可用空间
            final availableHeight = constraints.maxHeight;
            final spacing = 8.w;

            // 棋盘应该是正方形，使用可用高度作为参考
            final boardSize = availableHeight;

            return Flex(
              direction: Axis.horizontal,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 左侧棋盘区域 - flex: 3 (75%)
                Flexible(
                  flex: 3,
                  child: SizedBox(
                    width: boardSize,
                    height: boardSize,
                    child: const GobangBoard(),
                  ),
                ),

                SizedBox(width: spacing),

                // 右侧信息面板区域 - flex: 1 (25%)
                Flexible(flex: 1, child: const GameInfoPanel()),
              ],
            );
          },
        ),
      ),
    );
  }
}

/// 游戏结束对话框
class GameOverDialog extends StatelessWidget {
  final String winner;
  final VoidCallback onRestart;
  final VoidCallback onClose;

  const GameOverDialog({
    super.key,
    required this.winner,
    required this.onRestart,
    required this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Container(
        padding: EdgeInsets.all(24.w),
        decoration: BoxDecoration(
          color: const Color(0xFFF5F5DC),
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(color: const Color(0xFF8B4513), width: 2.w),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题
            Text(
              '游戏结束',
              style: TextStyle(
                fontSize: 20.sp,
                fontWeight: FontWeight.bold,
                color: const Color(0xFF8B4513),
              ),
            ),

            SizedBox(height: 16.h),

            // 获胜者信息
            Container(
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(color: const Color(0xFFD2B48C)),
              ),
              child: Column(
                children: [
                  Icon(Icons.emoji_events, size: 48.w, color: Colors.amber),
                  SizedBox(height: 8.h),
                  Text(
                    winner,
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF8B4513),
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(height: 24.h),

            // 按钮
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: onRestart,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF8B4513),
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(vertical: 12.h),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                    ),
                    child: Text('再来一局', style: TextStyle(fontSize: 14.sp)),
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: OutlinedButton(
                    onPressed: onClose,
                    style: OutlinedButton.styleFrom(
                      foregroundColor: const Color(0xFF8B4513),
                      side: const BorderSide(color: Color(0xFF8B4513)),
                      padding: EdgeInsets.symmetric(vertical: 12.h),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                    ),
                    child: Text('关闭', style: TextStyle(fontSize: 14.sp)),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
