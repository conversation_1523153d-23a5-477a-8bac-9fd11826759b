// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'game_manager.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$PlacementValidity {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() valid,
    required TResult Function(String field0) invalidLocation,
    required TResult Function(String field0) maxPiecesReached,
    required TResult Function(String field0) kingsFacing,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? valid,
    TResult? Function(String field0)? invalidLocation,
    TResult? Function(String field0)? maxPiecesReached,
    TResult? Function(String field0)? kingsFacing,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? valid,
    TResult Function(String field0)? invalidLocation,
    TResult Function(String field0)? maxPiecesReached,
    TResult Function(String field0)? kingsFacing,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(PlacementValidity_Valid value) valid,
    required TResult Function(PlacementValidity_InvalidLocation value)
        invalidLocation,
    required TResult Function(PlacementValidity_MaxPiecesReached value)
        maxPiecesReached,
    required TResult Function(PlacementValidity_KingsFacing value) kingsFacing,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(PlacementValidity_Valid value)? valid,
    TResult? Function(PlacementValidity_InvalidLocation value)? invalidLocation,
    TResult? Function(PlacementValidity_MaxPiecesReached value)?
        maxPiecesReached,
    TResult? Function(PlacementValidity_KingsFacing value)? kingsFacing,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(PlacementValidity_Valid value)? valid,
    TResult Function(PlacementValidity_InvalidLocation value)? invalidLocation,
    TResult Function(PlacementValidity_MaxPiecesReached value)?
        maxPiecesReached,
    TResult Function(PlacementValidity_KingsFacing value)? kingsFacing,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PlacementValidityCopyWith<$Res> {
  factory $PlacementValidityCopyWith(
          PlacementValidity value, $Res Function(PlacementValidity) then) =
      _$PlacementValidityCopyWithImpl<$Res, PlacementValidity>;
}

/// @nodoc
class _$PlacementValidityCopyWithImpl<$Res, $Val extends PlacementValidity>
    implements $PlacementValidityCopyWith<$Res> {
  _$PlacementValidityCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$PlacementValidity_ValidImplCopyWith<$Res> {
  factory _$$PlacementValidity_ValidImplCopyWith(
          _$PlacementValidity_ValidImpl value,
          $Res Function(_$PlacementValidity_ValidImpl) then) =
      __$$PlacementValidity_ValidImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$PlacementValidity_ValidImplCopyWithImpl<$Res>
    extends _$PlacementValidityCopyWithImpl<$Res, _$PlacementValidity_ValidImpl>
    implements _$$PlacementValidity_ValidImplCopyWith<$Res> {
  __$$PlacementValidity_ValidImplCopyWithImpl(
      _$PlacementValidity_ValidImpl _value,
      $Res Function(_$PlacementValidity_ValidImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$PlacementValidity_ValidImpl extends PlacementValidity_Valid {
  const _$PlacementValidity_ValidImpl() : super._();

  @override
  String toString() {
    return 'PlacementValidity.valid()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PlacementValidity_ValidImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() valid,
    required TResult Function(String field0) invalidLocation,
    required TResult Function(String field0) maxPiecesReached,
    required TResult Function(String field0) kingsFacing,
  }) {
    return valid();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? valid,
    TResult? Function(String field0)? invalidLocation,
    TResult? Function(String field0)? maxPiecesReached,
    TResult? Function(String field0)? kingsFacing,
  }) {
    return valid?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? valid,
    TResult Function(String field0)? invalidLocation,
    TResult Function(String field0)? maxPiecesReached,
    TResult Function(String field0)? kingsFacing,
    required TResult orElse(),
  }) {
    if (valid != null) {
      return valid();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(PlacementValidity_Valid value) valid,
    required TResult Function(PlacementValidity_InvalidLocation value)
        invalidLocation,
    required TResult Function(PlacementValidity_MaxPiecesReached value)
        maxPiecesReached,
    required TResult Function(PlacementValidity_KingsFacing value) kingsFacing,
  }) {
    return valid(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(PlacementValidity_Valid value)? valid,
    TResult? Function(PlacementValidity_InvalidLocation value)? invalidLocation,
    TResult? Function(PlacementValidity_MaxPiecesReached value)?
        maxPiecesReached,
    TResult? Function(PlacementValidity_KingsFacing value)? kingsFacing,
  }) {
    return valid?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(PlacementValidity_Valid value)? valid,
    TResult Function(PlacementValidity_InvalidLocation value)? invalidLocation,
    TResult Function(PlacementValidity_MaxPiecesReached value)?
        maxPiecesReached,
    TResult Function(PlacementValidity_KingsFacing value)? kingsFacing,
    required TResult orElse(),
  }) {
    if (valid != null) {
      return valid(this);
    }
    return orElse();
  }
}

abstract class PlacementValidity_Valid extends PlacementValidity {
  const factory PlacementValidity_Valid() = _$PlacementValidity_ValidImpl;
  const PlacementValidity_Valid._() : super._();
}

/// @nodoc
abstract class _$$PlacementValidity_InvalidLocationImplCopyWith<$Res> {
  factory _$$PlacementValidity_InvalidLocationImplCopyWith(
          _$PlacementValidity_InvalidLocationImpl value,
          $Res Function(_$PlacementValidity_InvalidLocationImpl) then) =
      __$$PlacementValidity_InvalidLocationImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String field0});
}

/// @nodoc
class __$$PlacementValidity_InvalidLocationImplCopyWithImpl<$Res>
    extends _$PlacementValidityCopyWithImpl<$Res,
        _$PlacementValidity_InvalidLocationImpl>
    implements _$$PlacementValidity_InvalidLocationImplCopyWith<$Res> {
  __$$PlacementValidity_InvalidLocationImplCopyWithImpl(
      _$PlacementValidity_InvalidLocationImpl _value,
      $Res Function(_$PlacementValidity_InvalidLocationImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? field0 = null,
  }) {
    return _then(_$PlacementValidity_InvalidLocationImpl(
      null == field0
          ? _value.field0
          : field0 // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$PlacementValidity_InvalidLocationImpl
    extends PlacementValidity_InvalidLocation {
  const _$PlacementValidity_InvalidLocationImpl(this.field0) : super._();

  @override
  final String field0;

  @override
  String toString() {
    return 'PlacementValidity.invalidLocation(field0: $field0)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PlacementValidity_InvalidLocationImpl &&
            (identical(other.field0, field0) || other.field0 == field0));
  }

  @override
  int get hashCode => Object.hash(runtimeType, field0);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PlacementValidity_InvalidLocationImplCopyWith<
          _$PlacementValidity_InvalidLocationImpl>
      get copyWith => __$$PlacementValidity_InvalidLocationImplCopyWithImpl<
          _$PlacementValidity_InvalidLocationImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() valid,
    required TResult Function(String field0) invalidLocation,
    required TResult Function(String field0) maxPiecesReached,
    required TResult Function(String field0) kingsFacing,
  }) {
    return invalidLocation(field0);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? valid,
    TResult? Function(String field0)? invalidLocation,
    TResult? Function(String field0)? maxPiecesReached,
    TResult? Function(String field0)? kingsFacing,
  }) {
    return invalidLocation?.call(field0);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? valid,
    TResult Function(String field0)? invalidLocation,
    TResult Function(String field0)? maxPiecesReached,
    TResult Function(String field0)? kingsFacing,
    required TResult orElse(),
  }) {
    if (invalidLocation != null) {
      return invalidLocation(field0);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(PlacementValidity_Valid value) valid,
    required TResult Function(PlacementValidity_InvalidLocation value)
        invalidLocation,
    required TResult Function(PlacementValidity_MaxPiecesReached value)
        maxPiecesReached,
    required TResult Function(PlacementValidity_KingsFacing value) kingsFacing,
  }) {
    return invalidLocation(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(PlacementValidity_Valid value)? valid,
    TResult? Function(PlacementValidity_InvalidLocation value)? invalidLocation,
    TResult? Function(PlacementValidity_MaxPiecesReached value)?
        maxPiecesReached,
    TResult? Function(PlacementValidity_KingsFacing value)? kingsFacing,
  }) {
    return invalidLocation?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(PlacementValidity_Valid value)? valid,
    TResult Function(PlacementValidity_InvalidLocation value)? invalidLocation,
    TResult Function(PlacementValidity_MaxPiecesReached value)?
        maxPiecesReached,
    TResult Function(PlacementValidity_KingsFacing value)? kingsFacing,
    required TResult orElse(),
  }) {
    if (invalidLocation != null) {
      return invalidLocation(this);
    }
    return orElse();
  }
}

abstract class PlacementValidity_InvalidLocation extends PlacementValidity {
  const factory PlacementValidity_InvalidLocation(final String field0) =
      _$PlacementValidity_InvalidLocationImpl;
  const PlacementValidity_InvalidLocation._() : super._();

  String get field0;
  @JsonKey(ignore: true)
  _$$PlacementValidity_InvalidLocationImplCopyWith<
          _$PlacementValidity_InvalidLocationImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$PlacementValidity_MaxPiecesReachedImplCopyWith<$Res> {
  factory _$$PlacementValidity_MaxPiecesReachedImplCopyWith(
          _$PlacementValidity_MaxPiecesReachedImpl value,
          $Res Function(_$PlacementValidity_MaxPiecesReachedImpl) then) =
      __$$PlacementValidity_MaxPiecesReachedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String field0});
}

/// @nodoc
class __$$PlacementValidity_MaxPiecesReachedImplCopyWithImpl<$Res>
    extends _$PlacementValidityCopyWithImpl<$Res,
        _$PlacementValidity_MaxPiecesReachedImpl>
    implements _$$PlacementValidity_MaxPiecesReachedImplCopyWith<$Res> {
  __$$PlacementValidity_MaxPiecesReachedImplCopyWithImpl(
      _$PlacementValidity_MaxPiecesReachedImpl _value,
      $Res Function(_$PlacementValidity_MaxPiecesReachedImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? field0 = null,
  }) {
    return _then(_$PlacementValidity_MaxPiecesReachedImpl(
      null == field0
          ? _value.field0
          : field0 // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$PlacementValidity_MaxPiecesReachedImpl
    extends PlacementValidity_MaxPiecesReached {
  const _$PlacementValidity_MaxPiecesReachedImpl(this.field0) : super._();

  @override
  final String field0;

  @override
  String toString() {
    return 'PlacementValidity.maxPiecesReached(field0: $field0)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PlacementValidity_MaxPiecesReachedImpl &&
            (identical(other.field0, field0) || other.field0 == field0));
  }

  @override
  int get hashCode => Object.hash(runtimeType, field0);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PlacementValidity_MaxPiecesReachedImplCopyWith<
          _$PlacementValidity_MaxPiecesReachedImpl>
      get copyWith => __$$PlacementValidity_MaxPiecesReachedImplCopyWithImpl<
          _$PlacementValidity_MaxPiecesReachedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() valid,
    required TResult Function(String field0) invalidLocation,
    required TResult Function(String field0) maxPiecesReached,
    required TResult Function(String field0) kingsFacing,
  }) {
    return maxPiecesReached(field0);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? valid,
    TResult? Function(String field0)? invalidLocation,
    TResult? Function(String field0)? maxPiecesReached,
    TResult? Function(String field0)? kingsFacing,
  }) {
    return maxPiecesReached?.call(field0);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? valid,
    TResult Function(String field0)? invalidLocation,
    TResult Function(String field0)? maxPiecesReached,
    TResult Function(String field0)? kingsFacing,
    required TResult orElse(),
  }) {
    if (maxPiecesReached != null) {
      return maxPiecesReached(field0);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(PlacementValidity_Valid value) valid,
    required TResult Function(PlacementValidity_InvalidLocation value)
        invalidLocation,
    required TResult Function(PlacementValidity_MaxPiecesReached value)
        maxPiecesReached,
    required TResult Function(PlacementValidity_KingsFacing value) kingsFacing,
  }) {
    return maxPiecesReached(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(PlacementValidity_Valid value)? valid,
    TResult? Function(PlacementValidity_InvalidLocation value)? invalidLocation,
    TResult? Function(PlacementValidity_MaxPiecesReached value)?
        maxPiecesReached,
    TResult? Function(PlacementValidity_KingsFacing value)? kingsFacing,
  }) {
    return maxPiecesReached?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(PlacementValidity_Valid value)? valid,
    TResult Function(PlacementValidity_InvalidLocation value)? invalidLocation,
    TResult Function(PlacementValidity_MaxPiecesReached value)?
        maxPiecesReached,
    TResult Function(PlacementValidity_KingsFacing value)? kingsFacing,
    required TResult orElse(),
  }) {
    if (maxPiecesReached != null) {
      return maxPiecesReached(this);
    }
    return orElse();
  }
}

abstract class PlacementValidity_MaxPiecesReached extends PlacementValidity {
  const factory PlacementValidity_MaxPiecesReached(final String field0) =
      _$PlacementValidity_MaxPiecesReachedImpl;
  const PlacementValidity_MaxPiecesReached._() : super._();

  String get field0;
  @JsonKey(ignore: true)
  _$$PlacementValidity_MaxPiecesReachedImplCopyWith<
          _$PlacementValidity_MaxPiecesReachedImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$PlacementValidity_KingsFacingImplCopyWith<$Res> {
  factory _$$PlacementValidity_KingsFacingImplCopyWith(
          _$PlacementValidity_KingsFacingImpl value,
          $Res Function(_$PlacementValidity_KingsFacingImpl) then) =
      __$$PlacementValidity_KingsFacingImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String field0});
}

/// @nodoc
class __$$PlacementValidity_KingsFacingImplCopyWithImpl<$Res>
    extends _$PlacementValidityCopyWithImpl<$Res,
        _$PlacementValidity_KingsFacingImpl>
    implements _$$PlacementValidity_KingsFacingImplCopyWith<$Res> {
  __$$PlacementValidity_KingsFacingImplCopyWithImpl(
      _$PlacementValidity_KingsFacingImpl _value,
      $Res Function(_$PlacementValidity_KingsFacingImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? field0 = null,
  }) {
    return _then(_$PlacementValidity_KingsFacingImpl(
      null == field0
          ? _value.field0
          : field0 // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$PlacementValidity_KingsFacingImpl
    extends PlacementValidity_KingsFacing {
  const _$PlacementValidity_KingsFacingImpl(this.field0) : super._();

  @override
  final String field0;

  @override
  String toString() {
    return 'PlacementValidity.kingsFacing(field0: $field0)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PlacementValidity_KingsFacingImpl &&
            (identical(other.field0, field0) || other.field0 == field0));
  }

  @override
  int get hashCode => Object.hash(runtimeType, field0);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PlacementValidity_KingsFacingImplCopyWith<
          _$PlacementValidity_KingsFacingImpl>
      get copyWith => __$$PlacementValidity_KingsFacingImplCopyWithImpl<
          _$PlacementValidity_KingsFacingImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() valid,
    required TResult Function(String field0) invalidLocation,
    required TResult Function(String field0) maxPiecesReached,
    required TResult Function(String field0) kingsFacing,
  }) {
    return kingsFacing(field0);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? valid,
    TResult? Function(String field0)? invalidLocation,
    TResult? Function(String field0)? maxPiecesReached,
    TResult? Function(String field0)? kingsFacing,
  }) {
    return kingsFacing?.call(field0);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? valid,
    TResult Function(String field0)? invalidLocation,
    TResult Function(String field0)? maxPiecesReached,
    TResult Function(String field0)? kingsFacing,
    required TResult orElse(),
  }) {
    if (kingsFacing != null) {
      return kingsFacing(field0);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(PlacementValidity_Valid value) valid,
    required TResult Function(PlacementValidity_InvalidLocation value)
        invalidLocation,
    required TResult Function(PlacementValidity_MaxPiecesReached value)
        maxPiecesReached,
    required TResult Function(PlacementValidity_KingsFacing value) kingsFacing,
  }) {
    return kingsFacing(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(PlacementValidity_Valid value)? valid,
    TResult? Function(PlacementValidity_InvalidLocation value)? invalidLocation,
    TResult? Function(PlacementValidity_MaxPiecesReached value)?
        maxPiecesReached,
    TResult? Function(PlacementValidity_KingsFacing value)? kingsFacing,
  }) {
    return kingsFacing?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(PlacementValidity_Valid value)? valid,
    TResult Function(PlacementValidity_InvalidLocation value)? invalidLocation,
    TResult Function(PlacementValidity_MaxPiecesReached value)?
        maxPiecesReached,
    TResult Function(PlacementValidity_KingsFacing value)? kingsFacing,
    required TResult orElse(),
  }) {
    if (kingsFacing != null) {
      return kingsFacing(this);
    }
    return orElse();
  }
}

abstract class PlacementValidity_KingsFacing extends PlacementValidity {
  const factory PlacementValidity_KingsFacing(final String field0) =
      _$PlacementValidity_KingsFacingImpl;
  const PlacementValidity_KingsFacing._() : super._();

  String get field0;
  @JsonKey(ignore: true)
  _$$PlacementValidity_KingsFacingImplCopyWith<
          _$PlacementValidity_KingsFacingImpl>
      get copyWith => throw _privateConstructorUsedError;
}
