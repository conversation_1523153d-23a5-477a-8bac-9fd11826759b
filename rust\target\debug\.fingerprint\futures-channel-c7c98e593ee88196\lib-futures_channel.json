{"rustc": 16591470773350601817, "features": "[\"alloc\", \"futures-sink\", \"sink\", \"std\"]", "declared_features": "[\"alloc\", \"cfg-target-has-atomic\", \"default\", \"futures-sink\", \"sink\", \"std\", \"unstable\"]", "target": 13634065851578929263, "profile": 12506755554509207344, "path": 17150008948835791108, "deps": [[7013762810557009322, "futures_sink", false, 2326873239644073587], [7620660491849607393, "futures_core", false, 15611546190662880035]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-channel-c7c98e593ee88196\\dep-lib-futures_channel", "checksum": false}}], "rustflags": ["--cfg", "frb_expand"], "config": 2069994364910194474, "compile_kind": 0}