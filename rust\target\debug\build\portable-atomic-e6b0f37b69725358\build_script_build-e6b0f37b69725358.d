D:\DATA\BaiduSyncdisk\project\personal\test_repo\test_gobang11\rust\target\debug\build\portable-atomic-e6b0f37b69725358\build_script_build-e6b0f37b69725358.exe: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\portable-atomic-1.11.1\build.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\portable-atomic-1.11.1\version.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\portable-atomic-1.11.1\src\gen\build.rs

D:\DATA\BaiduSyncdisk\project\personal\test_repo\test_gobang11\rust\target\debug\build\portable-atomic-e6b0f37b69725358\build_script_build-e6b0f37b69725358.d: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\portable-atomic-1.11.1\build.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\portable-atomic-1.11.1\version.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\portable-atomic-1.11.1\src\gen\build.rs

C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\portable-atomic-1.11.1\build.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\portable-atomic-1.11.1\version.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\portable-atomic-1.11.1\src\gen\build.rs:

# env-dep:CARGO_PKG_NAME=portable-atomic
