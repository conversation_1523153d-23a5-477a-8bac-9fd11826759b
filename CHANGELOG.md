# 更新日志 (Changelog)

本项目的所有重要变更都记录在此文件中。

## [2025-05-22]
- **修复数字相关问题**: 解决了在特定场景下数字显示或计算不准确的问题。[git: ea23b26]
- **浮动工具栏(FloatBoxPanel)重构与问题修复**:
    - 将`FloatBoxPanel`重构为使用专门的`FloatBoxController` (GetXController)进行状态管理，以优化其内部逻辑和响应性。
    - 修复了在与`HomeController`交互时，因直接在布尔类型Getter（如`isFlipped`, `isArrangeMode`）返回的`bool`值上错误调用`.value`而导致的`NoSuchMethodError`。
    - 注意：此重构和修复并未解决`integration_test/float_bar_btns_test.dart`中存在的集成测试连续运行失败的问题。[git: ba3fd49]
- **集成测试添加**: 新增`integration_test/float_bar_btns_test.dart`文件，用于测试浮动工具栏中“新对局”和“设置”按钮的核心功能。目前各测试用例可单独通过，但集成运行时从第二个测试开始失败，可能与GetX状态管理在测试间未正确重置有关。[git: 5104579]
- **代码重构**: 统一了Dart和Rust中与棋盘数组（90格 vs 256格）及索引转换相关的函数命名，以提高代码清晰度和一致性。主要变更包括：
- **代码重构**: 统一了Dart和Rust中与棋盘数组（90格 vs 256格）及索引转换相关的函数命名，以提高代码清晰度和一致性。主要变更包括：
    - Rust侧：
        - `convert_16x16_index_to_90_array_index` -> `convert_array_256_index_to_array_90_index`
        - `convert_256_board_to_90_board` -> `convert_board_256_to_board_90`
        - `convert_90_array_index_to_16x16_index` -> `convert_array_90_index_to_array_256_index`
        - `convert_90_board_to_256_board` -> `convert_board_90_to_board_256`
        - `get_board_index_from_row_col` -> `get_array_256_index_from_board_row_col`
    - Dart侧对应函数也进行了相应重命名，例如：
        - `convert16X16IndexTo90ArrayIndex` -> `convertArray256IndexToArray90Index`
        - `convert256BoardTo90Board` -> `convertBoard256ToBoard90`
        - `getBoardIndexFromRowCol` -> `getArray256IndexFromBoardRowCol`
    - 所有相关的函数调用、FFI桥接代码及单元测试均已同步更新。[git: 75eed02]

## [2025-05-21]
- **浮动工具栏(FloatBoxPanel)重构与问题修复**:
    - 将`FloatBoxPanel`重构为使用专门的`FloatBoxController` (GetXController)进行状态管理，以优化其内部逻辑和响应性。
    - 修复了在与`HomeController`交互时，因直接在布尔类型Getter（如`isFlipped`, `isArrangeMode`）返回的`bool`值上错误调用`.value`而导致的`NoSuchMethodError`。
    - 注意：此重构和修复并未解决`integration_test/float_bar_btns_test.dart`中存在的集成测试连续运行失败的问题。[git: ba3fd49]
- **集成测试添加**: 新增`integration_test/float_bar_btns_test.dart`文件，用于测试浮动工具栏中“新对局”和“设置”按钮的核心功能。目前各测试用例可单独通过，但集成运行时从第二个测试开始失败，可能与GetX状态管理在测试间未正确重置有关。[git: 5104579]
- **代码重构**: 统一了Dart和Rust中与棋盘数组（90格 vs 256格）及索引转换相关的函数命名，以提高代码清晰度和一致性。主要变更包括：
- **代码重构**: 统一了Dart和Rust中与棋盘数组（90格 vs 256格）及索引转换相关的函数命名，以提高代码清晰度和一致性。主要变更包括：
    - Rust侧：
        - `convert_16x16_index_to_90_array_index` -> `convert_array_256_index_to_array_90_index`
        - `convert_256_board_to_90_board` -> `convert_board_256_to_board_90`
        - `convert_90_array_index_to_16x16_index` -> `convert_array_90_index_to_array_256_index`
        - `convert_90_board_to_256_board` -> `convert_board_90_to_board_256`
        - `get_board_index_from_row_col` -> `get_array_256_index_from_board_row_col`
    - Dart侧对应函数也进行了相应重命名，例如：
        - `convert16X16IndexTo90ArrayIndex` -> `convertArray256IndexToArray90Index`
        - `convert256BoardTo90Board` -> `convertBoard256ToBoard90`
        - `getBoardIndexFromRowCol` -> `getArray256IndexFromBoardRowCol`
    - 所有相关的函数调用、FFI桥接代码及单元测试均已同步更新。[git: 75eed02]

## [2025-05-21]
- **Rust端棋盘翻转与坐标系统重构**:
    - `GameManager` 引入 `is_flipped: bool` 状态，表示用户棋盘视角是否翻转。
    - `GameManager.board` 内部始终以标准“黑上红下”存储。
    - FEN加载时能自动检测并设置 `is_flipped`。
    - 新增 `flip_board()` (切换翻转状态并返回新视角棋盘) 和 `get_display_board()` (返回当前视角棋盘) API。
    - 接收行列坐标的公共API (如 `update_board`, `is_piece_valid_move` 等) 修改为接收“视觉坐标”，并在内部根据 `is_flipped` 转换为“逻辑坐标”进行操作。
- **Dart端适配Rust坐标系统重构**:
    - `HomeController` 中的 `isFlipped` 状态 (`_isFlipped.value`) 与Rust `GameManager.isFlipped` 同步。
    - 调用Rust相关API (如 `updateBoard`, `isPieceValidMove`, `placePieceOnBoard`) 时，正确传递视觉坐标或将Dart侧存储的逻辑坐标转换为视觉坐标。
    - `HomeController` 中的 `flipBoard` 方法更新为调用 `gameManager.flipBoard()` 并同步 `_isFlipped.value`。
    - `getChessPosFromOffset` 和 `onMouseHover` 中的坐标转换逻辑已适配新的 `isFlipped` 状态管理。
    - 修复了 `lib/game/placement_validator.dart` 和 `lib/pages/home/<USER>
- **修复FEN粘贴与棋盘翻转状态同步问题**：
    - 确保通过FEN粘贴棋盘时，能正确更新并同步棋盘的`is_flipped`（翻转）状态。当粘贴代表“红上黑下”棋局的FEN时，应用能正确识别其为翻转状态，并在Rust核心逻辑 (`GameManager.is_flipped`) 和Dart UI层面 (`HomeController._isFlipped.value`，包括浮动工具栏的翻转按钮激活状态) 保持一致。
    - 此修复通过修改Dart端`HomeController.copyClipboardFenToBoard()`方法，使其调用`GameManager.loadFen()`（该方法在Rust内部正确处理FEN解析、方向检测及状态设置）实现。[git: 279f9e9]
- **修复FEN复制以反映当前视觉棋盘**：
    - 解决了复制棋盘FEN时，生成的FEN字符串未能反映棋盘当前视觉（可能已翻转）状态的问题。
    - 在Rust的`GameManager`中新增了`get_fen_for_current_display()`方法，该方法根据当前的`is_flipped`状态生成相应的FEN字符串（标准“黑上红下”或视觉翻转后的“红上黑下”形式）。
    - Dart端的“复制FEN”功能 (`HomeController.copyFenToClipboard()`) 现调用此新方法，确保复制的FEN与用户所见的棋盘一致。[git: 279f9e9]
- **改进摆棋模式下翻转棋盘的校验准确性**：
    - 由于上述FEN粘贴修复确保了`is_flipped`状态的正确同步，间接改进了摆棋模式下棋子放置校验在棋盘翻转时的准确性。校验逻辑依赖于正确的翻转状态来进行坐标转换。[git: 279f9e9]
- **修复摆谱模式下“交换红黑”后翻转按钮状态同步问题**：
    - 解决了在摆谱模式下，执行“交换红黑”操作后，浮动工具栏中的翻转按钮状态未与棋盘实际翻转状态同步的问题。
    - 修改了`lib/pages/home/<USER>

## [2025-05-17]
- 调整了摆谱模式下“交换红黑”按钮的功能，现在点击后仅交换棋子颜色，棋盘的视觉翻转状态保持不变，从而实现红黑双方棋子在当前视角下的位置互换。
- 清理了`lib/`目录下Dart代码中的不必要的注释，并将部分英文注释翻译为中文，以提高代码可读性。[git: c8a807]
- 修复Windows任务栏右键“关闭窗口”行为：现在会先激活应用窗口，再弹出退出确认对话框。[git: a4d2304]
- 修复了多个Dart文件中由于非`final`实例字段导致的`@immutable` lint警告，提升了代码的健壮性和规范性。[git: 3bff70]
- 修复了Dart代码中因`withOpacity`方法被弃用而产生的警告，采用了新的推荐用法。[git: 7fbf14b]
- 修复了摆谱模式下，当候选棋子放置到无效位置后，候选棋子会消失的问题。现在无效操作后，候选棋子将恢复悬停状态。[git: 0409e9a]
- 在退出摆谱模式时，增加对棋盘上所有棋子位置合规性的全面校验。若校验失败，则提示用户并阻止退出摆谱模式。[git: b78082c]
  - Rust端`GameManager`新增`validate_full_board_layout`方法，用于全面校验棋盘布局。
  - Dart端`HomeController`的`toggleArrangeMode`方法在退出摆谱模式前调用此新校验方法。
  - 为Rust枚举`SidePieceType`派生`Hash` trait以解决`HashMap`键约束问题。
- 修复了在摆谱模式下通过FEN粘贴棋盘后，错误地触发游戏结束判断的问题。修改`HomeController`中的`_handlePlayerOrGameSetChange`方法，在摆谱模式下跳过游戏结束检查。[git: b78082c]
- 优化了摆谱模式下移除将/帅的逻辑：当一方有多个将/帅时，允许移除其中一个（只要不是最后一个）。[git: b78082c]
  - Rust端`GameManager`新增`count_player_kings`方法。
  - Dart端`HomeController`的`handleRightClickRemove`方法调用新方法进行判断。
- 优化了摆谱模式下，通过“拿起另一棋子点击将/帅位置”的方式移除（覆盖）将/帅的逻辑：当被覆盖的将/帅是其己方的最后一个时，禁止非王棋子覆盖。[git: b78082c]
  - 此逻辑已在Rust端`GameManager`的`place_piece_on_board`方法中实现。
  - 为Rust函数`get_unside_piece_by_side_piece`添加了`#[frb(sync)]`标记，并重新生成了frb代码以确保Dart端调用正确。

## [2025-05-16]
- 修复了在摆谱模式下，当棋盘翻转时，对部分棋子（帅/将、仕/士、兵/卒）的己方区域校验不准确的问题。调整了Dart端`HomeController`中UI坐标到Rust固定参考系坐标的转换逻辑（包括行列翻转），并简化了`PlacementValidator`。[git: 95a4735]
- 增强了Rust端`GameManager`中对帅/将（King）和仕/士（Advisor）的位置校验，明确增加了棋子必须在己方半场的判断。[git: 95a4735]
- 优化了浮动工具栏中“翻转棋盘”按钮的激活状态显示方式，当棋盘翻转时，该按钮图标将改变颜色以提供更清晰的视觉反馈。[git: 4bbf193]
- 修复了在引擎对战中，点击“电脑图标”切换引擎状态的同时，若引擎恰好在执行走棋操作，可能因并发导致 `StateError (Bad state: 源位置没有找到棋子)` 的问题。通过在 `HomeController` 的 `_checkComputerMoveIfEngineLoaded` 方法中引入状态锁和额外的状态检查来解决。[git: 6e53a58]
- 修复了浮动工具栏按钮在“摆棋模式”切换时视觉状态更新不及时的Bug。通过将 `FloatBoxPanel` 内部按钮的启用/禁用逻辑直接绑定到 `HomeController` 的 `isArrangeMode` 状态（使用GetX的 `Obx` 和 `Get.find()`）实现。[git: 268d2d6]
- 解决了项目中多处因 `Player` 类型定义不明确导致的编译期命名冲突问题。统一使用从 `rust/chess/piece_utils.dart` 桥接过来的 `Player` 枚举，并在相关文件中通过为 `piece_utils.dart` 导入添加 `as chess_utils` 前缀来明确指定类型。[git: 268d2d6]
- 修复了 `lib/pages/home/<USER>

## [2025-05-15]
- 优化了棋谱视图在回溯或导航时的滚动行为，确保当前激活的棋步在视图中可见且位置更佳。
- 添加了在历史棋局中走新棋步时的确认对话框，防止意外删除后续棋谱。
- 棋谱回溯时，棋盘上会高亮显示当前棋步的起始和目标位置。
- 完善了棋谱导航按钮（后退一步/前进一步）的禁用逻辑，确保在棋谱开头或结尾时按钮正确禁用，避免无效操作。
- 改进了棋谱回溯功能：当通过导航按钮或双击棋谱回溯时，后续棋谱项将变为半透明而不是被删除。仅当在历史局面走出新着法时，原后续棋谱才会被清除。(进一步修复了透明度更新问题，通过在`BoardPieces.goBackToBoardAfterIndex`中调用`moveHistory.refresh()`来确保UI正确响应`activeMoveIndex`的变化)。
- 为棋谱视图添加了导航按钮（到开头、后退一步、前进一步、到结尾）。
- 在棋谱显示中，为中文着法添加了Tooltip，鼠标悬停时显示其ICCS表示。
- 为黑方兵（卒）添加了特殊布局下的单元测试，模仿红方兵的类似测试，并使用了FEN `4k4/9/9/9/9/9/3p1p3/4p4/3p1p3/4K4 b - - 0 1`。

## [2025-05-13]
- 完成摆谱功能。
