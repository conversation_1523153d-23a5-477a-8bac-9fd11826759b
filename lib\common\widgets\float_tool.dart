import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../pages/home/<USER>'; // HomeController
import '../global.dart'; // For testPanelWidth, testBorderRadius

enum PanelShape { rectangle, rounded }

enum DockType { inside, outside }

enum PanelState { expanded, closed }

class FloatBoxController extends GetxController {
  // --- Dependencies ---
  final HomeController homeCtrl = Get.find<HomeController>();

  // --- Configuration (passed from FloatBoxPanel widget) ---
  final List<IconData> buttons;
  final void Function(int)? onPressed;
  final double panelWidth;
  final Color borderColor;
  final double borderWidth;
  final double iconSize;
  final IconData initialPanelIcon;
  final BorderRadius borderRadius;
  final Color backgroundColor;
  final Color panelButtonColor;
  final Color customButtonColor;
  final PanelShape panelShape;
  final double panelOpenOffset;
  final int panelAnimDuration;
  final Curve panelAnimCurve;
  final DockType dockType;
  final double dockOffset;
  // final bool dockActivate; // If this needs to be reactive, make it RxBool
  final int dockAnimDuration;
  final Curve dockAnimCurve;
  final Color innerButtonFocusColor;
  final Color customButtonFocusColor;

  // --- Reactive State ---
  final Rx<PanelState> panelState = PanelState.closed.obs;
  final RxDouble xOffset = 0.0.obs;
  final RxDouble yOffset = 0.0.obs; // Initialized in _initializePosition
  final Rx<IconData> panelIcon;
  final RxList<bool> isFocusColors = <bool>[].obs;

  // --- Screen Size (updated by widget) ---
  final RxDouble _pageWidth = 0.0.obs;
  final RxDouble _pageHeight = 0.0.obs;

  // --- Internal State ---
  double _xOffsetRatio = 0.0; // Initialized in _initializePosition
  double _yOffsetRatio = 1 / 3; // Default initial ratio

  double _mouseOffsetX = 0.0;
  double _mouseOffsetY = 0.0;
  RxInt movementSpeed = 0.obs; // Made Rx for AnimatedPositioned duration

  double? _oldYOffset;
  double? _oldYOffsetRatio;
  bool _isFirstTimePositioning = true;

  FloatBoxController({
    required this.buttons,
    this.onPressed,
    required this.panelWidth,
    required this.borderColor,
    required this.borderWidth,
    required this.iconSize,
    required this.initialPanelIcon,
    required this.borderRadius,
    required this.backgroundColor,
    required this.panelButtonColor,
    required this.customButtonColor,
    required this.panelShape,
    required this.panelOpenOffset,
    required this.panelAnimDuration,
    required this.panelAnimCurve,
    required this.dockType,
    required this.dockOffset,
    // required this.dockActivate,
    required this.dockAnimDuration,
    required this.dockAnimCurve,
    required this.innerButtonFocusColor,
    required this.customButtonFocusColor,
  }) : panelIcon = initialPanelIcon.obs {
    for (var i = 0; i < (buttons.length + 1); i++) {
      isFocusColors.add(false);
    }
  }

  void updateScreenSize(double pWidth, double pHeight) {
    bool screenSizeChanged = (_pageWidth.value != pWidth || _pageHeight.value != pHeight);

    if (pWidth <= 0 || pHeight <= 0) return; // Ignore invalid sizes

    _pageWidth.value = pWidth;
    _pageHeight.value = pHeight;

    if (_isFirstTimePositioning) {
      _initializePosition();
      _isFirstTimePositioning = false;
    } else if (screenSizeChanged) {
      // Handle re-scaling if screen size changes after initial setup
      // Recalculate absolute offsets based on preserved ratios
      xOffset.value = _pageWidth.value * _xOffsetRatio;
      yOffset.value = _pageHeight.value * _yOffsetRatio;

      // Ensure oldYOffset is also scaled if it exists
      if (_oldYOffsetRatio != null) {
        _oldYOffset = _pageHeight.value * _oldYOffsetRatio!;
      }

      _adjustPositionOnPanUpdate(xOffset.value, yOffset.value, isReScale: true);
      _calcOffsetWhenForceDock(); // Re-apply docking logic
    }
  }

  void _initializePosition() {
    if (_pageWidth.value == 0 || _pageHeight.value == 0) return;

    xOffset.value = _pageWidth.value; // Start by assuming far right
    _getPoperDockXOffset(); // Calculate initial docked X
    _xOffsetRatio = xOffset.value / _pageWidth.value;
    // yOffsetRatio is already 1/3

    // Set initial yOffset based on ratio
    yOffset.value = _pageHeight.value * _yOffsetRatio;

    // Apply pan update logic for initial constraints and then force dock
    _adjustPositionOnPanUpdate(xOffset.value, yOffset.value, isReScale: true);
    _calcOffsetWhenForceDock();
  }

  void onInnerButtonTap() {
    movementSpeed.value = panelAnimDuration;
    if (panelState.value == PanelState.expanded) {
      panelState.value = PanelState.closed;
      _calcOffsetWhenForceDock();
      panelIcon.value = initialPanelIcon; // Reset to initial icon (e.g., Icons.add)
    } else {
      panelState.value = PanelState.expanded;
      _calcOffsetWhenExpand();
      panelIcon.value = CupertinoIcons.minus_circle_fill;
    }
  }

  void onPanStartGesture(Offset globalPosition) {
    _mouseOffsetX = globalPosition.dx - xOffset.value;
    _mouseOffsetY = globalPosition.dy - yOffset.value;
  }

  void onPanUpdateGesture(Offset globalPosition) {
    _adjustPositionOnPanUpdate(globalPosition.dx, globalPosition.dy);
    // Update ratios after position change
    if (_pageWidth.value > 0) _xOffsetRatio = xOffset.value / _pageWidth.value;
    if (_pageHeight.value > 0) _yOffsetRatio = yOffset.value / _pageHeight.value;
  }

  void _adjustPositionOnPanUpdate(double globalDx, double globalDy, {bool isReScale = false}) {
    movementSpeed.value = 0;

    double newY = isReScale ? globalDy : globalDy - _mouseOffsetY;
    if (newY < 0 + _dockBoundary()) {
      newY = 0 + _dockBoundary();
    }
    if (newY > (_pageHeight.value - currentPanelHeight) - _dockBoundary()) {
      newY = (_pageHeight.value - currentPanelHeight) - _dockBoundary();
    }
    yOffset.value = newY;

    double newX = isReScale ? globalDx : globalDx - _mouseOffsetX;
    if (newX < 0 + _dockBoundary()) {
      newX = 0 + _dockBoundary();
    }
    if (newX > (_pageWidth.value - panelWidth) - _dockBoundary()) {
      newX = (_pageWidth.value - panelWidth) - _dockBoundary();
    }
    xOffset.value = newX;

    if (!isReScale) {
      _oldYOffset = null;
      _oldYOffsetRatio = null;
    } else if (_oldYOffsetRatio != null && _pageHeight.value > 0) {
      _oldYOffset = _oldYOffsetRatio! * _pageHeight.value;
    }
  }

  void onPanEndGesture() {
    _calcOffsetWhenForceDock();
    // Update ratios after docking
    if (_pageWidth.value > 0) _xOffsetRatio = xOffset.value / _pageWidth.value;
    if (_pageHeight.value > 0) _yOffsetRatio = yOffset.value / _pageHeight.value;
  }

  void setButtonFocus(int index, bool focused) {
    if (index >= 0 && index < isFocusColors.length) {
      isFocusColors[index] = focused;
    }
  }

  double _dockBoundary() {
    if (dockType == DockType.inside) return dockOffset;
    return -dockOffset;
  }

  BorderRadius get currentBorderRadius {
    if (panelShape == PanelShape.rectangle) return borderRadius;
    return BorderRadius.circular(panelWidth);
  }

  double get currentPanelHeight {
    if (panelState.value == PanelState.expanded) {
      return panelWidth * (buttons.length + 1) + borderWidth;
    }
    return panelWidth + (borderWidth * 2);
  }

  void _calcPanelYOffsetWhenOpening() {
    if (yOffset.value < 0) {
      _updateOldYOffset();
      yOffset.value = 0.0 + panelWidth + borderWidth + _dockBoundary();
    } else {
      if (yOffset.value + currentPanelHeight > _pageHeight.value + _dockBoundary()) {
        final newYOffsetValue = _pageHeight.value - currentPanelHeight + _dockBoundary();
        if (newYOffsetValue != yOffset.value) {
          _updateOldYOffset();
          yOffset.value = newYOffsetValue;
        }
      } else {
        _oldYOffset = null; // Already captured by yOffset.value
        _updateOldYOffset(); // Effectively captures current yOffset into _oldYOffset
      }
    }
  }

  void _updateOldYOffset({bool setNull = false}) {
    if (setNull || _pageHeight.value == 0) {
      _oldYOffset = null;
      _oldYOffsetRatio = null;
    } else {
      _oldYOffset = yOffset.value;
      _oldYOffsetRatio = _oldYOffset! / _pageHeight.value;
    }
  }

  double _openDockLeft() {
    if (_pageWidth.value == 0) return xOffset.value; // Avoid division by zero if not initialized
    if (xOffset.value < (_pageWidth.value / 2)) {
      return panelOpenOffset;
    }
    return ((_pageWidth.value - panelWidth)) - (panelOpenOffset);
  }

  Border? get currentPanelBorder {
    if (borderWidth <= 0) return null;
    return Border.all(color: borderColor, width: borderWidth);
  }

  void _calcOffsetWhenForceDock() {
    if (panelState.value == PanelState.closed) {
      movementSpeed.value = dockAnimDuration;
      _getPoperDockXOffset();
      if (_oldYOffset != null && yOffset.value != _oldYOffset!) {
        yOffset.value = _oldYOffset!;
      }
    }
  }

  void _getPoperDockXOffset() {
    if (_pageWidth.value == 0) return;
    double center = xOffset.value + (panelWidth / 2);
    final dockEdgeOffset = (center < _pageWidth.value / 2)
        ? -panelWidth // Corrected: Dock to left edge, making it half off-screen like original
        : (_pageWidth.value - panelWidth); // Dock to right edge
    xOffset.value = dockEdgeOffset - _dockBoundary();
  }

  void _calcOffsetWhenExpand() {
    xOffset.value = _openDockLeft();
    _calcPanelYOffsetWhenOpening();
  }
}

class FloatBoxPanel extends StatelessWidget {
  // Changed to StatelessWidget
  final Key? panelKey; // Optional key for multiple instances
  final Color borderColor;
  final double borderWidthInput; // Renamed to avoid conflict
  final double panelWidthInput; // Renamed
  final double iconSizeInput; // Renamed
  final IconData initialPanelIcon;
  final BorderRadius? borderRadiusInput; // Renamed
  final Color backgroundColor;
  final Color panelButtonColor;
  final Color customButtonColor;
  final PanelShape panelShape;
  final double panelOpenOffsetInput; // Renamed
  final int panelAnimDuration;
  final Curve panelAnimCurve;
  final DockType dockType;
  // dockOffset is calculated from panelWidthInput
  final bool dockActivate;
  final int dockAnimDuration;
  final Curve dockAnimCurve;
  final List<IconData> buttons;
  final void Function(int)? onPressed;
  final Color innerButtonFocusColor;
  final Color customButtonFocusColor;

  // Calculated properties, to be passed to controller
  final double finalPanelWidth;
  final double finalBorderWidth;
  final double finalIconSize;
  final BorderRadius finalBorderRadius;
  final double finalPanelOpenOffset;
  final double finalDockOffset;

  FloatBoxPanel({
    this.panelKey, // Use this key as the tag for Get.put/Get.find
    this.buttons = const [],
    this.borderColor = const Color(0xFF333333),
    this.borderWidthInput = 0,
    this.panelWidthInput = testPanelWidth,
    this.iconSizeInput = 24,
    this.initialPanelIcon = Icons.add,
    this.borderRadiusInput,
    this.backgroundColor = const Color(0xFF333333),
    this.panelButtonColor = Colors.white,
    this.customButtonColor = Colors.white,
    this.panelShape = PanelShape.rounded,
    this.panelOpenOffsetInput = 5.0,
    this.panelAnimDuration = 600,
    this.panelAnimCurve = Curves.fastLinearToSlowEaseIn,
    this.dockType = DockType.outside,
    this.dockAnimDuration = 300,
    this.dockAnimCurve = Curves.fastLinearToSlowEaseIn,
    this.onPressed,
    this.innerButtonFocusColor = Colors.blue,
    this.customButtonFocusColor = Colors.red,
    this.dockActivate = false,
  })  : finalPanelWidth = panelWidthInput * (panelWidthInput / testPanelWidth),
        finalBorderWidth = borderWidthInput * (panelWidthInput / testPanelWidth),
        finalIconSize = iconSizeInput * (panelWidthInput / testPanelWidth),
        finalBorderRadius =
            borderRadiusInput ?? BorderRadius.circular(testBorderRadius * (panelWidthInput / testPanelWidth)),
        finalPanelOpenOffset = panelOpenOffsetInput * (panelWidthInput / testPanelWidth),
        finalDockOffset = (panelWidthInput * (panelWidthInput / testPanelWidth)) / 2,
        super(key: panelKey) {
    // Pass panelKey to super
    // Initialize and register the controller
    // Use a unique tag if multiple instances, panelKey.toString() can be a good tag.
    Get.put(
      FloatBoxController(
        buttons: buttons,
        onPressed: onPressed,
        panelWidth: finalPanelWidth,
        borderColor: borderColor,
        borderWidth: finalBorderWidth,
        iconSize: finalIconSize,
        initialPanelIcon: initialPanelIcon,
        borderRadius: finalBorderRadius,
        backgroundColor: backgroundColor,
        panelButtonColor: panelButtonColor,
        customButtonColor: customButtonColor,
        panelShape: panelShape,
        panelOpenOffset: finalPanelOpenOffset,
        panelAnimDuration: panelAnimDuration,
        panelAnimCurve: panelAnimCurve,
        dockType: dockType,
        dockOffset: finalDockOffset,
        // dockActivate: dockActivate, // Not used in controller directly yet
        dockAnimDuration: dockAnimDuration,
        dockAnimCurve: dockAnimCurve,
        innerButtonFocusColor: innerButtonFocusColor,
        customButtonFocusColor: customButtonFocusColor,
      ),
      tag: panelKey?.toString(), // Use the provided key as tag
    );
  }

  @override
  Widget build(BuildContext context) {
    // Find controller using the same tag
    final FloatBoxController ctrl = Get.find<FloatBoxController>(tag: panelKey?.toString());

    ctrl.updateScreenSize(MediaQuery.of(context).size.width, MediaQuery.of(context).size.height);

    return Obx(() => AnimatedPositioned(
          duration: Duration(milliseconds: ctrl.movementSpeed.value),
          top: ctrl.yOffset.value,
          left: ctrl.xOffset.value,
          curve: ctrl.dockAnimCurve, // Use config from controller constructor
          child: AnimatedContainer(
            duration: Duration(milliseconds: ctrl.panelAnimDuration),
            width: ctrl.panelWidth,
            height: ctrl.currentPanelHeight,
            decoration: BoxDecoration(
              color: ctrl.backgroundColor,
              borderRadius: ctrl.currentBorderRadius,
              border: ctrl.currentPanelBorder,
            ),
            curve: ctrl.panelAnimCurve,
            child: Wrap(
              direction: Axis.horizontal,
              children: [
                GestureDetector(
                  onPanEnd: (_) => ctrl.onPanEndGesture(),
                  onPanStart: (details) => ctrl.onPanStartGesture(details.globalPosition),
                  onPanUpdate: (details) => ctrl.onPanUpdateGesture(details.globalPosition),
                  onTap: () => ctrl.onInnerButtonTap(),
                  child: MouseRegion(
                    onEnter: (_) => ctrl.setButtonFocus(0, true),
                    onExit: (_) => ctrl.setButtonFocus(0, false),
                    cursor: SystemMouseCursors.click,
                    child: Obx(() => _FloatButton(
                          focusColor: ctrl.innerButtonFocusColor,
                          size: ctrl.panelWidth,
                          icon: ctrl.panelIcon.value,
                          color: ctrl.panelButtonColor,
                          hightLight: ctrl.isFocusColors.isNotEmpty ? ctrl.isFocusColors[0] : false,
                          iconSize: ctrl.iconSize,
                        )),
                  ),
                ),
                Obx(() => Visibility(
                      visible: ctrl.panelState.value == PanelState.expanded,
                      child: Column(
                        children: List.generate(
                          ctrl.buttons.length,
                          (index) {
                            bool isEnabled;
                            // Accessing homeCtrl reactively
                            if (ctrl.homeCtrl.isArrangeMode) {
                              //确保使用 .value 访问 RxBool
                              final allowedInArrangeMode = [1, 2, 5, 6];
                              isEnabled = allowedInArrangeMode.contains(index);
                            } else {
                              isEnabled = true;
                            }
                            return GestureDetector(
                              onPanStart: (details) => ctrl.onPanStartGesture(details.globalPosition),
                              onPanUpdate: (details) => ctrl.onPanUpdateGesture(details.globalPosition),
                              onTap: () {
                                if (ctrl.onPressed != null) {
                                  ctrl.onPressed!(index);
                                }
                              },
                              child: MouseRegion(
                                onEnter: (_) => ctrl.setButtonFocus(index + 1, true),
                                onExit: (_) => ctrl.setButtonFocus(index + 1, false),
                                cursor: SystemMouseCursors.click,
                                child: Obx(() => _FloatButton(
                                      key: ValueKey('float_button_${index}_$isEnabled'),
                                      focusColor: ctrl.customButtonFocusColor,
                                      size: ctrl.panelWidth,
                                      icon: ctrl.buttons[index],
                                      color: ctrl.customButtonColor,
                                      hightLight:
                                          ctrl.isFocusColors.length > index + 1 ? ctrl.isFocusColors[index + 1] : false,
                                      iconSize: ctrl.iconSize,
                                      enabled: isEnabled,
                                      isActivated: index == 2 && ctrl.homeCtrl.isBoardFlippedForDisplay.value,
                                    )),
                              ),
                            );
                          },
                        ),
                      ),
                    )),
              ],
            ),
          ),
        ));
  }
}

class _FloatButton extends StatelessWidget {
  final double size;
  final Color color;
  final IconData icon;
  final double iconSize;
  final bool hightLight;
  final Color focusColor;
  final bool enabled;
  final bool isActivated;

  const _FloatButton({
    super.key,
    required this.icon,
    required this.color,
    required this.focusColor,
    this.size = 70,
    this.iconSize = 24,
    this.hightLight = false,
    this.enabled = true,
    this.isActivated = false,
  });

  @override
  Widget build(BuildContext context) {
    Color iconColorToShow = (hightLight || isActivated) ? focusColor : color;

    if (!enabled) {
      iconColorToShow = iconColorToShow.withAlpha((iconColorToShow.alpha * 0.4).round()); // Dim the color
    }

    Widget iconDisplay = Icon(
      icon,
      color: iconColorToShow,
      size: iconSize,
    );

    if (!enabled) {
      return Ink(
        width: size,
        height: size,
        child: Stack(
          alignment: Alignment.center,
          children: [
            iconDisplay,
            Icon(
              CupertinoIcons.xmark,
              color: Colors.yellowAccent.withAlpha((Colors.yellowAccent.alpha * 0.85).round()),
              size: iconSize * 0.9,
            ),
          ],
        ),
      );
    }

    return Ink(
      width: size,
      height: size,
      child: Center(child: iconDisplay),
    );
  }
}
