// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../frb_generated.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';

// These functions are ignored because they are not marked as `pub`: `check_win`, `is_valid_position`
// These types are ignored because they are neither used by any `pub` functions nor (for structs and enums) marked `#[frb(unignore)]`: `Position`
// These function are ignored because they are on traits that is not defined in current crate (put an empty `#[frb]` on it to unignore): `assert_receiver_is_total_eq`, `assert_receiver_is_total_eq`, `assert_receiver_is_total_eq`, `assert_receiver_is_total_eq`, `clone`, `clone`, `clone`, `clone`, `clone`, `eq`, `eq`, `eq`, `eq`, `fmt`, `fmt`, `fmt`, `fmt`, `fmt`

/// Flutter接口函数
GobangGame createGobangGame() =>
    RustLib.instance.api.crateApiGobangCreateGobangGame();

bool gameMakeMove({
  required GobangGame game,
  required int row,
  required int col,
}) => RustLib.instance.api.crateApiGobangGameMakeMove(
  game: game,
  row: row,
  col: col,
);

PieceType gameGetPiece({
  required GobangGame game,
  required int row,
  required int col,
}) => RustLib.instance.api.crateApiGobangGameGetPiece(
  game: game,
  row: row,
  col: col,
);

PieceType gameGetCurrentPlayer({required GobangGame game}) =>
    RustLib.instance.api.crateApiGobangGameGetCurrentPlayer(game: game);

GameState gameGetState({required GobangGame game}) =>
    RustLib.instance.api.crateApiGobangGameGetState(game: game);

void gameReset({required GobangGame game}) =>
    RustLib.instance.api.crateApiGobangGameReset(game: game);

bool gameUndo({required GobangGame game}) =>
    RustLib.instance.api.crateApiGobangGameUndo(game: game);

int gameGetBoardSize({required GobangGame game}) =>
    RustLib.instance.api.crateApiGobangGameGetBoardSize(game: game);

int gameGetMoveCount({required GobangGame game}) =>
    RustLib.instance.api.crateApiGobangGameGetMoveCount(game: game);

// Rust type: RustOpaqueMoi<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<GobangGame>>
abstract class GobangGame implements RustOpaqueInterface {
  static Future<GobangGame> default_() =>
      RustLib.instance.api.crateApiGobangGobangGameDefault();

  /// 获取棋盘大小
  Future<int> getBoardSize();

  /// 获取当前玩家
  Future<PieceType> getCurrentPlayer();

  /// 获取游戏模式
  Future<GameMode> getGameMode();

  /// 获取游戏状态
  Future<GameState> getGameState();

  /// 获取移动历史数量
  Future<int> getMoveCount();

  /// 获取指定位置的棋子
  Future<PieceType> getPiece({required int row, required int col});

  /// 尝试落子
  Future<bool> makeMove({required int row, required int col});

  // HINT: Make it `#[frb(sync)]` to let it become the default constructor of Dart class.
  /// 创建新游戏
  static Future<GobangGame> newInstance({
    required BigInt boardSize,
    required GameMode gameMode,
  }) => RustLib.instance.api.crateApiGobangGobangGameNew(
    boardSize: boardSize,
    gameMode: gameMode,
  );

  /// 重置游戏
  Future<void> reset();

  /// 悔棋
  Future<bool> undoMove();
}

/// 游戏模式
enum GameMode { humanVsHuman, humanVsAi, aiVsAi }

/// 游戏状态
enum GameState { playing, blackWin, whiteWin, draw }

/// 棋子类型
enum PieceType { empty, black, white }
