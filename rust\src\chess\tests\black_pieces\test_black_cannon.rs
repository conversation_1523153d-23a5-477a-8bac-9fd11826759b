#[cfg(test)]
mod tests {
    use crate::chess::{
        <PERSON><PERSON><PERSON><PERSON>, PieceType, Player, SidePieceType, board_to_fen,
        get_array_256_index_from_board_row_col, get_unside_piece_by_side_piece,
    };

    /// 测试黑方八路炮(炮8)在初始状态下的有效移动
    #[test]
    fn test_black_cannon_at_col_8_initial_moves() {
        let gm = GameManager::new();

        // 获取黑方八路炮(炮8)的位置
        let cannon_pos = get_array_256_index_from_board_row_col(3, 8);

        // 1. 获取棋子类型并验证是炮
        let piece = SidePieceType::BlackCannon;
        let unside_piece = get_unside_piece_by_side_piece(piece);
        assert_eq!(unside_piece, PieceType::Cannon);

        // 2. 验证具体移动
        let expected_moves = [
            (
                "h7h6",
                "炮8进1",
                "rnbakabnr/9/1c7/p1p1p1pcp/9/9/P1P1P1P1P/1C5C1/9/RNBAKABNR b - - 0 1",
            ),
            (
                "h7h5",
                "炮8进2",
                "rnbakabnr/9/1c7/p1p1p1p1p/7c1/9/P1P1P1P1P/1C5C1/9/RNBAKABNR b - - 0 1",
            ),
            (
                "h7h4",
                "炮8进3",
                "rnbakabnr/9/1c7/p1p1p1p1p/9/7c1/P1P1P1P1P/1C5C1/9/RNBAKABNR b - - 0 1",
            ),
            (
                "h7h3",
                "炮8进4",
                "rnbakabnr/9/1c7/p1p1p1p1p/9/9/P1P1P1PcP/1C5C1/9/RNBAKABNR b - - 0 1",
            ),
            (
                "h7h0",
                "炮8进7",
                "rnbakabnr/9/1c7/p1p1p1p1p/9/9/P1P1P1P1P/1C5C1/9/RNBAKABcR b - - 0 1",
            ),
            (
                "h7h8",
                "炮8退1",
                "rnbakabnr/7c1/1c7/p1p1p1p1p/9/9/P1P1P1P1P/1C5C1/9/RNBAKABNR b - - 0 1",
            ),
            (
                "h7i7",
                "炮8平9",
                "rnbakabnr/9/1c6c/p1p1p1p1p/9/9/P1P1P1P1P/1C5C1/9/RNBAKABNR b - - 0 1",
            ),
            (
                "h7g7",
                "炮8平7",
                "rnbakabnr/9/1c4c2/p1p1p1p1p/9/9/P1P1P1P1P/1C5C1/9/RNBAKABNR b - - 0 1",
            ),
            (
                "h7f7",
                "炮8平6",
                "rnbakabnr/9/1c3c3/p1p1p1p1p/9/9/P1P1P1P1P/1C5C1/9/RNBAKABNR b - - 0 1",
            ),
            (
                "h7e7",
                "炮8平5",
                "rnbakabnr/9/1c2c4/p1p1p1p1p/9/9/P1P1P1P1P/1C5C1/9/RNBAKABNR b - - 0 1",
            ),
            (
                "h7d7",
                "炮8平4",
                "rnbakabnr/9/1c1c5/p1p1p1p1p/9/9/P1P1P1P1P/1C5C1/9/RNBAKABNR b - - 0 1",
            ),
            (
                "h7c7",
                "炮8平3",
                "rnbakabnr/9/1cc6/p1p1p1p1p/9/9/P1P1P1P1P/1C5C1/9/RNBAKABNR b - - 0 1",
            ),
        ];

        let valid_moves = gm.get_piece_all_valid_moves(cannon_pos);

        // 2.1 验证移动数量
        assert_eq!(valid_moves.len(), expected_moves.len());

        // 2.2 验证iccs是否正确
        for (iccs, _, _) in &expected_moves {
            assert!(valid_moves.contains(&(*iccs).to_string()));
        }

        // 2.3 验证iccs -> 中文记录法
        for (iccs, chinese, _) in &expected_moves {
            assert_eq!(gm.iccs_move_to_chinese_move(iccs), *chinese);
        }

        // 3. 验证移动后的棋盘状态
        for (iccs, _, expected_fen) in &expected_moves {
            // 创建新的GameManager实例进行测试
            let mut test_gm = GameManager::new();

            // 使用 GameManager 的 make_move_by_iccs 方法执行移动
            test_gm.make_move_by_iccs(iccs);

            // 获取移动后的FEN字符串
            // 注意：在实际游戏中，黑方移动后应该轮到红方行棋
            // 但在这个测试中，我们保持与预期FEN字符串一致的玩家参数（Player::Black）
            let actual_fen = board_to_fen(&test_gm.board, Player::Black);

            // 验证FEN字符串
            assert_eq!(actual_fen, *expected_fen);
        }
    }

    /// 测试黑方二路炮(炮2)在初始状态下的有效移动
    #[test]
    fn test_black_cannon_at_col_2_initial_moves() {
        let gm = GameManager::new();

        // 获取黑方二路炮(炮2)的位置
        let cannon_pos = get_array_256_index_from_board_row_col(3, 2);

        // 1. 获取棋子类型并验证是炮
        let piece = SidePieceType::BlackCannon;
        let unside_piece = get_unside_piece_by_side_piece(piece);
        assert_eq!(unside_piece, PieceType::Cannon);

        // 2. 验证具体移动
        let expected_moves = [
            (
                "b7b6",
                "炮2进1",
                "rnbakabnr/9/7c1/pcp1p1p1p/9/9/P1P1P1P1P/1C5C1/9/RNBAKABNR b - - 0 1",
            ),
            (
                "b7b5",
                "炮2进2",
                "rnbakabnr/9/7c1/p1p1p1p1p/1c7/9/P1P1P1P1P/1C5C1/9/RNBAKABNR b - - 0 1",
            ),
            (
                "b7b4",
                "炮2进3",
                "rnbakabnr/9/7c1/p1p1p1p1p/9/1c7/P1P1P1P1P/1C5C1/9/RNBAKABNR b - - 0 1",
            ),
            (
                "b7b3",
                "炮2进4",
                "rnbakabnr/9/7c1/p1p1p1p1p/9/9/PcP1P1P1P/1C5C1/9/RNBAKABNR b - - 0 1",
            ),
            (
                "b7b0",
                "炮2进7",
                "rnbakabnr/9/7c1/p1p1p1p1p/9/9/P1P1P1P1P/1C5C1/9/RcBAKABNR b - - 0 1",
            ),
            (
                "b7b8",
                "炮2退1",
                "rnbakabnr/1c7/7c1/p1p1p1p1p/9/9/P1P1P1P1P/1C5C1/9/RNBAKABNR b - - 0 1",
            ),
            (
                "b7g7",
                "炮2平7",
                "rnbakabnr/9/6cc1/p1p1p1p1p/9/9/P1P1P1P1P/1C5C1/9/RNBAKABNR b - - 0 1",
            ),
            (
                "b7f7",
                "炮2平6",
                "rnbakabnr/9/5c1c1/p1p1p1p1p/9/9/P1P1P1P1P/1C5C1/9/RNBAKABNR b - - 0 1",
            ),
            (
                "b7e7",
                "炮2平5",
                "rnbakabnr/9/4c2c1/p1p1p1p1p/9/9/P1P1P1P1P/1C5C1/9/RNBAKABNR b - - 0 1",
            ),
            (
                "b7d7",
                "炮2平4",
                "rnbakabnr/9/3c3c1/p1p1p1p1p/9/9/P1P1P1P1P/1C5C1/9/RNBAKABNR b - - 0 1",
            ),
            (
                "b7c7",
                "炮2平3",
                "rnbakabnr/9/2c4c1/p1p1p1p1p/9/9/P1P1P1P1P/1C5C1/9/RNBAKABNR b - - 0 1",
            ),
            (
                "b7a7",
                "炮2平1",
                "rnbakabnr/9/c6c1/p1p1p1p1p/9/9/P1P1P1P1P/1C5C1/9/RNBAKABNR b - - 0 1",
            ),
        ];

        let valid_moves = gm.get_piece_all_valid_moves(cannon_pos);

        // 2.1 验证移动数量
        assert_eq!(valid_moves.len(), expected_moves.len());

        // 2.2 验证iccs是否正确
        for (iccs, _, _) in &expected_moves {
            assert!(valid_moves.contains(&(*iccs).to_string()));
        }

        // 2.3 验证iccs -> 中文记录法
        for (iccs, chinese, _) in &expected_moves {
            assert_eq!(gm.iccs_move_to_chinese_move(iccs), *chinese);
        }

        // 3. 验证移动后的棋盘状态
        for (iccs, _, expected_fen) in &expected_moves {
            // 创建新的GameManager实例进行测试
            let mut test_gm = GameManager::new();

            // 使用 GameManager 的 make_move_by_iccs 方法执行移动
            test_gm.make_move_by_iccs(iccs);

            // 获取移动后的FEN字符串
            let actual_fen = board_to_fen(&test_gm.board, Player::Black);

            // 验证FEN字符串
            assert_eq!(actual_fen, *expected_fen);
        }
    }
}
