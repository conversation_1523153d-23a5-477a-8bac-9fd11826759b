#[flutter_rust_bridge::frb(init)]
pub fn init_app() {
    println!("init_app");
    // Default utilities - feel free to customize
    // flutter_rust_bridge::setup_default_user_utils();
    init_logger("./logs/");
    log::info!("日志系统已初始化");
}

/// 初始化日志，将日志输出到文件和标准输出。
/// 所有级别为`debug`或以上的日志将被记录在`./logs/<date>.log`中。
/// 级别为`info`及以上的日志将被输出到标准输出，带有彩色标签。
///
/// # 示例
///
/// ```
/// init_logger("./logs/");
/// ```
fn init_logger(path: &str) {
    // 创建日志目录
    std::fs::create_dir_all(path).expect("创建日志目录失败！");
    // 设置日志颜色
    let colors = fern::colors::ColoredLevelConfig::new()
        .error(fern::colors::Color::Red)
        .warn(fern::colors::Color::Yellow)
        .info(fern::colors::Color::Green)
        .debug(fern::colors::Color::Blue)
        .trace(fern::colors::Color::BrightBlack);
    // 设置日志格式（颜色），为避免中文+颜色时出现乱码，这里写得稍显繁琐
    let mut d = fern::Dispatch::new();
    d = d.format(move |out, message, record| {
        let format = if atty::is(atty::Stream::Stdout) {
            format!(
                "{} [{}] {}",
                chrono::Local::now().format("%Y/%m/%d %H:%M:%S"),
                colors.color(record.level()),
                message
            )
        } else {
            format!(
                "{} [{}] {}",
                chrono::Local::now().format("%Y/%m/%d %H:%M:%S"),
                record.level(),
                message
            )
        };
        out.finish(format_args!("{format}"));
    });
    // 在debug和release模式下都记录日志
    #[cfg(debug_assertions)]
    d.level(log::LevelFilter::Debug)
        .chain(fern::DateBased::new(path, "%Y-%m-%d.log"))
        .chain(std::io::stdout())
        .apply()
        .expect("Debug模式下日志模块配置失败！");
    #[cfg(not(debug_assertions))]
    d.level(log::LevelFilter::Info)
        .chain(fern::DateBased::new(path, "%Y-%m-%d.log"))
        // .chain(std::io::stdout()) // release模式下基本不需要标准输出
        .apply()
        .expect("Release模式下日志模块配置失败！");
    // 设置panic时的钩子函数，用于记录panic信息
    // 更全面
    // let prev = std::panic::take_hook();
    // std::panic::set_hook(Box::new(move |info| {
    //     log::error("{}", info);
    //     prev(info);
    // }));
    // 更简单
    std::panic::set_hook(Box::new(|m| {
        log::error!("{m}");
    }));
}
