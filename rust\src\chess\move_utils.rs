use flutter_rust_bridge::frb;

use super::{COL_NUM_TO_STR_MAP, ROW_NUM_TO_STR_MAP, get_board_row_col_from_array_256_index};

// 转换为ICCS棋谱表示法：https://www.xqbase.com/protocol/cchess_move.htm
pub fn get_iccs_move_str_from_pos(src_index: u8, dst_index: u8) -> String {
    let (src_row, src_col) = get_board_row_col_from_array_256_index(src_index);
    let (dst_row, dst_col) = get_board_row_col_from_array_256_index(dst_index);

    let src_move_str = get_pos_str_from_row_col(src_row, src_col);
    let dst_move_str = get_pos_str_from_row_col(dst_row, dst_col);

    format!("{src_move_str}{dst_move_str}")
}

fn get_pos_str_from_row_col(row: u8, col: u8) -> String {
    format!(
        "{}{}",
        COL_NUM_TO_STR_MAP[&{ col }],
        ROW_NUM_TO_STR_MAP[&{ row }],
    )
}

/// 从ICCS棋谱表示法中获取源位置和目标位置的行列坐标（行从上往下数，列从左往右数，都是从1开始）
///
/// # 参数
///
/// * `iccs_move` - ICCS格式的棋步字符串，例如 "a3a4"
///
/// # 返回值
///
/// 返回一个包含四个u8值的行列值元组 (src_row, src_col, dst_row, dst_col)
///
/// # 示例
///
/// ```
/// let (src_row, src_col, dst_row, dst_col) = get_src_dst_row_col_from_iccs_move("a3a4");
/// assert_eq!((src_row, src_col, dst_row, dst_col), (7, 1, 6, 1));
/// ```
#[frb(sync)]
pub fn get_src_dst_row_col_from_iccs_move(iccs_move: &str) -> (u8, u8, u8, u8) {
    let iccs_move = iccs_move.to_lowercase();
    let src_row: u8 = 10 - (iccs_move.chars().nth(1).unwrap() as u8 - b'0');
    let src_col: u8 = iccs_move.chars().nth(0).unwrap() as u8 - b'a' + 1;
    let dst_row: u8 = 10 - (iccs_move.chars().nth(3).unwrap() as u8 - b'0');
    let dst_col: u8 = iccs_move.chars().nth(2).unwrap() as u8 - b'a' + 1;
    (src_row, src_col, dst_row, dst_col)
}

#[cfg(test)]
mod tests {
    use crate::chess::move_utils::get_src_dst_row_col_from_iccs_move;

    #[test]
    fn test_get_src_dst_row_col_from_iccs_move() {
        assert_eq!(get_src_dst_row_col_from_iccs_move("a3a4"), (7, 1, 6, 1));
        assert_eq!(get_src_dst_row_col_from_iccs_move("i0h2"), (10, 9, 8, 8));
    }
}
