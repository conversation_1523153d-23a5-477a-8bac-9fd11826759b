// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../chess/piece_utils.dart';
import '../frb_generated.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';

Stream<String> subscribeUcciEngine(
        {required Player player, required String enginePath}) =>
    RustLib.instance.api.crateApiUcciApiSubscribeUcciEngine(
        player: player, enginePath: enginePath);

Future<bool> writeToProcess(
        {required String command,
        required int msec,
        required Player player,
        String? checkStrOption}) =>
    RustLib.instance.api.crateApiUcciApiWriteToProcess(
        command: command,
        msec: msec,
        player: player,
        checkStrOption: checkStrOption);

Future<bool> isProcessLoaded({required int msec, required Player player}) =>
    RustLib.instance.api
        .crateApiUcciApiIsProcessLoaded(msec: msec, player: player);

Future<bool> isProcessUnloaded({required int msec, required Player player}) =>
    RustLib.instance.api
        .crateApiUcciApiIsProcessUnloaded(msec: msec, player: player);

Future<String> getEngineName({required Player player}) =>
    RustLib.instance.api.crateApiUcciApiGetEngineName(player: player);
