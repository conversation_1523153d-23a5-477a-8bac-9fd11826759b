import 'dart:ui';

import 'package:flutter/material.dart';

class IOSMenuWidget extends StatelessWidget {
  final List<IOSMenuItem> menuItems;
  final Function(String) onItemSelected;

  const IOSMenuWidget({
    super.key,
    required this.menuItems,
    required this.onItemSelected,
  });

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(13),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.7),
            borderRadius: BorderRadius.circular(13),
            border: Border.all(color: Colors.white.withValues(alpha: 0.2), width: 1),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: List.generate(menuItems.length * 2 - 1, (index) {
              if (index.isOdd) {
                return Divider(
                  height: 1,
                  thickness: 1,
                  color: Colors.black.withValues(alpha: 0.1),
                );
              }
              return _buildMenuItem(menuItems[index ~/ 2]);
            }),
          ),
        ),
      ),
    );
  }

  Widget _buildMenuItem(IOSMenuItem item) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => onItemSelected(item.text),
        highlightColor: Colors.black.withValues(alpha: 0.1),
        splashColor: Colors.black.withValues(alpha: 0.05),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
          child: Row(
            children: [
              Icon(item.icon, color: Colors.black87, size: 18),
              const SizedBox(width: 8), // 调整间距
              Expanded(
                child: Text(
                  item.text,
                  style: const TextStyle(color: Colors.black87, fontSize: 15),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class IOSMenuItem {
  final String text;
  final IconData icon;

  IOSMenuItem({required this.text, required this.icon});
}

Future<String?> showIOSMenu(BuildContext context, Offset position, List<IOSMenuItem> menuItems) async {
  final RenderBox overlay = Overlay.of(context).context.findRenderObject() as RenderBox;
  final RelativeRect positionRect = RelativeRect.fromRect(
    Rect.fromPoints(position, position),
    Offset.zero & overlay.size,
  );

  return await showMenu<String>(
    context: context,
    position: positionRect,
    items: [
      PopupMenuItem<String>(
        padding: EdgeInsets.zero,
        child: IOSMenuWidget(
          menuItems: menuItems,
          onItemSelected: (value) => Navigator.of(context).pop(value),
        ),
      ),
    ],
    elevation: 0,
    shadowColor: Colors.transparent,
    surfaceTintColor: Colors.transparent,
    color: Colors.transparent,
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(13)),
  );
}
