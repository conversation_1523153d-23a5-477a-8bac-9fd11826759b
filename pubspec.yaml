name: meng_ru_ling_shi
description: "梦入零式 - 基于alpha_zero/muzero/efficientZero的中国象棋AI下棋程序，取名为《梦入零式》，意为从零开始在梦境中学习进化的中国象棋AI。"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.4.3 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.6
  rust_lib_meng_ru_ling_shi:
    path: rust_builder
  flutter_rust_bridge: 2.10.0

  freezed_annotation: ^2.0.3
  window_manager: ^0.3.4
  get: ^4.6.6
  intl: ^0.18.1

  docking: ^1.4.1+1
  macos_ui:
    git:
      # url: https://github.com/Mayb3Nots/macos_ui.git
      url: **************:macosui/macos_ui.git
      ref: dev

  flutter_inset_shadow: ^2.0.3
  overlay_support: ^2.0.1
  dashed_rect: ^0.0.4
  flutter_svg: ^2.0.6
  file_picker: ^10.1.9
  pausable_timer: ^3.0.0

  collection: any
dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^3.0.0
  integration_test:
    sdk: flutter

  icons_launcher: ^2.1.7
  freezed: ^2.5.3
  build_runner: ^2.0.0


icons_launcher:
  image_path: "assets/icon/app_icon_128x128.png"
  platforms:
    android:
      enable: true
    windows:
      enable: true


# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  uses-material-design: true
  assets:
    - assets/skins/
    - assets/engine/
    - assets/sounds/
    - assets/icon/
