use flutter_rust_bridge::frb;
use num_derive::FromPrimitive;
use num_traits::FromPrimitive;

use super::{GameManager, get_array_256_index_from_board_row_col, get_iccs_move_str_from_pos};

#[derive(<PERSON>ialEq, Eq, Debug, <PERSON>lone, Copy)]
pub enum Player {
    Red,
    Black,
    Unknown, // TODO: 移除，需要的地方改用Option<Player>
}
impl std::fmt::Display for Player {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.get_name())
    }
}
impl Player {
    #[frb(sync)]
    pub fn get_name(&self) -> String {
        match self {
            Self::Red => "红方".to_string(),
            Self::Black => "黑方".to_string(),
            Self::Unknown => "未知".to_string(),
        }
    }

    #[frb(sync)]
    pub fn opposite(&self) -> Self {
        match self {
            Self::Red => Self::Black,
            Self::Black => Self::Red,
            Self::Unknown => Self::Unknown,
        }
    }
}

// 定义PieceMove结构体
#[derive(Debug, Clone)]
pub struct PieceMove {
    pub chinese: String,
    pub iccs: String,
    pub src_row: u8,
    pub src_col: u8,
    pub dst_row: u8,
    pub dst_col: u8,
    pub player: Player, // 新增：走棋方
}

impl PieceMove {
    #[frb(sync)]
    #[frb(positional)]
    pub fn new(src_row: u8, src_col: u8, dst_row: u8, dst_col: u8, player: Player) -> Self {
        let iccs = get_iccs_move_str_from_pos(
            get_array_256_index_from_board_row_col(src_row, src_col),
            get_array_256_index_from_board_row_col(dst_row, dst_col),
        );
        // 注意：这里创建了一个新的GameManager实例，可能不适用于获取真实的棋谱中文表示
        // 实际游戏中，应该使用已有的GameManager实例
        let chinese = GameManager::new().iccs_move_to_chinese_move(&iccs);
        Self {
            chinese,
            iccs,
            src_row,
            src_col,
            dst_row,
            dst_col,
            player,
        }
    }

    /// 从ICCS坐标创建PieceMove
    #[frb(sync)]
    pub fn from_iccs(iccs: &str, game_manager: &GameManager) -> Self {
        use super::get_src_dst_row_col_from_iccs_move;
        let (src_row, src_col, dst_row, dst_col) = get_src_dst_row_col_from_iccs_move(iccs);
        let chinese = game_manager.iccs_move_to_chinese_move(iccs);
        let player = game_manager.current_player; // 获取当前走棋方
        Self {
            chinese,
            iccs: iccs.to_string(),
            src_row,
            src_col,
            dst_row,
            dst_col,
            player,
        }
    }
}

#[derive(PartialEq, Eq, Debug, FromPrimitive)]
pub enum PieceType {
    King = 0,
    Advisor = 1,
    Bishop = 2,
    Knight = 3,
    Rook = 4,
    Cannon = 5,
    Pawn = 6,
    None,
}

impl PieceType {
    pub const fn get_max_count(&self) -> u8 {
        match self {
            Self::King => 1,
            Self::Advisor => 2,
            Self::Bishop => 2,
            Self::Knight => 2,
            Self::Rook => 2,
            Self::Cannon => 2,
            Self::Pawn => 5,
            Self::None => 0, // 通常不查询None的上限，但为完整性设为0
        }
    }
}

#[derive(PartialEq, Eq, PartialOrd, Debug, Clone, Copy, FromPrimitive, Hash)]
pub enum SidePieceType {
    // 无子
    None = 0,
    // 红方
    RedKing = 8,
    RedAdvisor = 9,
    RedBishop = 10,
    RedKnight = 11,
    RedRook = 12,
    RedCannon = 13,
    RedPawn = 14,
    // 黑方
    BlackKing = 16,
    BlackAdvisor = 17,
    BlackBishop = 18,
    BlackKnight = 19,
    BlackRook = 20,
    BlackCannon = 21,
    BlackPawn = 22,
}
impl std::fmt::Display for SidePieceType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.get_chinese_name())
    }
}
impl SidePieceType {
    #[frb(sync)]
    pub const fn get_chinese_name(&self) -> &str {
        match self {
            Self::None => "(空)",
            Self::RedKing => "帅",
            Self::RedAdvisor => "仕",
            Self::RedBishop => "相",
            Self::RedKnight => "马",
            Self::RedRook => "车",
            Self::RedCannon => "炮",
            Self::RedPawn => "兵",
            Self::BlackKing => "将",
            Self::BlackAdvisor => "士",
            Self::BlackBishop => "象",
            Self::BlackKnight => "马",
            Self::BlackRook => "车",
            Self::BlackCannon => "炮",
            Self::BlackPawn => "卒",
        }
    }

    #[frb(sync)]
    pub fn get_side_piece_type_index(&self) -> u8 {
        *self as u8
    }

    #[frb(sync)]
    #[frb(positional)]
    pub fn get_side_piece_type_by_index(index: u8) -> Self {
        Self::from_u8(index).unwrap()
    }

    #[frb(sync)]
    pub fn get_side(&self) -> Player {
        if is_red_piece(*self) {
            Player::Red
        } else if is_black_piece(*self) {
            Player::Black
        } else {
            Player::Unknown
        }
    }
}

pub fn is_red_piece(side_piece: SidePieceType) -> bool {
    side_piece >= SidePieceType::RedKing && side_piece <= SidePieceType::RedPawn
}

pub fn is_black_piece(side_piece: SidePieceType) -> bool {
    side_piece >= SidePieceType::BlackKing && side_piece <= SidePieceType::BlackPawn
}

#[frb(sync)]
pub fn get_unside_piece_by_side_piece(side_piece: SidePieceType) -> PieceType {
    let unside_piece_pos = match side_piece {
        x if is_red_piece(x) => side_piece as u8 - 8,
        x if is_black_piece(x) => side_piece as u8 - 16,
        _ => return PieceType::None,
    };

    match FromPrimitive::from_u8(unside_piece_pos) {
        Some(piece_type) => piece_type,
        None => PieceType::None,
    }
}
