import 'package:flutter/material.dart';

/// 显示ICCS坐标的悬停提示组件
class IccsPositionTooltip extends StatelessWidget {
  final Offset? position;
  final String? iccsText;
  // 棋盘的尺寸信息，用于计算提示位置
  final Size? boardSize;
  // 棋盘是否翻转（默认为false，即红方在下）
  final bool needFlipForDisplay;

  const IccsPositionTooltip({
    super.key,
    this.position,
    this.iccsText,
    this.boardSize,
    this.needFlipForDisplay = false,
  });

  // 将数字转换为中文数字
  static String _getChineseNumber(int number) {
    const chineseNumbers = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
    if (number >= 0 && number <= 9) {
      return chineseNumbers[number];
    }
    return number.toString(); // 超出范围返回原数字
  }

  @override
  Widget build(BuildContext context) {
    if (position == null || iccsText == null || iccsText!.isEmpty) {
      return const SizedBox.shrink();
    }

    // 提示气泡的尺寸估计
    const tooltipWidth = 100.0; // 增加宽度以适应更多内容
    const tooltipHeight = 60.0; // 增加高度以适应更多内容

    // 计算提示的最佳位置
    double left = position!.dx;
    double top = position!.dy - tooltipHeight - 5; // 默认在鼠标上方

    // 如果有棋盘尺寸信息，则根据鼠标位置调整提示位置
    if (boardSize != null) {
      // 检查是否靠近左边缘
      if (position!.dx < tooltipWidth / 2) {
        left = position!.dx + 10; // 向右偏移
      }
      // 检查是否靠近右边缘
      else if (position!.dx > boardSize!.width - tooltipWidth) {
        left = position!.dx - tooltipWidth; // 向左偏移
      }

      // 检查是否靠近上边缘
      if (position!.dy < tooltipHeight + 20) {
        top = position!.dy + 10; // 在鼠标下方显示
      }
    }

    // 从ICCS坐标解析列号
    String colChar = '';
    int colNum = 0;
    if (iccsText!.isNotEmpty) {
      colChar = iccsText![0]; // 第一个字符是列号，如'a'
      colNum = colChar.codeUnitAt(0) - 'a'.codeUnitAt(0) + 1; // 转换为数字，从1开始
    }

    // 计算红方和黑方的路数
    // 红方：从右到左数，1-9路
    int redCol = 10 - colNum; // 红方视角的列号
    // 黑方：从左到右数，1-9路
    int blackCol = colNum; // 黑方视角的列号

    // 将红方路数转换为中文数字
    String redColChinese = _getChineseNumber(redCol);

    // 构建中文路数信息，根据棋盘是否翻转调整显示顺序
    String chinesePositionInfo;
    if (needFlipForDisplay) {
      // 翻转状态：黑方在下，红方在上
      chinesePositionInfo = '黑方：$blackCol路\n红方：$redColChinese路';
    } else {
      // 正常状态：红方在下，黑方在上
      chinesePositionInfo = '红方：$redColChinese路\n黑方：$blackCol路';
    }

    return Positioned(
      left: left,
      top: top,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.7),
          borderRadius: BorderRadius.circular(4),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.3),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // ICCS坐标
            Text(
              'ICCS: $iccsText',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 11,
              ),
            ),
            const SizedBox(height: 4),
            // 中文路数信息
            Text(
              chinesePositionInfo,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 11,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
