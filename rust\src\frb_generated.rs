// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

#![allow(
    non_camel_case_types,
    unused,
    non_snake_case,
    clippy::needless_return,
    clippy::redundant_closure_call,
    clippy::redundant_closure,
    clippy::useless_conversion,
    clippy::unit_arg,
    clippy::unused_unit,
    clippy::double_parens,
    clippy::let_and_return,
    clippy::too_many_arguments,
    clippy::match_single_binding,
    clippy::clone_on_copy,
    clippy::let_unit_value,
    clippy::deref_addrof,
    clippy::explicit_auto_deref,
    clippy::borrow_deref_ref,
    clippy::needless_borrow
)]

// Section: imports

use crate::chess::game_manager::*;
use flutter_rust_bridge::for_generated::byteorder::{NativeEndian, ReadBytesExt, WriteBytesExt};
use flutter_rust_bridge::for_generated::{Lifetimeable, Lockable, transform_result_dco};
use flutter_rust_bridge::{<PERSON><PERSON>, IntoIntoDart};

// Section: boilerplate

flutter_rust_bridge::frb_generated_boilerplate!(
    default_stream_sink_codec = SseCodec,
    default_rust_opaque = RustOpaqueMoi,
    default_rust_auto_opaque = RustAutoOpaqueMoi,
);
pub(crate) const FLUTTER_RUST_BRIDGE_CODEGEN_VERSION: &str = "2.10.0";
pub(crate) const FLUTTER_RUST_BRIDGE_CODEGEN_CONTENT_HASH: i32 = -1047586606;

// Section: executor

flutter_rust_bridge::frb_generated_default_handler!();

// Section: wire_funcs

fn wire__crate__chess__game_manager__GameManager_are_kings_facing_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "GameManager_are_kings_facing",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<GameManager>,
            >>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let mut api_that_guard = None;
                let decode_indices_ =
                    flutter_rust_bridge::for_generated::lockable_compute_decode_order(vec![
                        flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                            &api_that, 0, false,
                        ),
                    ]);
                for i in decode_indices_ {
                    match i {
                        0 => api_that_guard = Some(api_that.lockable_decode_sync_ref()),
                        _ => unreachable!(),
                    }
                }
                let api_that_guard = api_that_guard.unwrap();
                let output_ok = Result::<_, ()>::Ok(
                    crate::chess::game_manager::GameManager::are_kings_facing(&*api_that_guard),
                )?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__chess__game_manager__GameManager_auto_accessor_get_current_player_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "GameManager_auto_accessor_get_current_player",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<GameManager>,
            >>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let mut api_that_guard = None;
                let decode_indices_ =
                    flutter_rust_bridge::for_generated::lockable_compute_decode_order(vec![
                        flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                            &api_that, 0, false,
                        ),
                    ]);
                for i in decode_indices_ {
                    match i {
                        0 => api_that_guard = Some(api_that.lockable_decode_sync_ref()),
                        _ => unreachable!(),
                    }
                }
                let api_that_guard = api_that_guard.unwrap();
                let output_ok = Result::<_, ()>::Ok(api_that_guard.current_player.clone())?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__chess__game_manager__GameManager_auto_accessor_get_need_flip_for_display_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "GameManager_auto_accessor_get_need_flip_for_display",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<GameManager>,
            >>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let mut api_that_guard = None;
                let decode_indices_ =
                    flutter_rust_bridge::for_generated::lockable_compute_decode_order(vec![
                        flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                            &api_that, 0, false,
                        ),
                    ]);
                for i in decode_indices_ {
                    match i {
                        0 => api_that_guard = Some(api_that.lockable_decode_sync_ref()),
                        _ => unreachable!(),
                    }
                }
                let api_that_guard = api_that_guard.unwrap();
                let output_ok = Result::<_, ()>::Ok(api_that_guard.need_flip_for_display.clone())?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__chess__game_manager__GameManager_auto_accessor_set_current_player_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "GameManager_auto_accessor_set_current_player",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<GameManager>,
            >>::sse_decode(&mut deserializer);
            let api_current_player =
                <crate::chess::piece_utils::Player>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let mut api_that_guard = None;
                let decode_indices_ =
                    flutter_rust_bridge::for_generated::lockable_compute_decode_order(vec![
                        flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                            &api_that, 0, true,
                        ),
                    ]);
                for i in decode_indices_ {
                    match i {
                        0 => api_that_guard = Some(api_that.lockable_decode_sync_ref_mut()),
                        _ => unreachable!(),
                    }
                }
                let mut api_that_guard = api_that_guard.unwrap();
                let output_ok = Result::<_, ()>::Ok({
                    {
                        api_that_guard.current_player = api_current_player;
                    };
                })?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__chess__game_manager__GameManager_auto_accessor_set_need_flip_for_display_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "GameManager_auto_accessor_set_need_flip_for_display",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<GameManager>,
            >>::sse_decode(&mut deserializer);
            let api_need_flip_for_display = <bool>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let mut api_that_guard = None;
                let decode_indices_ =
                    flutter_rust_bridge::for_generated::lockable_compute_decode_order(vec![
                        flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                            &api_that, 0, true,
                        ),
                    ]);
                for i in decode_indices_ {
                    match i {
                        0 => api_that_guard = Some(api_that.lockable_decode_sync_ref_mut()),
                        _ => unreachable!(),
                    }
                }
                let mut api_that_guard = api_that_guard.unwrap();
                let output_ok = Result::<_, ()>::Ok({
                    {
                        api_that_guard.need_flip_for_display = api_need_flip_for_display;
                    };
                })?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__chess__game_manager__GameManager_board_col_count_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "GameManager_board_col_count",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let output_ok = Result::<_, ()>::Ok(
                    crate::chess::game_manager::GameManager::board_col_count(),
                )?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__chess__game_manager__GameManager_board_row_count_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "GameManager_board_row_count",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let output_ok = Result::<_, ()>::Ok(
                    crate::chess::game_manager::GameManager::board_row_count(),
                )?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__chess__game_manager__GameManager_clear_board_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "GameManager_clear_board",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<GameManager>,
            >>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let mut api_that_guard = None;
                let decode_indices_ =
                    flutter_rust_bridge::for_generated::lockable_compute_decode_order(vec![
                        flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                            &api_that, 0, true,
                        ),
                    ]);
                for i in decode_indices_ {
                    match i {
                        0 => api_that_guard = Some(api_that.lockable_decode_sync_ref_mut()),
                        _ => unreachable!(),
                    }
                }
                let mut api_that_guard = api_that_guard.unwrap();
                let output_ok = Result::<_, ()>::Ok({
                    crate::chess::game_manager::GameManager::clear_board(&mut *api_that_guard);
                })?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__chess__game_manager__GameManager_count_player_kings_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "GameManager_count_player_kings",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<GameManager>,
            >>::sse_decode(&mut deserializer);
            let api_player = <crate::chess::piece_utils::Player>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let mut api_that_guard = None;
                let decode_indices_ =
                    flutter_rust_bridge::for_generated::lockable_compute_decode_order(vec![
                        flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                            &api_that, 0, false,
                        ),
                    ]);
                for i in decode_indices_ {
                    match i {
                        0 => api_that_guard = Some(api_that.lockable_decode_sync_ref()),
                        _ => unreachable!(),
                    }
                }
                let api_that_guard = api_that_guard.unwrap();
                let output_ok = Result::<_, ()>::Ok(
                    crate::chess::game_manager::GameManager::count_player_kings(
                        &*api_that_guard,
                        api_player,
                    ),
                )?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__chess__game_manager__GameManager_exchange_pieces_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_normal::<flutter_rust_bridge::for_generated::SseCodec, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "GameManager_exchange_pieces",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<GameManager>,
            >>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| {
                transform_result_sse::<_, ()>((move || {
                    let mut api_that_guard = None;
                    let decode_indices_ =
                        flutter_rust_bridge::for_generated::lockable_compute_decode_order(vec![
                            flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                                &api_that, 0, true,
                            ),
                        ]);
                    for i in decode_indices_ {
                        match i {
                            0 => api_that_guard = Some(api_that.lockable_decode_sync_ref_mut()),
                            _ => unreachable!(),
                        }
                    }
                    let mut api_that_guard = api_that_guard.unwrap();
                    let output_ok = Result::<_, ()>::Ok({
                        crate::chess::game_manager::GameManager::exchange_pieces(
                            &mut *api_that_guard,
                        );
                    })?;
                    Ok(output_ok)
                })())
            }
        },
    )
}
fn wire__crate__chess__game_manager__GameManager_get_display_board_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_normal::<flutter_rust_bridge::for_generated::SseCodec, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "GameManager_get_display_board",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<GameManager>,
            >>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| {
                transform_result_sse::<_, ()>((move || {
                    let mut api_that_guard = None;
                    let decode_indices_ =
                        flutter_rust_bridge::for_generated::lockable_compute_decode_order(vec![
                            flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                                &api_that, 0, false,
                            ),
                        ]);
                    for i in decode_indices_ {
                        match i {
                            0 => api_that_guard = Some(api_that.lockable_decode_sync_ref()),
                            _ => unreachable!(),
                        }
                    }
                    let api_that_guard = api_that_guard.unwrap();
                    let output_ok = Result::<_, ()>::Ok(
                        crate::chess::game_manager::GameManager::get_display_board(
                            &*api_that_guard,
                        ),
                    )?;
                    Ok(output_ok)
                })())
            }
        },
    )
}
fn wire__crate__chess__game_manager__GameManager_get_display_board_fen_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_normal::<flutter_rust_bridge::for_generated::SseCodec, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "GameManager_get_display_board_fen",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<GameManager>,
            >>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| {
                transform_result_sse::<_, ()>((move || {
                    let mut api_that_guard = None;
                    let decode_indices_ =
                        flutter_rust_bridge::for_generated::lockable_compute_decode_order(vec![
                            flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                                &api_that, 0, false,
                            ),
                        ]);
                    for i in decode_indices_ {
                        match i {
                            0 => api_that_guard = Some(api_that.lockable_decode_sync_ref()),
                            _ => unreachable!(),
                        }
                    }
                    let api_that_guard = api_that_guard.unwrap();
                    let output_ok = Result::<_, ()>::Ok(
                        crate::chess::game_manager::GameManager::get_display_board_fen(
                            &*api_that_guard,
                        ),
                    )?;
                    Ok(output_ok)
                })())
            }
        },
    )
}
fn wire__crate__chess__game_manager__GameManager_get_orig_board_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "GameManager_get_orig_board",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let output_ok =
                    Result::<_, ()>::Ok(crate::chess::game_manager::GameManager::get_orig_board())?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__chess__game_manager__GameManager_get_random_legal_move_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_normal::<flutter_rust_bridge::for_generated::SseCodec, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "GameManager_get_random_legal_move",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<GameManager>,
            >>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| {
                transform_result_sse::<_, ()>((move || {
                    let mut api_that_guard = None;
                    let decode_indices_ =
                        flutter_rust_bridge::for_generated::lockable_compute_decode_order(vec![
                            flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                                &api_that, 0, false,
                            ),
                        ]);
                    for i in decode_indices_ {
                        match i {
                            0 => api_that_guard = Some(api_that.lockable_decode_sync_ref()),
                            _ => unreachable!(),
                        }
                    }
                    let api_that_guard = api_that_guard.unwrap();
                    let output_ok = Result::<_, ()>::Ok(
                        crate::chess::game_manager::GameManager::get_random_legal_move(
                            &*api_that_guard,
                        ),
                    )?;
                    Ok(output_ok)
                })())
            }
        },
    )
}
fn wire__crate__chess__game_manager__GameManager_get_side_piece_by_index_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "GameManager_get_side_piece_by_index",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<GameManager>,
            >>::sse_decode(&mut deserializer);
            let api_pos = <u8>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let mut api_that_guard = None;
                let decode_indices_ =
                    flutter_rust_bridge::for_generated::lockable_compute_decode_order(vec![
                        flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                            &api_that, 0, false,
                        ),
                    ]);
                for i in decode_indices_ {
                    match i {
                        0 => api_that_guard = Some(api_that.lockable_decode_sync_ref()),
                        _ => unreachable!(),
                    }
                }
                let api_that_guard = api_that_guard.unwrap();
                let output_ok = Result::<_, ()>::Ok(
                    crate::chess::game_manager::GameManager::get_side_piece_by_index(
                        &*api_that_guard,
                        api_pos,
                    ),
                )?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__chess__game_manager__GameManager_iccs_move_to_chinese_move_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_normal::<flutter_rust_bridge::for_generated::SseCodec, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "GameManager_iccs_move_to_chinese_move",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<GameManager>,
            >>::sse_decode(&mut deserializer);
            let api_iccs_move = <String>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| {
                transform_result_sse::<_, ()>((move || {
                    let mut api_that_guard = None;
                    let decode_indices_ =
                        flutter_rust_bridge::for_generated::lockable_compute_decode_order(vec![
                            flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                                &api_that, 0, false,
                            ),
                        ]);
                    for i in decode_indices_ {
                        match i {
                            0 => api_that_guard = Some(api_that.lockable_decode_sync_ref()),
                            _ => unreachable!(),
                        }
                    }
                    let api_that_guard = api_that_guard.unwrap();
                    let output_ok = Result::<_, ()>::Ok(
                        crate::chess::game_manager::GameManager::iccs_move_to_chinese_move(
                            &*api_that_guard,
                            &api_iccs_move,
                        ),
                    )?;
                    Ok(output_ok)
                })())
            }
        },
    )
}
fn wire__crate__chess__game_manager__GameManager_is_current_player_losing_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_normal::<flutter_rust_bridge::for_generated::SseCodec, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "GameManager_is_current_player_losing",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<GameManager>,
            >>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| {
                transform_result_sse::<_, ()>((move || {
                    let mut api_that_guard = None;
                    let decode_indices_ =
                        flutter_rust_bridge::for_generated::lockable_compute_decode_order(vec![
                            flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                                &api_that, 0, false,
                            ),
                        ]);
                    for i in decode_indices_ {
                        match i {
                            0 => api_that_guard = Some(api_that.lockable_decode_sync_ref()),
                            _ => unreachable!(),
                        }
                    }
                    let api_that_guard = api_that_guard.unwrap();
                    let output_ok = Result::<_, ()>::Ok(
                        crate::chess::game_manager::GameManager::is_current_player_losing(
                            &*api_that_guard,
                        ),
                    )?;
                    Ok(output_ok)
                })())
            }
        },
    )
}
fn wire__crate__chess__game_manager__GameManager_is_current_player_winning_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_normal::<flutter_rust_bridge::for_generated::SseCodec, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "GameManager_is_current_player_winning",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<GameManager>,
            >>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| {
                transform_result_sse::<_, ()>((move || {
                    let mut api_that_guard = None;
                    let decode_indices_ =
                        flutter_rust_bridge::for_generated::lockable_compute_decode_order(vec![
                            flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                                &api_that, 0, false,
                            ),
                        ]);
                    for i in decode_indices_ {
                        match i {
                            0 => api_that_guard = Some(api_that.lockable_decode_sync_ref()),
                            _ => unreachable!(),
                        }
                    }
                    let api_that_guard = api_that_guard.unwrap();
                    let output_ok = Result::<_, ()>::Ok(
                        crate::chess::game_manager::GameManager::is_current_player_winning(
                            &*api_that_guard,
                        ),
                    )?;
                    Ok(output_ok)
                })())
            }
        },
    )
}
fn wire__crate__chess__game_manager__GameManager_is_piece_valid_move_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_normal::<flutter_rust_bridge::for_generated::SseCodec, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "GameManager_is_piece_valid_move",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<GameManager>,
            >>::sse_decode(&mut deserializer);
            let api_visual_src_row = <u8>::sse_decode(&mut deserializer);
            let api_visual_src_col = <u8>::sse_decode(&mut deserializer);
            let api_visual_dst_row = <u8>::sse_decode(&mut deserializer);
            let api_visual_dst_col = <u8>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| {
                transform_result_sse::<_, ()>((move || {
                    let mut api_that_guard = None;
                    let decode_indices_ =
                        flutter_rust_bridge::for_generated::lockable_compute_decode_order(vec![
                            flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                                &api_that, 0, false,
                            ),
                        ]);
                    for i in decode_indices_ {
                        match i {
                            0 => api_that_guard = Some(api_that.lockable_decode_sync_ref()),
                            _ => unreachable!(),
                        }
                    }
                    let api_that_guard = api_that_guard.unwrap();
                    let output_ok = Result::<_, ()>::Ok(
                        crate::chess::game_manager::GameManager::is_piece_valid_move(
                            &*api_that_guard,
                            api_visual_src_row,
                            api_visual_src_col,
                            api_visual_dst_row,
                            api_visual_dst_col,
                        ),
                    )?;
                    Ok(output_ok)
                })())
            }
        },
    )
}
fn wire__crate__chess__game_manager__GameManager_is_valid_piece_placement_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_normal::<flutter_rust_bridge::for_generated::SseCodec, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "GameManager_is_valid_piece_placement",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<GameManager>,
            >>::sse_decode(&mut deserializer);
            let api_visual_row = <u8>::sse_decode(&mut deserializer);
            let api_visual_col = <u8>::sse_decode(&mut deserializer);
            let api_piece_type =
                <crate::chess::piece_utils::SidePieceType>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| {
                transform_result_sse::<_, ()>((move || {
                    let mut api_that_guard = None;
                    let decode_indices_ =
                        flutter_rust_bridge::for_generated::lockable_compute_decode_order(vec![
                            flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                                &api_that, 0, false,
                            ),
                        ]);
                    for i in decode_indices_ {
                        match i {
                            0 => api_that_guard = Some(api_that.lockable_decode_sync_ref()),
                            _ => unreachable!(),
                        }
                    }
                    let api_that_guard = api_that_guard.unwrap();
                    let output_ok = Result::<_, ()>::Ok(
                        crate::chess::game_manager::GameManager::is_valid_piece_placement(
                            &*api_that_guard,
                            api_visual_row,
                            api_visual_col,
                            api_piece_type,
                        ),
                    )?;
                    Ok(output_ok)
                })())
            }
        },
    )
}
fn wire__crate__chess__game_manager__GameManager_is_valid_setup_for_game_start_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "GameManager_is_valid_setup_for_game_start",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<GameManager>,
            >>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let mut api_that_guard = None;
                let decode_indices_ =
                    flutter_rust_bridge::for_generated::lockable_compute_decode_order(vec![
                        flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                            &api_that, 0, false,
                        ),
                    ]);
                for i in decode_indices_ {
                    match i {
                        0 => api_that_guard = Some(api_that.lockable_decode_sync_ref()),
                        _ => unreachable!(),
                    }
                }
                let api_that_guard = api_that_guard.unwrap();
                let output_ok = Result::<_, ()>::Ok(
                    crate::chess::game_manager::GameManager::is_valid_setup_for_game_start(
                        &*api_that_guard,
                    ),
                )?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__chess__game_manager__GameManager_load_fen_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "GameManager_load_fen",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<GameManager>,
            >>::sse_decode(&mut deserializer);
            let api_fen_str = <String>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, String>((move || {
                let mut api_that_guard = None;
                let decode_indices_ =
                    flutter_rust_bridge::for_generated::lockable_compute_decode_order(vec![
                        flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                            &api_that, 0, true,
                        ),
                    ]);
                for i in decode_indices_ {
                    match i {
                        0 => api_that_guard = Some(api_that.lockable_decode_sync_ref_mut()),
                        _ => unreachable!(),
                    }
                }
                let mut api_that_guard = api_that_guard.unwrap();
                let output_ok = crate::chess::game_manager::GameManager::load_fen(
                    &mut *api_that_guard,
                    &api_fen_str,
                )?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__chess__game_manager__GameManager_make_move_by_iccs_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "GameManager_make_move_by_iccs",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<GameManager>,
            >>::sse_decode(&mut deserializer);
            let api_iccs_move = <String>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let mut api_that_guard = None;
                let decode_indices_ =
                    flutter_rust_bridge::for_generated::lockable_compute_decode_order(vec![
                        flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                            &api_that, 0, true,
                        ),
                    ]);
                for i in decode_indices_ {
                    match i {
                        0 => api_that_guard = Some(api_that.lockable_decode_sync_ref_mut()),
                        _ => unreachable!(),
                    }
                }
                let mut api_that_guard = api_that_guard.unwrap();
                let output_ok = Result::<_, ()>::Ok({
                    crate::chess::game_manager::GameManager::make_move_by_iccs(
                        &mut *api_that_guard,
                        &api_iccs_move,
                    );
                })?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__chess__game_manager__GameManager_new_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "GameManager_new",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let output_ok =
                    Result::<_, ()>::Ok(crate::chess::game_manager::GameManager::new())?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__chess__game_manager__GameManager_place_piece_on_board_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "GameManager_place_piece_on_board",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<GameManager>,
            >>::sse_decode(&mut deserializer);
            let api_visual_row = <u8>::sse_decode(&mut deserializer);
            let api_visual_col = <u8>::sse_decode(&mut deserializer);
            let api_piece_to_place =
                <crate::chess::piece_utils::SidePieceType>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let mut api_that_guard = None;
                let decode_indices_ =
                    flutter_rust_bridge::for_generated::lockable_compute_decode_order(vec![
                        flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                            &api_that, 0, true,
                        ),
                    ]);
                for i in decode_indices_ {
                    match i {
                        0 => api_that_guard = Some(api_that.lockable_decode_sync_ref_mut()),
                        _ => unreachable!(),
                    }
                }
                let mut api_that_guard = api_that_guard.unwrap();
                let output_ok = Result::<_, ()>::Ok(
                    crate::chess::game_manager::GameManager::place_piece_on_board(
                        &mut *api_that_guard,
                        api_visual_row,
                        api_visual_col,
                        api_piece_to_place,
                    ),
                )?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__chess__game_manager__GameManager_reset_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "GameManager_reset",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<GameManager>,
            >>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let mut api_that_guard = None;
                let decode_indices_ =
                    flutter_rust_bridge::for_generated::lockable_compute_decode_order(vec![
                        flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                            &api_that, 0, true,
                        ),
                    ]);
                for i in decode_indices_ {
                    match i {
                        0 => api_that_guard = Some(api_that.lockable_decode_sync_ref_mut()),
                        _ => unreachable!(),
                    }
                }
                let mut api_that_guard = api_that_guard.unwrap();
                let output_ok = Result::<_, ()>::Ok({
                    crate::chess::game_manager::GameManager::reset(&mut *api_that_guard);
                })?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__chess__game_manager__GameManager_switch_player_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "GameManager_switch_player",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<GameManager>,
            >>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let mut api_that_guard = None;
                let decode_indices_ =
                    flutter_rust_bridge::for_generated::lockable_compute_decode_order(vec![
                        flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                            &api_that, 0, true,
                        ),
                    ]);
                for i in decode_indices_ {
                    match i {
                        0 => api_that_guard = Some(api_that.lockable_decode_sync_ref_mut()),
                        _ => unreachable!(),
                    }
                }
                let mut api_that_guard = api_that_guard.unwrap();
                let output_ok = Result::<_, ()>::Ok({
                    crate::chess::game_manager::GameManager::switch_player(&mut *api_that_guard);
                })?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__chess__game_manager__GameManager_update_board_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_normal::<flutter_rust_bridge::for_generated::SseCodec, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "GameManager_update_board",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<GameManager>,
            >>::sse_decode(&mut deserializer);
            let api_visual_row = <u8>::sse_decode(&mut deserializer);
            let api_visual_col = <u8>::sse_decode(&mut deserializer);
            let api_piece_index = <u8>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| {
                transform_result_sse::<_, ()>((move || {
                    let mut api_that_guard = None;
                    let decode_indices_ =
                        flutter_rust_bridge::for_generated::lockable_compute_decode_order(vec![
                            flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                                &api_that, 0, true,
                            ),
                        ]);
                    for i in decode_indices_ {
                        match i {
                            0 => api_that_guard = Some(api_that.lockable_decode_sync_ref_mut()),
                            _ => unreachable!(),
                        }
                    }
                    let mut api_that_guard = api_that_guard.unwrap();
                    let output_ok = Result::<_, ()>::Ok({
                        crate::chess::game_manager::GameManager::update_board(
                            &mut *api_that_guard,
                            api_visual_row,
                            api_visual_col,
                            api_piece_index,
                        );
                    })?;
                    Ok(output_ok)
                })())
            }
        },
    )
}
fn wire__crate__chess__game_manager__GameManager_validate_full_board_layout_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "GameManager_validate_full_board_layout",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<GameManager>,
            >>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, String>((move || {
                let mut api_that_guard = None;
                let decode_indices_ =
                    flutter_rust_bridge::for_generated::lockable_compute_decode_order(vec![
                        flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                            &api_that, 0, false,
                        ),
                    ]);
                for i in decode_indices_ {
                    match i {
                        0 => api_that_guard = Some(api_that.lockable_decode_sync_ref()),
                        _ => unreachable!(),
                    }
                }
                let api_that_guard = api_that_guard.unwrap();
                let output_ok =
                    crate::chess::game_manager::GameManager::validate_full_board_layout(
                        &*api_that_guard,
                    )?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__chess__board_utils__board_to_fen_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "board_to_fen",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_board = <Vec<u8>>::sse_decode(&mut deserializer);
            let api_current_player =
                <crate::chess::piece_utils::Player>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let output_ok = Result::<_, ()>::Ok(crate::chess::board_utils::board_to_fen(
                    &api_board,
                    api_current_player,
                ))?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__chess__board_utils__convert_array_256_index_to_array_90_index_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "convert_array_256_index_to_array_90_index",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_index_256 = <u8>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let output_ok = Result::<_, ()>::Ok(
                    crate::chess::board_utils::convert_array_256_index_to_array_90_index(
                        api_index_256,
                    ),
                )?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__chess__board_utils__convert_array_90_index_to_array_256_index_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "convert_array_90_index_to_array_256_index",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_index_90 = <u8>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let output_ok = Result::<_, ()>::Ok(
                    crate::chess::board_utils::convert_array_90_index_to_array_256_index(
                        api_index_90,
                    ),
                )?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__chess__board_utils__convert_board_256_to_board_90_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "convert_board_256_to_board_90",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_board_256 = <[u8; 256]>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let output_ok = Result::<_, ()>::Ok(
                    crate::chess::board_utils::convert_board_256_to_board_90(&api_board_256),
                )?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__chess__board_utils__convert_board_90_to_board_256_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "convert_board_90_to_board_256",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_board_90 = <[u8; 90]>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let output_ok = Result::<_, ()>::Ok(
                    crate::chess::board_utils::convert_board_90_to_board_256(&api_board_90),
                )?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__chess__board_utils__fen_to_board_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "fen_to_board",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_fen = <String>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, String>((move || {
                let output_ok = crate::chess::board_utils::fen_to_board(&api_fen)?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__chess__others__generate_state_picture_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_normal::<flutter_rust_bridge::for_generated::SseCodec, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "generate_state_picture",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_board = <[u8; 90]>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| {
                transform_result_sse::<_, ()>((move || {
                    let output_ok = Result::<_, ()>::Ok(
                        crate::chess::others::generate_state_picture(api_board),
                    )?;
                    Ok(output_ok)
                })())
            }
        },
    )
}
fn wire__crate__chess__board_utils__get_array_256_index_from_board_row_col_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "get_array_256_index_from_board_row_col",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_row = <u8>::sse_decode(&mut deserializer);
            let api_col = <u8>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let output_ok = Result::<_, ()>::Ok(
                    crate::chess::board_utils::get_array_256_index_from_board_row_col(
                        api_row, api_col,
                    ),
                )?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__api__ucci_api__get_engine_name_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_normal::<flutter_rust_bridge::for_generated::SseCodec, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "get_engine_name",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_player = <crate::chess::piece_utils::Player>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| {
                transform_result_sse::<_, ()>((move || {
                    let output_ok =
                        Result::<_, ()>::Ok(crate::api::ucci_api::get_engine_name(api_player))?;
                    Ok(output_ok)
                })())
            }
        },
    )
}
fn wire__crate__chess__move_utils__get_iccs_move_str_from_pos_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_normal::<flutter_rust_bridge::for_generated::SseCodec, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "get_iccs_move_str_from_pos",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_src_index = <u8>::sse_decode(&mut deserializer);
            let api_dst_index = <u8>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| {
                transform_result_sse::<_, ()>((move || {
                    let output_ok =
                        Result::<_, ()>::Ok(crate::chess::move_utils::get_iccs_move_str_from_pos(
                            api_src_index,
                            api_dst_index,
                        ))?;
                    Ok(output_ok)
                })())
            }
        },
    )
}
fn wire__crate__chess__move_utils__get_src_dst_row_col_from_iccs_move_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "get_src_dst_row_col_from_iccs_move",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_iccs_move = <String>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let output_ok = Result::<_, ()>::Ok(
                    crate::chess::move_utils::get_src_dst_row_col_from_iccs_move(&api_iccs_move),
                )?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__chess__piece_utils__get_unside_piece_by_side_piece_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "get_unside_piece_by_side_piece",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_side_piece =
                <crate::chess::piece_utils::SidePieceType>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let output_ok = Result::<_, ()>::Ok(
                    crate::chess::piece_utils::get_unside_piece_by_side_piece(api_side_piece),
                )?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__api__log_api__init_app_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_normal::<flutter_rust_bridge::for_generated::SseCodec, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "init_app",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            deserializer.end();
            move |context| {
                transform_result_sse::<_, ()>((move || {
                    let output_ok = Result::<_, ()>::Ok({
                        crate::api::log_api::init_app();
                    })?;
                    Ok(output_ok)
                })())
            }
        },
    )
}
fn wire__crate__chess__piece_utils__is_black_piece_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_normal::<flutter_rust_bridge::for_generated::SseCodec, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "is_black_piece",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_side_piece =
                <crate::chess::piece_utils::SidePieceType>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| {
                transform_result_sse::<_, ()>((move || {
                    let output_ok = Result::<_, ()>::Ok(
                        crate::chess::piece_utils::is_black_piece(api_side_piece),
                    )?;
                    Ok(output_ok)
                })())
            }
        },
    )
}
fn wire__crate__chess__board_utils__is_pos_in_board_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_normal::<flutter_rust_bridge::for_generated::SseCodec, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "is_pos_in_board",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_pos = <u8>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| {
                transform_result_sse::<_, ()>((move || {
                    let output_ok =
                        Result::<_, ()>::Ok(crate::chess::board_utils::is_pos_in_board(api_pos))?;
                    Ok(output_ok)
                })())
            }
        },
    )
}
fn wire__crate__chess__board_utils__is_pos_in_fort_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_normal::<flutter_rust_bridge::for_generated::SseCodec, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "is_pos_in_fort",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_pos = <u8>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| {
                transform_result_sse::<_, ()>((move || {
                    let output_ok =
                        Result::<_, ()>::Ok(crate::chess::board_utils::is_pos_in_fort(api_pos))?;
                    Ok(output_ok)
                })())
            }
        },
    )
}
fn wire__crate__api__ucci_api__is_process_loaded_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_normal::<flutter_rust_bridge::for_generated::SseCodec, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "is_process_loaded",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_msec = <u32>::sse_decode(&mut deserializer);
            let api_player = <crate::chess::piece_utils::Player>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| {
                transform_result_sse::<_, ()>((move || {
                    let output_ok = Result::<_, ()>::Ok(crate::api::ucci_api::is_process_loaded(
                        api_msec, api_player,
                    ))?;
                    Ok(output_ok)
                })())
            }
        },
    )
}
fn wire__crate__api__ucci_api__is_process_unloaded_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_normal::<flutter_rust_bridge::for_generated::SseCodec, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "is_process_unloaded",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_msec = <u32>::sse_decode(&mut deserializer);
            let api_player = <crate::chess::piece_utils::Player>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| {
                transform_result_sse::<_, ()>((move || {
                    let output_ok = Result::<_, ()>::Ok(
                        crate::api::ucci_api::is_process_unloaded(api_msec, api_player),
                    )?;
                    Ok(output_ok)
                })())
            }
        },
    )
}
fn wire__crate__chess__piece_utils__is_red_piece_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_normal::<flutter_rust_bridge::for_generated::SseCodec, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "is_red_piece",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_side_piece =
                <crate::chess::piece_utils::SidePieceType>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| {
                transform_result_sse::<_, ()>((move || {
                    let output_ok = Result::<_, ()>::Ok(crate::chess::piece_utils::is_red_piece(
                        api_side_piece,
                    ))?;
                    Ok(output_ok)
                })())
            }
        },
    )
}
fn wire__crate__chess__piece_utils__piece_move_from_iccs_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "piece_move_from_iccs",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_iccs = <String>::sse_decode(&mut deserializer);
            let api_game_manager = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<GameManager>,
            >>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let mut api_game_manager_guard = None;
                let decode_indices_ =
                    flutter_rust_bridge::for_generated::lockable_compute_decode_order(vec![
                        flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                            &api_game_manager,
                            0,
                            false,
                        ),
                    ]);
                for i in decode_indices_ {
                    match i {
                        0 => {
                            api_game_manager_guard =
                                Some(api_game_manager.lockable_decode_sync_ref())
                        }
                        _ => unreachable!(),
                    }
                }
                let api_game_manager_guard = api_game_manager_guard.unwrap();
                let output_ok =
                    Result::<_, ()>::Ok(crate::chess::piece_utils::PieceMove::from_iccs(
                        &api_iccs,
                        &*api_game_manager_guard,
                    ))?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__chess__piece_utils__piece_move_new_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "piece_move_new",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_src_row = <u8>::sse_decode(&mut deserializer);
            let api_src_col = <u8>::sse_decode(&mut deserializer);
            let api_dst_row = <u8>::sse_decode(&mut deserializer);
            let api_dst_col = <u8>::sse_decode(&mut deserializer);
            let api_player = <crate::chess::piece_utils::Player>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let output_ok = Result::<_, ()>::Ok(crate::chess::piece_utils::PieceMove::new(
                    api_src_row,
                    api_src_col,
                    api_dst_row,
                    api_dst_col,
                    api_player,
                ))?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__chess__piece_utils__piece_type_get_max_count_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_normal::<flutter_rust_bridge::for_generated::SseCodec, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "piece_type_get_max_count",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <crate::chess::piece_utils::PieceType>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| {
                transform_result_sse::<_, ()>((move || {
                    let output_ok = Result::<_, ()>::Ok(
                        crate::chess::piece_utils::PieceType::get_max_count(&api_that),
                    )?;
                    Ok(output_ok)
                })())
            }
        },
    )
}
fn wire__crate__chess__piece_utils__player_get_name_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "player_get_name",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <crate::chess::piece_utils::Player>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let output_ok =
                    Result::<_, ()>::Ok(crate::chess::piece_utils::Player::get_name(&api_that))?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__chess__piece_utils__player_opposite_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "player_opposite",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <crate::chess::piece_utils::Player>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let output_ok =
                    Result::<_, ()>::Ok(crate::chess::piece_utils::Player::opposite(&api_that))?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__chess__piece_utils__side_piece_type_get_chinese_name_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "side_piece_type_get_chinese_name",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that =
                <crate::chess::piece_utils::SidePieceType>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let output_ok = Result::<_, ()>::Ok({
                    crate::chess::piece_utils::SidePieceType::get_chinese_name(&api_that);
                })?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__chess__piece_utils__side_piece_type_get_side_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "side_piece_type_get_side",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that =
                <crate::chess::piece_utils::SidePieceType>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let output_ok = Result::<_, ()>::Ok(
                    crate::chess::piece_utils::SidePieceType::get_side(&api_that),
                )?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__chess__piece_utils__side_piece_type_get_side_piece_type_by_index_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "side_piece_type_get_side_piece_type_by_index",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_index = <u8>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let output_ok = Result::<_, ()>::Ok(
                    crate::chess::piece_utils::SidePieceType::get_side_piece_type_by_index(
                        api_index,
                    ),
                )?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__chess__piece_utils__side_piece_type_get_side_piece_type_index_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "side_piece_type_get_side_piece_type_index",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that =
                <crate::chess::piece_utils::SidePieceType>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let output_ok = Result::<_, ()>::Ok(
                    crate::chess::piece_utils::SidePieceType::get_side_piece_type_index(&api_that),
                )?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__api__ucci_api__subscribe_ucci_engine_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_normal::<flutter_rust_bridge::for_generated::SseCodec, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "subscribe_ucci_engine",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_player = <crate::chess::piece_utils::Player>::sse_decode(&mut deserializer);
            let api_engine_path = <String>::sse_decode(&mut deserializer);
            let api_listener =
                <StreamSink<String, flutter_rust_bridge::for_generated::SseCodec>>::sse_decode(
                    &mut deserializer,
                );
            deserializer.end();
            move |context| {
                transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>(
                    (move || {
                        let output_ok = crate::api::ucci_api::subscribe_ucci_engine(
                            api_player,
                            api_engine_path,
                            api_listener,
                        )?;
                        Ok(output_ok)
                    })(),
                )
            }
        },
    )
}
fn wire__crate__api__ucci_api__write_to_process_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_normal::<flutter_rust_bridge::for_generated::SseCodec, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "write_to_process",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_command = <String>::sse_decode(&mut deserializer);
            let api_msec = <u32>::sse_decode(&mut deserializer);
            let api_player = <crate::chess::piece_utils::Player>::sse_decode(&mut deserializer);
            let api_check_str_option = <Option<String>>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| {
                transform_result_sse::<_, ()>((move || {
                    let output_ok = Result::<_, ()>::Ok(crate::api::ucci_api::write_to_process(
                        api_command,
                        api_msec,
                        api_player,
                        api_check_str_option,
                    ))?;
                    Ok(output_ok)
                })())
            }
        },
    )
}

// Section: related_funcs

flutter_rust_bridge::frb_generated_moi_arc_impl_value!(
    flutter_rust_bridge::for_generated::RustAutoOpaqueInner<GameManager>
);

// Section: dart2rust

impl SseDecode for flutter_rust_bridge::for_generated::anyhow::Error {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut inner = <String>::sse_decode(deserializer);
        return flutter_rust_bridge::for_generated::anyhow::anyhow!("{}", inner);
    }
}

impl SseDecode for GameManager {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut inner = <RustOpaqueMoi<
            flutter_rust_bridge::for_generated::RustAutoOpaqueInner<GameManager>,
        >>::sse_decode(deserializer);
        return flutter_rust_bridge::for_generated::rust_auto_opaque_decode_owned(inner);
    }
}

impl SseDecode
    for RustOpaqueMoi<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<GameManager>>
{
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut inner = <usize>::sse_decode(deserializer);
        return decode_rust_opaque_moi(inner);
    }
}

impl SseDecode for StreamSink<String, flutter_rust_bridge::for_generated::SseCodec> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut inner = <String>::sse_decode(deserializer);
        return StreamSink::deserialize(inner);
    }
}

impl SseDecode for String {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut inner = <Vec<u8>>::sse_decode(deserializer);
        return String::from_utf8(inner).unwrap();
    }
}

impl SseDecode for bool {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        deserializer.cursor.read_u8().unwrap() != 0
    }
}

impl SseDecode for i32 {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        deserializer.cursor.read_i32::<NativeEndian>().unwrap()
    }
}

impl SseDecode for Vec<u8> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut len_ = <i32>::sse_decode(deserializer);
        let mut ans_ = vec![];
        for idx_ in 0..len_ {
            ans_.push(<u8>::sse_decode(deserializer));
        }
        return ans_;
    }
}

impl SseDecode for Option<String> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        if (<bool>::sse_decode(deserializer)) {
            return Some(<String>::sse_decode(deserializer));
        } else {
            return None;
        }
    }
}

impl SseDecode for Option<crate::chess::piece_utils::PieceMove> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        if (<bool>::sse_decode(deserializer)) {
            return Some(<crate::chess::piece_utils::PieceMove>::sse_decode(
                deserializer,
            ));
        } else {
            return None;
        }
    }
}

impl SseDecode for Option<u8> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        if (<bool>::sse_decode(deserializer)) {
            return Some(<u8>::sse_decode(deserializer));
        } else {
            return None;
        }
    }
}

impl SseDecode for crate::chess::piece_utils::PieceMove {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut var_chinese = <String>::sse_decode(deserializer);
        let mut var_iccs = <String>::sse_decode(deserializer);
        let mut var_srcRow = <u8>::sse_decode(deserializer);
        let mut var_srcCol = <u8>::sse_decode(deserializer);
        let mut var_dstRow = <u8>::sse_decode(deserializer);
        let mut var_dstCol = <u8>::sse_decode(deserializer);
        let mut var_player = <crate::chess::piece_utils::Player>::sse_decode(deserializer);
        return crate::chess::piece_utils::PieceMove {
            chinese: var_chinese,
            iccs: var_iccs,
            src_row: var_srcRow,
            src_col: var_srcCol,
            dst_row: var_dstRow,
            dst_col: var_dstCol,
            player: var_player,
        };
    }
}

impl SseDecode for crate::chess::piece_utils::PieceType {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut inner = <i32>::sse_decode(deserializer);
        return match inner {
            0 => crate::chess::piece_utils::PieceType::King,
            1 => crate::chess::piece_utils::PieceType::Advisor,
            2 => crate::chess::piece_utils::PieceType::Bishop,
            3 => crate::chess::piece_utils::PieceType::Knight,
            4 => crate::chess::piece_utils::PieceType::Rook,
            5 => crate::chess::piece_utils::PieceType::Cannon,
            6 => crate::chess::piece_utils::PieceType::Pawn,
            7 => crate::chess::piece_utils::PieceType::None,
            _ => unreachable!("Invalid variant for PieceType: {}", inner),
        };
    }
}

impl SseDecode for crate::chess::game_manager::PlacementValidity {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut tag_ = <i32>::sse_decode(deserializer);
        match tag_ {
            0 => {
                return crate::chess::game_manager::PlacementValidity::Valid;
            }
            1 => {
                let mut var_field0 = <String>::sse_decode(deserializer);
                return crate::chess::game_manager::PlacementValidity::InvalidLocation(var_field0);
            }
            2 => {
                let mut var_field0 = <String>::sse_decode(deserializer);
                return crate::chess::game_manager::PlacementValidity::MaxPiecesReached(var_field0);
            }
            3 => {
                let mut var_field0 = <String>::sse_decode(deserializer);
                return crate::chess::game_manager::PlacementValidity::KingsFacing(var_field0);
            }
            _ => {
                unimplemented!("");
            }
        }
    }
}

impl SseDecode for crate::chess::piece_utils::Player {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut inner = <i32>::sse_decode(deserializer);
        return match inner {
            0 => crate::chess::piece_utils::Player::Red,
            1 => crate::chess::piece_utils::Player::Black,
            2 => crate::chess::piece_utils::Player::Unknown,
            _ => unreachable!("Invalid variant for Player: {}", inner),
        };
    }
}

impl SseDecode for ([u8; 256], crate::chess::piece_utils::Player) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut var_field0 = <[u8; 256]>::sse_decode(deserializer);
        let mut var_field1 = <crate::chess::piece_utils::Player>::sse_decode(deserializer);
        return (var_field0, var_field1);
    }
}

impl SseDecode for (u8, u8, u8, u8) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut var_field0 = <u8>::sse_decode(deserializer);
        let mut var_field1 = <u8>::sse_decode(deserializer);
        let mut var_field2 = <u8>::sse_decode(deserializer);
        let mut var_field3 = <u8>::sse_decode(deserializer);
        return (var_field0, var_field1, var_field2, var_field3);
    }
}

impl SseDecode for crate::chess::piece_utils::SidePieceType {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut inner = <i32>::sse_decode(deserializer);
        return match inner {
            0 => crate::chess::piece_utils::SidePieceType::None,
            1 => crate::chess::piece_utils::SidePieceType::RedKing,
            2 => crate::chess::piece_utils::SidePieceType::RedAdvisor,
            3 => crate::chess::piece_utils::SidePieceType::RedBishop,
            4 => crate::chess::piece_utils::SidePieceType::RedKnight,
            5 => crate::chess::piece_utils::SidePieceType::RedRook,
            6 => crate::chess::piece_utils::SidePieceType::RedCannon,
            7 => crate::chess::piece_utils::SidePieceType::RedPawn,
            8 => crate::chess::piece_utils::SidePieceType::BlackKing,
            9 => crate::chess::piece_utils::SidePieceType::BlackAdvisor,
            10 => crate::chess::piece_utils::SidePieceType::BlackBishop,
            11 => crate::chess::piece_utils::SidePieceType::BlackKnight,
            12 => crate::chess::piece_utils::SidePieceType::BlackRook,
            13 => crate::chess::piece_utils::SidePieceType::BlackCannon,
            14 => crate::chess::piece_utils::SidePieceType::BlackPawn,
            _ => unreachable!("Invalid variant for SidePieceType: {}", inner),
        };
    }
}

impl SseDecode for u32 {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        deserializer.cursor.read_u32::<NativeEndian>().unwrap()
    }
}

impl SseDecode for u8 {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        deserializer.cursor.read_u8().unwrap()
    }
}

impl SseDecode for [u8; 256] {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut inner = <Vec<u8>>::sse_decode(deserializer);
        return flutter_rust_bridge::for_generated::from_vec_to_array(inner);
    }
}

impl SseDecode for [u8; 90] {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut inner = <Vec<u8>>::sse_decode(deserializer);
        return flutter_rust_bridge::for_generated::from_vec_to_array(inner);
    }
}

impl SseDecode for () {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {}
}

impl SseDecode for usize {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        deserializer.cursor.read_u64::<NativeEndian>().unwrap() as _
    }
}

fn pde_ffi_dispatcher_primary_impl(
    func_id: i32,
    port: flutter_rust_bridge::for_generated::MessagePort,
    ptr: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len: i32,
    data_len: i32,
) {
    // Codec=Pde (Serialization + dispatch), see doc to use other codecs
    match func_id {
        10 => wire__crate__chess__game_manager__GameManager_exchange_pieces_impl(
            port,
            ptr,
            rust_vec_len,
            data_len,
        ),
        11 => wire__crate__chess__game_manager__GameManager_get_display_board_impl(
            port,
            ptr,
            rust_vec_len,
            data_len,
        ),
        12 => wire__crate__chess__game_manager__GameManager_get_display_board_fen_impl(
            port,
            ptr,
            rust_vec_len,
            data_len,
        ),
        14 => wire__crate__chess__game_manager__GameManager_get_random_legal_move_impl(
            port,
            ptr,
            rust_vec_len,
            data_len,
        ),
        16 => wire__crate__chess__game_manager__GameManager_iccs_move_to_chinese_move_impl(
            port,
            ptr,
            rust_vec_len,
            data_len,
        ),
        17 => wire__crate__chess__game_manager__GameManager_is_current_player_losing_impl(
            port,
            ptr,
            rust_vec_len,
            data_len,
        ),
        18 => wire__crate__chess__game_manager__GameManager_is_current_player_winning_impl(
            port,
            ptr,
            rust_vec_len,
            data_len,
        ),
        19 => wire__crate__chess__game_manager__GameManager_is_piece_valid_move_impl(
            port,
            ptr,
            rust_vec_len,
            data_len,
        ),
        20 => wire__crate__chess__game_manager__GameManager_is_valid_piece_placement_impl(
            port,
            ptr,
            rust_vec_len,
            data_len,
        ),
        28 => wire__crate__chess__game_manager__GameManager_update_board_impl(
            port,
            ptr,
            rust_vec_len,
            data_len,
        ),
        36 => wire__crate__chess__others__generate_state_picture_impl(
            port,
            ptr,
            rust_vec_len,
            data_len,
        ),
        38 => wire__crate__api__ucci_api__get_engine_name_impl(port, ptr, rust_vec_len, data_len),
        39 => wire__crate__chess__move_utils__get_iccs_move_str_from_pos_impl(
            port,
            ptr,
            rust_vec_len,
            data_len,
        ),
        42 => wire__crate__api__log_api__init_app_impl(port, ptr, rust_vec_len, data_len),
        43 => {
            wire__crate__chess__piece_utils__is_black_piece_impl(port, ptr, rust_vec_len, data_len)
        }
        44 => {
            wire__crate__chess__board_utils__is_pos_in_board_impl(port, ptr, rust_vec_len, data_len)
        }
        45 => {
            wire__crate__chess__board_utils__is_pos_in_fort_impl(port, ptr, rust_vec_len, data_len)
        }
        46 => wire__crate__api__ucci_api__is_process_loaded_impl(port, ptr, rust_vec_len, data_len),
        47 => {
            wire__crate__api__ucci_api__is_process_unloaded_impl(port, ptr, rust_vec_len, data_len)
        }
        48 => wire__crate__chess__piece_utils__is_red_piece_impl(port, ptr, rust_vec_len, data_len),
        51 => wire__crate__chess__piece_utils__piece_type_get_max_count_impl(
            port,
            ptr,
            rust_vec_len,
            data_len,
        ),
        58 => wire__crate__api__ucci_api__subscribe_ucci_engine_impl(
            port,
            ptr,
            rust_vec_len,
            data_len,
        ),
        59 => wire__crate__api__ucci_api__write_to_process_impl(port, ptr, rust_vec_len, data_len),
        _ => unreachable!(),
    }
}

fn pde_ffi_dispatcher_sync_impl(
    func_id: i32,
    ptr: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len: i32,
    data_len: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    // Codec=Pde (Serialization + dispatch), see doc to use other codecs
    match func_id {
                        1 => wire__crate__chess__game_manager__GameManager_are_kings_facing_impl(ptr, rust_vec_len, data_len),
2 => wire__crate__chess__game_manager__GameManager_auto_accessor_get_current_player_impl(ptr, rust_vec_len, data_len),
3 => wire__crate__chess__game_manager__GameManager_auto_accessor_get_need_flip_for_display_impl(ptr, rust_vec_len, data_len),
4 => wire__crate__chess__game_manager__GameManager_auto_accessor_set_current_player_impl(ptr, rust_vec_len, data_len),
5 => wire__crate__chess__game_manager__GameManager_auto_accessor_set_need_flip_for_display_impl(ptr, rust_vec_len, data_len),
6 => wire__crate__chess__game_manager__GameManager_board_col_count_impl(ptr, rust_vec_len, data_len),
7 => wire__crate__chess__game_manager__GameManager_board_row_count_impl(ptr, rust_vec_len, data_len),
8 => wire__crate__chess__game_manager__GameManager_clear_board_impl(ptr, rust_vec_len, data_len),
9 => wire__crate__chess__game_manager__GameManager_count_player_kings_impl(ptr, rust_vec_len, data_len),
13 => wire__crate__chess__game_manager__GameManager_get_orig_board_impl(ptr, rust_vec_len, data_len),
15 => wire__crate__chess__game_manager__GameManager_get_side_piece_by_index_impl(ptr, rust_vec_len, data_len),
21 => wire__crate__chess__game_manager__GameManager_is_valid_setup_for_game_start_impl(ptr, rust_vec_len, data_len),
22 => wire__crate__chess__game_manager__GameManager_load_fen_impl(ptr, rust_vec_len, data_len),
23 => wire__crate__chess__game_manager__GameManager_make_move_by_iccs_impl(ptr, rust_vec_len, data_len),
24 => wire__crate__chess__game_manager__GameManager_new_impl(ptr, rust_vec_len, data_len),
25 => wire__crate__chess__game_manager__GameManager_place_piece_on_board_impl(ptr, rust_vec_len, data_len),
26 => wire__crate__chess__game_manager__GameManager_reset_impl(ptr, rust_vec_len, data_len),
27 => wire__crate__chess__game_manager__GameManager_switch_player_impl(ptr, rust_vec_len, data_len),
29 => wire__crate__chess__game_manager__GameManager_validate_full_board_layout_impl(ptr, rust_vec_len, data_len),
30 => wire__crate__chess__board_utils__board_to_fen_impl(ptr, rust_vec_len, data_len),
31 => wire__crate__chess__board_utils__convert_array_256_index_to_array_90_index_impl(ptr, rust_vec_len, data_len),
32 => wire__crate__chess__board_utils__convert_array_90_index_to_array_256_index_impl(ptr, rust_vec_len, data_len),
33 => wire__crate__chess__board_utils__convert_board_256_to_board_90_impl(ptr, rust_vec_len, data_len),
34 => wire__crate__chess__board_utils__convert_board_90_to_board_256_impl(ptr, rust_vec_len, data_len),
35 => wire__crate__chess__board_utils__fen_to_board_impl(ptr, rust_vec_len, data_len),
37 => wire__crate__chess__board_utils__get_array_256_index_from_board_row_col_impl(ptr, rust_vec_len, data_len),
40 => wire__crate__chess__move_utils__get_src_dst_row_col_from_iccs_move_impl(ptr, rust_vec_len, data_len),
41 => wire__crate__chess__piece_utils__get_unside_piece_by_side_piece_impl(ptr, rust_vec_len, data_len),
49 => wire__crate__chess__piece_utils__piece_move_from_iccs_impl(ptr, rust_vec_len, data_len),
50 => wire__crate__chess__piece_utils__piece_move_new_impl(ptr, rust_vec_len, data_len),
52 => wire__crate__chess__piece_utils__player_get_name_impl(ptr, rust_vec_len, data_len),
53 => wire__crate__chess__piece_utils__player_opposite_impl(ptr, rust_vec_len, data_len),
54 => wire__crate__chess__piece_utils__side_piece_type_get_chinese_name_impl(ptr, rust_vec_len, data_len),
55 => wire__crate__chess__piece_utils__side_piece_type_get_side_impl(ptr, rust_vec_len, data_len),
56 => wire__crate__chess__piece_utils__side_piece_type_get_side_piece_type_by_index_impl(ptr, rust_vec_len, data_len),
57 => wire__crate__chess__piece_utils__side_piece_type_get_side_piece_type_index_impl(ptr, rust_vec_len, data_len),
                        _ => unreachable!(),
                    }
}

// Section: rust2dart

// Codec=Dco (DartCObject based), see doc to use other codecs
impl flutter_rust_bridge::IntoDart for FrbWrapper<GameManager> {
    fn into_dart(self) -> flutter_rust_bridge::for_generated::DartAbi {
        flutter_rust_bridge::for_generated::rust_auto_opaque_encode::<_, MoiArc<_>>(self.0)
            .into_dart()
    }
}
impl flutter_rust_bridge::for_generated::IntoDartExceptPrimitive for FrbWrapper<GameManager> {}

impl flutter_rust_bridge::IntoIntoDart<FrbWrapper<GameManager>> for GameManager {
    fn into_into_dart(self) -> FrbWrapper<GameManager> {
        self.into()
    }
}

// Codec=Dco (DartCObject based), see doc to use other codecs
impl flutter_rust_bridge::IntoDart for crate::chess::piece_utils::PieceMove {
    fn into_dart(self) -> flutter_rust_bridge::for_generated::DartAbi {
        [
            self.chinese.into_into_dart().into_dart(),
            self.iccs.into_into_dart().into_dart(),
            self.src_row.into_into_dart().into_dart(),
            self.src_col.into_into_dart().into_dart(),
            self.dst_row.into_into_dart().into_dart(),
            self.dst_col.into_into_dart().into_dart(),
            self.player.into_into_dart().into_dart(),
        ]
        .into_dart()
    }
}
impl flutter_rust_bridge::for_generated::IntoDartExceptPrimitive
    for crate::chess::piece_utils::PieceMove
{
}
impl flutter_rust_bridge::IntoIntoDart<crate::chess::piece_utils::PieceMove>
    for crate::chess::piece_utils::PieceMove
{
    fn into_into_dart(self) -> crate::chess::piece_utils::PieceMove {
        self
    }
}
// Codec=Dco (DartCObject based), see doc to use other codecs
impl flutter_rust_bridge::IntoDart for crate::chess::piece_utils::PieceType {
    fn into_dart(self) -> flutter_rust_bridge::for_generated::DartAbi {
        match self {
            Self::King => 0.into_dart(),
            Self::Advisor => 1.into_dart(),
            Self::Bishop => 2.into_dart(),
            Self::Knight => 3.into_dart(),
            Self::Rook => 4.into_dart(),
            Self::Cannon => 5.into_dart(),
            Self::Pawn => 6.into_dart(),
            Self::None => 7.into_dart(),
            _ => unreachable!(),
        }
    }
}
impl flutter_rust_bridge::for_generated::IntoDartExceptPrimitive
    for crate::chess::piece_utils::PieceType
{
}
impl flutter_rust_bridge::IntoIntoDart<crate::chess::piece_utils::PieceType>
    for crate::chess::piece_utils::PieceType
{
    fn into_into_dart(self) -> crate::chess::piece_utils::PieceType {
        self
    }
}
// Codec=Dco (DartCObject based), see doc to use other codecs
impl flutter_rust_bridge::IntoDart for crate::chess::game_manager::PlacementValidity {
    fn into_dart(self) -> flutter_rust_bridge::for_generated::DartAbi {
        match self {
            crate::chess::game_manager::PlacementValidity::Valid => [0.into_dart()].into_dart(),
            crate::chess::game_manager::PlacementValidity::InvalidLocation(field0) => {
                [1.into_dart(), field0.into_into_dart().into_dart()].into_dart()
            }
            crate::chess::game_manager::PlacementValidity::MaxPiecesReached(field0) => {
                [2.into_dart(), field0.into_into_dart().into_dart()].into_dart()
            }
            crate::chess::game_manager::PlacementValidity::KingsFacing(field0) => {
                [3.into_dart(), field0.into_into_dart().into_dart()].into_dart()
            }
            _ => {
                unimplemented!("");
            }
        }
    }
}
impl flutter_rust_bridge::for_generated::IntoDartExceptPrimitive
    for crate::chess::game_manager::PlacementValidity
{
}
impl flutter_rust_bridge::IntoIntoDart<crate::chess::game_manager::PlacementValidity>
    for crate::chess::game_manager::PlacementValidity
{
    fn into_into_dart(self) -> crate::chess::game_manager::PlacementValidity {
        self
    }
}
// Codec=Dco (DartCObject based), see doc to use other codecs
impl flutter_rust_bridge::IntoDart for crate::chess::piece_utils::Player {
    fn into_dart(self) -> flutter_rust_bridge::for_generated::DartAbi {
        match self {
            Self::Red => 0.into_dart(),
            Self::Black => 1.into_dart(),
            Self::Unknown => 2.into_dart(),
            _ => unreachable!(),
        }
    }
}
impl flutter_rust_bridge::for_generated::IntoDartExceptPrimitive
    for crate::chess::piece_utils::Player
{
}
impl flutter_rust_bridge::IntoIntoDart<crate::chess::piece_utils::Player>
    for crate::chess::piece_utils::Player
{
    fn into_into_dart(self) -> crate::chess::piece_utils::Player {
        self
    }
}
// Codec=Dco (DartCObject based), see doc to use other codecs
impl flutter_rust_bridge::IntoDart for crate::chess::piece_utils::SidePieceType {
    fn into_dart(self) -> flutter_rust_bridge::for_generated::DartAbi {
        match self {
            Self::None => 0.into_dart(),
            Self::RedKing => 1.into_dart(),
            Self::RedAdvisor => 2.into_dart(),
            Self::RedBishop => 3.into_dart(),
            Self::RedKnight => 4.into_dart(),
            Self::RedRook => 5.into_dart(),
            Self::RedCannon => 6.into_dart(),
            Self::RedPawn => 7.into_dart(),
            Self::BlackKing => 8.into_dart(),
            Self::BlackAdvisor => 9.into_dart(),
            Self::BlackBishop => 10.into_dart(),
            Self::BlackKnight => 11.into_dart(),
            Self::BlackRook => 12.into_dart(),
            Self::BlackCannon => 13.into_dart(),
            Self::BlackPawn => 14.into_dart(),
            _ => unreachable!(),
        }
    }
}
impl flutter_rust_bridge::for_generated::IntoDartExceptPrimitive
    for crate::chess::piece_utils::SidePieceType
{
}
impl flutter_rust_bridge::IntoIntoDart<crate::chess::piece_utils::SidePieceType>
    for crate::chess::piece_utils::SidePieceType
{
    fn into_into_dart(self) -> crate::chess::piece_utils::SidePieceType {
        self
    }
}

impl SseEncode for flutter_rust_bridge::for_generated::anyhow::Error {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <String>::sse_encode(format!("{:?}", self), serializer);
    }
}

impl SseEncode for GameManager {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <RustOpaqueMoi<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<GameManager>>>::sse_encode(flutter_rust_bridge::for_generated::rust_auto_opaque_encode::<_, MoiArc<_>>(self), serializer);
    }
}

impl SseEncode
    for RustOpaqueMoi<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<GameManager>>
{
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        let (ptr, size) = self.sse_encode_raw();
        <usize>::sse_encode(ptr, serializer);
        <i32>::sse_encode(size, serializer);
    }
}

impl SseEncode for StreamSink<String, flutter_rust_bridge::for_generated::SseCodec> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        unimplemented!("")
    }
}

impl SseEncode for String {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <Vec<u8>>::sse_encode(self.into_bytes(), serializer);
    }
}

impl SseEncode for bool {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        serializer.cursor.write_u8(self as _).unwrap();
    }
}

impl SseEncode for i32 {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        serializer.cursor.write_i32::<NativeEndian>(self).unwrap();
    }
}

impl SseEncode for Vec<u8> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <i32>::sse_encode(self.len() as _, serializer);
        for item in self {
            <u8>::sse_encode(item, serializer);
        }
    }
}

impl SseEncode for Option<String> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <bool>::sse_encode(self.is_some(), serializer);
        if let Some(value) = self {
            <String>::sse_encode(value, serializer);
        }
    }
}

impl SseEncode for Option<crate::chess::piece_utils::PieceMove> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <bool>::sse_encode(self.is_some(), serializer);
        if let Some(value) = self {
            <crate::chess::piece_utils::PieceMove>::sse_encode(value, serializer);
        }
    }
}

impl SseEncode for Option<u8> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <bool>::sse_encode(self.is_some(), serializer);
        if let Some(value) = self {
            <u8>::sse_encode(value, serializer);
        }
    }
}

impl SseEncode for crate::chess::piece_utils::PieceMove {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <String>::sse_encode(self.chinese, serializer);
        <String>::sse_encode(self.iccs, serializer);
        <u8>::sse_encode(self.src_row, serializer);
        <u8>::sse_encode(self.src_col, serializer);
        <u8>::sse_encode(self.dst_row, serializer);
        <u8>::sse_encode(self.dst_col, serializer);
        <crate::chess::piece_utils::Player>::sse_encode(self.player, serializer);
    }
}

impl SseEncode for crate::chess::piece_utils::PieceType {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <i32>::sse_encode(
            match self {
                crate::chess::piece_utils::PieceType::King => 0,
                crate::chess::piece_utils::PieceType::Advisor => 1,
                crate::chess::piece_utils::PieceType::Bishop => 2,
                crate::chess::piece_utils::PieceType::Knight => 3,
                crate::chess::piece_utils::PieceType::Rook => 4,
                crate::chess::piece_utils::PieceType::Cannon => 5,
                crate::chess::piece_utils::PieceType::Pawn => 6,
                crate::chess::piece_utils::PieceType::None => 7,
                _ => {
                    unimplemented!("");
                }
            },
            serializer,
        );
    }
}

impl SseEncode for crate::chess::game_manager::PlacementValidity {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        match self {
            crate::chess::game_manager::PlacementValidity::Valid => {
                <i32>::sse_encode(0, serializer);
            }
            crate::chess::game_manager::PlacementValidity::InvalidLocation(field0) => {
                <i32>::sse_encode(1, serializer);
                <String>::sse_encode(field0, serializer);
            }
            crate::chess::game_manager::PlacementValidity::MaxPiecesReached(field0) => {
                <i32>::sse_encode(2, serializer);
                <String>::sse_encode(field0, serializer);
            }
            crate::chess::game_manager::PlacementValidity::KingsFacing(field0) => {
                <i32>::sse_encode(3, serializer);
                <String>::sse_encode(field0, serializer);
            }
            _ => {
                unimplemented!("");
            }
        }
    }
}

impl SseEncode for crate::chess::piece_utils::Player {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <i32>::sse_encode(
            match self {
                crate::chess::piece_utils::Player::Red => 0,
                crate::chess::piece_utils::Player::Black => 1,
                crate::chess::piece_utils::Player::Unknown => 2,
                _ => {
                    unimplemented!("");
                }
            },
            serializer,
        );
    }
}

impl SseEncode for ([u8; 256], crate::chess::piece_utils::Player) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <[u8; 256]>::sse_encode(self.0, serializer);
        <crate::chess::piece_utils::Player>::sse_encode(self.1, serializer);
    }
}

impl SseEncode for (u8, u8, u8, u8) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <u8>::sse_encode(self.0, serializer);
        <u8>::sse_encode(self.1, serializer);
        <u8>::sse_encode(self.2, serializer);
        <u8>::sse_encode(self.3, serializer);
    }
}

impl SseEncode for crate::chess::piece_utils::SidePieceType {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <i32>::sse_encode(
            match self {
                crate::chess::piece_utils::SidePieceType::None => 0,
                crate::chess::piece_utils::SidePieceType::RedKing => 1,
                crate::chess::piece_utils::SidePieceType::RedAdvisor => 2,
                crate::chess::piece_utils::SidePieceType::RedBishop => 3,
                crate::chess::piece_utils::SidePieceType::RedKnight => 4,
                crate::chess::piece_utils::SidePieceType::RedRook => 5,
                crate::chess::piece_utils::SidePieceType::RedCannon => 6,
                crate::chess::piece_utils::SidePieceType::RedPawn => 7,
                crate::chess::piece_utils::SidePieceType::BlackKing => 8,
                crate::chess::piece_utils::SidePieceType::BlackAdvisor => 9,
                crate::chess::piece_utils::SidePieceType::BlackBishop => 10,
                crate::chess::piece_utils::SidePieceType::BlackKnight => 11,
                crate::chess::piece_utils::SidePieceType::BlackRook => 12,
                crate::chess::piece_utils::SidePieceType::BlackCannon => 13,
                crate::chess::piece_utils::SidePieceType::BlackPawn => 14,
                _ => {
                    unimplemented!("");
                }
            },
            serializer,
        );
    }
}

impl SseEncode for u32 {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        serializer.cursor.write_u32::<NativeEndian>(self).unwrap();
    }
}

impl SseEncode for u8 {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        serializer.cursor.write_u8(self).unwrap();
    }
}

impl SseEncode for [u8; 256] {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <Vec<u8>>::sse_encode(
            {
                let boxed: Box<[_]> = Box::new(self);
                boxed.into_vec()
            },
            serializer,
        );
    }
}

impl SseEncode for [u8; 90] {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <Vec<u8>>::sse_encode(
            {
                let boxed: Box<[_]> = Box::new(self);
                boxed.into_vec()
            },
            serializer,
        );
    }
}

impl SseEncode for () {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {}
}

impl SseEncode for usize {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        serializer
            .cursor
            .write_u64::<NativeEndian>(self as _)
            .unwrap();
    }
}

#[cfg(not(target_family = "wasm"))]
mod io {
    // This file is automatically generated, so please do not edit it.
    // @generated by `flutter_rust_bridge`@ 2.10.0.

    // Section: imports

    use super::*;
    use crate::chess::game_manager::*;
    use flutter_rust_bridge::for_generated::byteorder::{
        NativeEndian, ReadBytesExt, WriteBytesExt,
    };
    use flutter_rust_bridge::for_generated::{Lifetimeable, Lockable, transform_result_dco};
    use flutter_rust_bridge::{Handler, IntoIntoDart};

    // Section: boilerplate

    flutter_rust_bridge::frb_generated_boilerplate_io!();

    #[unsafe(no_mangle)]
    pub extern "C" fn frbgen_meng_ru_ling_shi_rust_arc_increment_strong_count_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
        ptr: *const std::ffi::c_void,
    ) {
        MoiArc::<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<GameManager>>::increment_strong_count(ptr as _);
    }

    #[unsafe(no_mangle)]
    pub extern "C" fn frbgen_meng_ru_ling_shi_rust_arc_decrement_strong_count_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
        ptr: *const std::ffi::c_void,
    ) {
        MoiArc::<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<GameManager>>::decrement_strong_count(ptr as _);
    }
}
#[cfg(not(target_family = "wasm"))]
pub use io::*;

/// cbindgen:ignore
#[cfg(target_family = "wasm")]
mod web {
    // This file is automatically generated, so please do not edit it.
    // @generated by `flutter_rust_bridge`@ 2.10.0.

    // Section: imports

    use super::*;
    use crate::chess::game_manager::*;
    use flutter_rust_bridge::for_generated::byteorder::{
        NativeEndian, ReadBytesExt, WriteBytesExt,
    };
    use flutter_rust_bridge::for_generated::wasm_bindgen;
    use flutter_rust_bridge::for_generated::wasm_bindgen::prelude::*;
    use flutter_rust_bridge::for_generated::{Lifetimeable, Lockable, transform_result_dco};
    use flutter_rust_bridge::{Handler, IntoIntoDart};

    // Section: boilerplate

    flutter_rust_bridge::frb_generated_boilerplate_web!();

    #[wasm_bindgen]
    pub fn rust_arc_increment_strong_count_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
        ptr: *const std::ffi::c_void,
    ) {
        MoiArc::<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<GameManager>>::increment_strong_count(ptr as _);
    }

    #[wasm_bindgen]
    pub fn rust_arc_decrement_strong_count_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
        ptr: *const std::ffi::c_void,
    ) {
        MoiArc::<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<GameManager>>::decrement_strong_count(ptr as _);
    }
}
#[cfg(target_family = "wasm")]
pub use web::*;
