// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

// ignore_for_file: unused_import, unused_element, unnecessary_import, duplicate_ignore, invalid_use_of_internal_member, annotate_overrides, non_constant_identifier_names, curly_braces_in_flow_control_structures, prefer_const_literals_to_create_immutables, unused_field

import 'api/ucci_api.dart';
import 'chess/board_utils.dart';
import 'chess/game_manager.dart';
import 'chess/move_utils.dart';
import 'chess/others.dart';
import 'chess/piece_utils.dart';
import 'dart:async';
import 'dart:convert';
import 'frb_generated.dart';
import 'frb_generated.io.dart'
    if (dart.library.js_interop) 'frb_generated.web.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';

/// Main entrypoint of the Rust API
class RustLib extends BaseEntrypoint<RustLibApi, RustLibApiImpl, RustLibWire> {
  @internal
  static final instance = RustLib._();

  RustLib._();

  /// Initialize flutter_rust_bridge
  static Future<void> init({
    RustLibApi? api,
    BaseHandler? handler,
    ExternalLibrary? externalLibrary,
  }) async {
    await instance.initImpl(
      api: api,
      handler: handler,
      externalLibrary: externalLibrary,
    );
  }

  /// Initialize flutter_rust_bridge in mock mode.
  /// No libraries for FFI are loaded.
  static void initMock({
    required RustLibApi api,
  }) {
    instance.initMockImpl(
      api: api,
    );
  }

  /// Dispose flutter_rust_bridge
  ///
  /// The call to this function is optional, since flutter_rust_bridge (and everything else)
  /// is automatically disposed when the app stops.
  static void dispose() => instance.disposeImpl();

  @override
  ApiImplConstructor<RustLibApiImpl, RustLibWire> get apiImplConstructor =>
      RustLibApiImpl.new;

  @override
  WireConstructor<RustLibWire> get wireConstructor =>
      RustLibWire.fromExternalLibrary;

  @override
  Future<void> executeRustInitializers() async {
    await api.crateApiLogApiInitApp();
  }

  @override
  ExternalLibraryLoaderConfig get defaultExternalLibraryLoaderConfig =>
      kDefaultExternalLibraryLoaderConfig;

  @override
  String get codegenVersion => '2.10.0';

  @override
  int get rustContentHash => -1047586606;

  static const kDefaultExternalLibraryLoaderConfig =
      ExternalLibraryLoaderConfig(
    stem: 'rust_lib_meng_ru_ling_shi',
    ioDirectory: 'rust/target/release/',
    webPrefix: 'pkg/',
  );
}

abstract class RustLibApi extends BaseApi {
  bool crateChessGameManagerGameManagerAreKingsFacing(
      {required GameManager that});

  Player crateChessGameManagerGameManagerAutoAccessorGetCurrentPlayer(
      {required GameManager that});

  bool crateChessGameManagerGameManagerAutoAccessorGetNeedFlipForDisplay(
      {required GameManager that});

  void crateChessGameManagerGameManagerAutoAccessorSetCurrentPlayer(
      {required GameManager that, required Player currentPlayer});

  void crateChessGameManagerGameManagerAutoAccessorSetNeedFlipForDisplay(
      {required GameManager that, required bool needFlipForDisplay});

  int crateChessGameManagerGameManagerBoardColCount();

  int crateChessGameManagerGameManagerBoardRowCount();

  void crateChessGameManagerGameManagerClearBoard({required GameManager that});

  int crateChessGameManagerGameManagerCountPlayerKings(
      {required GameManager that, required Player player});

  Future<void> crateChessGameManagerGameManagerExchangePieces(
      {required GameManager that});

  Future<U8Array256> crateChessGameManagerGameManagerGetDisplayBoard(
      {required GameManager that});

  Future<String> crateChessGameManagerGameManagerGetDisplayBoardFen(
      {required GameManager that});

  U8Array256 crateChessGameManagerGameManagerGetOrigBoard();

  Future<PieceMove?> crateChessGameManagerGameManagerGetRandomLegalMove(
      {required GameManager that});

  SidePieceType crateChessGameManagerGameManagerGetSidePieceByIndex(
      {required GameManager that, required int pos});

  Future<String> crateChessGameManagerGameManagerIccsMoveToChineseMove(
      {required GameManager that, required String iccsMove});

  Future<bool> crateChessGameManagerGameManagerIsCurrentPlayerLosing(
      {required GameManager that});

  Future<bool> crateChessGameManagerGameManagerIsCurrentPlayerWinning(
      {required GameManager that});

  Future<bool> crateChessGameManagerGameManagerIsPieceValidMove(
      {required GameManager that,
      required int visualSrcRow,
      required int visualSrcCol,
      required int visualDstRow,
      required int visualDstCol});

  Future<bool> crateChessGameManagerGameManagerIsValidPiecePlacement(
      {required GameManager that,
      required int visualRow,
      required int visualCol,
      required SidePieceType pieceType});

  bool crateChessGameManagerGameManagerIsValidSetupForGameStart(
      {required GameManager that});

  void crateChessGameManagerGameManagerLoadFen(
      {required GameManager that, required String fenStr});

  void crateChessGameManagerGameManagerMakeMoveByIccs(
      {required GameManager that, required String iccsMove});

  GameManager crateChessGameManagerGameManagerNew();

  PlacementValidity crateChessGameManagerGameManagerPlacePieceOnBoard(
      {required GameManager that,
      required int visualRow,
      required int visualCol,
      required SidePieceType pieceToPlace});

  void crateChessGameManagerGameManagerReset({required GameManager that});

  void crateChessGameManagerGameManagerSwitchPlayer(
      {required GameManager that});

  Future<void> crateChessGameManagerGameManagerUpdateBoard(
      {required GameManager that,
      required int visualRow,
      required int visualCol,
      required int pieceIndex});

  void crateChessGameManagerGameManagerValidateFullBoardLayout(
      {required GameManager that});

  String crateChessBoardUtilsBoardToFen(
      {required List<int> board, required Player currentPlayer});

  int? crateChessBoardUtilsConvertArray256IndexToArray90Index(
      {required int index256});

  int crateChessBoardUtilsConvertArray90IndexToArray256Index(
      {required int index90});

  U8Array90 crateChessBoardUtilsConvertBoard256ToBoard90(
      {required U8Array256 board256});

  U8Array256 crateChessBoardUtilsConvertBoard90ToBoard256(
      {required U8Array90 board90});

  (U8Array256, Player) crateChessBoardUtilsFenToBoard({required String fen});

  Future<String> crateChessOthersGenerateStatePicture(
      {required U8Array90 board});

  int crateChessBoardUtilsGetArray256IndexFromBoardRowCol(
      {required int row, required int col});

  Future<String> crateApiUcciApiGetEngineName({required Player player});

  Future<String> crateChessMoveUtilsGetIccsMoveStrFromPos(
      {required int srcIndex, required int dstIndex});

  (int, int, int, int) crateChessMoveUtilsGetSrcDstRowColFromIccsMove(
      {required String iccsMove});

  PieceType crateChessPieceUtilsGetUnsidePieceBySidePiece(
      {required SidePieceType sidePiece});

  Future<void> crateApiLogApiInitApp();

  Future<bool> crateChessPieceUtilsIsBlackPiece(
      {required SidePieceType sidePiece});

  Future<bool> crateChessBoardUtilsIsPosInBoard({required int pos});

  Future<bool> crateChessBoardUtilsIsPosInFort({required int pos});

  Future<bool> crateApiUcciApiIsProcessLoaded(
      {required int msec, required Player player});

  Future<bool> crateApiUcciApiIsProcessUnloaded(
      {required int msec, required Player player});

  Future<bool> crateChessPieceUtilsIsRedPiece(
      {required SidePieceType sidePiece});

  PieceMove crateChessPieceUtilsPieceMoveFromIccs(
      {required String iccs, required GameManager gameManager});

  PieceMove crateChessPieceUtilsPieceMoveNew(
      {required int srcRow,
      required int srcCol,
      required int dstRow,
      required int dstCol,
      required Player player});

  Future<int> crateChessPieceUtilsPieceTypeGetMaxCount(
      {required PieceType that});

  String crateChessPieceUtilsPlayerGetName({required Player that});

  Player crateChessPieceUtilsPlayerOpposite({required Player that});

  void crateChessPieceUtilsSidePieceTypeGetChineseName(
      {required SidePieceType that});

  Player crateChessPieceUtilsSidePieceTypeGetSide(
      {required SidePieceType that});

  SidePieceType crateChessPieceUtilsSidePieceTypeGetSidePieceTypeByIndex(
      {required int index});

  int crateChessPieceUtilsSidePieceTypeGetSidePieceTypeIndex(
      {required SidePieceType that});

  Stream<String> crateApiUcciApiSubscribeUcciEngine(
      {required Player player, required String enginePath});

  Future<bool> crateApiUcciApiWriteToProcess(
      {required String command,
      required int msec,
      required Player player,
      String? checkStrOption});

  RustArcIncrementStrongCountFnType
      get rust_arc_increment_strong_count_GameManager;

  RustArcDecrementStrongCountFnType
      get rust_arc_decrement_strong_count_GameManager;

  CrossPlatformFinalizerArg get rust_arc_decrement_strong_count_GameManagerPtr;
}

class RustLibApiImpl extends RustLibApiImplPlatform implements RustLibApi {
  RustLibApiImpl({
    required super.handler,
    required super.wire,
    required super.generalizedFrbRustBinding,
    required super.portManager,
  });

  @override
  bool crateChessGameManagerGameManagerAreKingsFacing(
      {required GameManager that}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
            that, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 1)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_bool,
        decodeErrorData: null,
      ),
      constMeta: kCrateChessGameManagerGameManagerAreKingsFacingConstMeta,
      argValues: [that],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateChessGameManagerGameManagerAreKingsFacingConstMeta =>
      const TaskConstMeta(
        debugName: "GameManager_are_kings_facing",
        argNames: ["that"],
      );

  @override
  Player crateChessGameManagerGameManagerAutoAccessorGetCurrentPlayer(
      {required GameManager that}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
            that, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 2)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_player,
        decodeErrorData: null,
      ),
      constMeta:
          kCrateChessGameManagerGameManagerAutoAccessorGetCurrentPlayerConstMeta,
      argValues: [that],
      apiImpl: this,
    ));
  }

  TaskConstMeta
      get kCrateChessGameManagerGameManagerAutoAccessorGetCurrentPlayerConstMeta =>
          const TaskConstMeta(
            debugName: "GameManager_auto_accessor_get_current_player",
            argNames: ["that"],
          );

  @override
  bool crateChessGameManagerGameManagerAutoAccessorGetNeedFlipForDisplay(
      {required GameManager that}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
            that, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 3)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_bool,
        decodeErrorData: null,
      ),
      constMeta:
          kCrateChessGameManagerGameManagerAutoAccessorGetNeedFlipForDisplayConstMeta,
      argValues: [that],
      apiImpl: this,
    ));
  }

  TaskConstMeta
      get kCrateChessGameManagerGameManagerAutoAccessorGetNeedFlipForDisplayConstMeta =>
          const TaskConstMeta(
            debugName: "GameManager_auto_accessor_get_need_flip_for_display",
            argNames: ["that"],
          );

  @override
  void crateChessGameManagerGameManagerAutoAccessorSetCurrentPlayer(
      {required GameManager that, required Player currentPlayer}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_RefMut_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
            that, serializer);
        sse_encode_player(currentPlayer, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 4)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_unit,
        decodeErrorData: null,
      ),
      constMeta:
          kCrateChessGameManagerGameManagerAutoAccessorSetCurrentPlayerConstMeta,
      argValues: [that, currentPlayer],
      apiImpl: this,
    ));
  }

  TaskConstMeta
      get kCrateChessGameManagerGameManagerAutoAccessorSetCurrentPlayerConstMeta =>
          const TaskConstMeta(
            debugName: "GameManager_auto_accessor_set_current_player",
            argNames: ["that", "currentPlayer"],
          );

  @override
  void crateChessGameManagerGameManagerAutoAccessorSetNeedFlipForDisplay(
      {required GameManager that, required bool needFlipForDisplay}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_RefMut_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
            that, serializer);
        sse_encode_bool(needFlipForDisplay, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 5)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_unit,
        decodeErrorData: null,
      ),
      constMeta:
          kCrateChessGameManagerGameManagerAutoAccessorSetNeedFlipForDisplayConstMeta,
      argValues: [that, needFlipForDisplay],
      apiImpl: this,
    ));
  }

  TaskConstMeta
      get kCrateChessGameManagerGameManagerAutoAccessorSetNeedFlipForDisplayConstMeta =>
          const TaskConstMeta(
            debugName: "GameManager_auto_accessor_set_need_flip_for_display",
            argNames: ["that", "needFlipForDisplay"],
          );

  @override
  int crateChessGameManagerGameManagerBoardColCount() {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 6)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_u_8,
        decodeErrorData: null,
      ),
      constMeta: kCrateChessGameManagerGameManagerBoardColCountConstMeta,
      argValues: [],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateChessGameManagerGameManagerBoardColCountConstMeta =>
      const TaskConstMeta(
        debugName: "GameManager_board_col_count",
        argNames: [],
      );

  @override
  int crateChessGameManagerGameManagerBoardRowCount() {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 7)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_u_8,
        decodeErrorData: null,
      ),
      constMeta: kCrateChessGameManagerGameManagerBoardRowCountConstMeta,
      argValues: [],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateChessGameManagerGameManagerBoardRowCountConstMeta =>
      const TaskConstMeta(
        debugName: "GameManager_board_row_count",
        argNames: [],
      );

  @override
  void crateChessGameManagerGameManagerClearBoard({required GameManager that}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_RefMut_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
            that, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 8)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_unit,
        decodeErrorData: null,
      ),
      constMeta: kCrateChessGameManagerGameManagerClearBoardConstMeta,
      argValues: [that],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateChessGameManagerGameManagerClearBoardConstMeta =>
      const TaskConstMeta(
        debugName: "GameManager_clear_board",
        argNames: ["that"],
      );

  @override
  int crateChessGameManagerGameManagerCountPlayerKings(
      {required GameManager that, required Player player}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
            that, serializer);
        sse_encode_player(player, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 9)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_u_8,
        decodeErrorData: null,
      ),
      constMeta: kCrateChessGameManagerGameManagerCountPlayerKingsConstMeta,
      argValues: [that, player],
      apiImpl: this,
    ));
  }

  TaskConstMeta
      get kCrateChessGameManagerGameManagerCountPlayerKingsConstMeta =>
          const TaskConstMeta(
            debugName: "GameManager_count_player_kings",
            argNames: ["that", "player"],
          );

  @override
  Future<void> crateChessGameManagerGameManagerExchangePieces(
      {required GameManager that}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_RefMut_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
            that, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 10, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_unit,
        decodeErrorData: null,
      ),
      constMeta: kCrateChessGameManagerGameManagerExchangePiecesConstMeta,
      argValues: [that],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateChessGameManagerGameManagerExchangePiecesConstMeta =>
      const TaskConstMeta(
        debugName: "GameManager_exchange_pieces",
        argNames: ["that"],
      );

  @override
  Future<U8Array256> crateChessGameManagerGameManagerGetDisplayBoard(
      {required GameManager that}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
            that, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 11, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_u_8_array_256,
        decodeErrorData: null,
      ),
      constMeta: kCrateChessGameManagerGameManagerGetDisplayBoardConstMeta,
      argValues: [that],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateChessGameManagerGameManagerGetDisplayBoardConstMeta =>
      const TaskConstMeta(
        debugName: "GameManager_get_display_board",
        argNames: ["that"],
      );

  @override
  Future<String> crateChessGameManagerGameManagerGetDisplayBoardFen(
      {required GameManager that}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
            that, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 12, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_String,
        decodeErrorData: null,
      ),
      constMeta: kCrateChessGameManagerGameManagerGetDisplayBoardFenConstMeta,
      argValues: [that],
      apiImpl: this,
    ));
  }

  TaskConstMeta
      get kCrateChessGameManagerGameManagerGetDisplayBoardFenConstMeta =>
          const TaskConstMeta(
            debugName: "GameManager_get_display_board_fen",
            argNames: ["that"],
          );

  @override
  U8Array256 crateChessGameManagerGameManagerGetOrigBoard() {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 13)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_u_8_array_256,
        decodeErrorData: null,
      ),
      constMeta: kCrateChessGameManagerGameManagerGetOrigBoardConstMeta,
      argValues: [],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateChessGameManagerGameManagerGetOrigBoardConstMeta =>
      const TaskConstMeta(
        debugName: "GameManager_get_orig_board",
        argNames: [],
      );

  @override
  Future<PieceMove?> crateChessGameManagerGameManagerGetRandomLegalMove(
      {required GameManager that}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
            that, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 14, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_opt_box_autoadd_piece_move,
        decodeErrorData: null,
      ),
      constMeta: kCrateChessGameManagerGameManagerGetRandomLegalMoveConstMeta,
      argValues: [that],
      apiImpl: this,
    ));
  }

  TaskConstMeta
      get kCrateChessGameManagerGameManagerGetRandomLegalMoveConstMeta =>
          const TaskConstMeta(
            debugName: "GameManager_get_random_legal_move",
            argNames: ["that"],
          );

  @override
  SidePieceType crateChessGameManagerGameManagerGetSidePieceByIndex(
      {required GameManager that, required int pos}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
            that, serializer);
        sse_encode_u_8(pos, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 15)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_side_piece_type,
        decodeErrorData: null,
      ),
      constMeta: kCrateChessGameManagerGameManagerGetSidePieceByIndexConstMeta,
      argValues: [that, pos],
      apiImpl: this,
    ));
  }

  TaskConstMeta
      get kCrateChessGameManagerGameManagerGetSidePieceByIndexConstMeta =>
          const TaskConstMeta(
            debugName: "GameManager_get_side_piece_by_index",
            argNames: ["that", "pos"],
          );

  @override
  Future<String> crateChessGameManagerGameManagerIccsMoveToChineseMove(
      {required GameManager that, required String iccsMove}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
            that, serializer);
        sse_encode_String(iccsMove, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 16, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_String,
        decodeErrorData: null,
      ),
      constMeta:
          kCrateChessGameManagerGameManagerIccsMoveToChineseMoveConstMeta,
      argValues: [that, iccsMove],
      apiImpl: this,
    ));
  }

  TaskConstMeta
      get kCrateChessGameManagerGameManagerIccsMoveToChineseMoveConstMeta =>
          const TaskConstMeta(
            debugName: "GameManager_iccs_move_to_chinese_move",
            argNames: ["that", "iccsMove"],
          );

  @override
  Future<bool> crateChessGameManagerGameManagerIsCurrentPlayerLosing(
      {required GameManager that}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
            that, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 17, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_bool,
        decodeErrorData: null,
      ),
      constMeta:
          kCrateChessGameManagerGameManagerIsCurrentPlayerLosingConstMeta,
      argValues: [that],
      apiImpl: this,
    ));
  }

  TaskConstMeta
      get kCrateChessGameManagerGameManagerIsCurrentPlayerLosingConstMeta =>
          const TaskConstMeta(
            debugName: "GameManager_is_current_player_losing",
            argNames: ["that"],
          );

  @override
  Future<bool> crateChessGameManagerGameManagerIsCurrentPlayerWinning(
      {required GameManager that}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
            that, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 18, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_bool,
        decodeErrorData: null,
      ),
      constMeta:
          kCrateChessGameManagerGameManagerIsCurrentPlayerWinningConstMeta,
      argValues: [that],
      apiImpl: this,
    ));
  }

  TaskConstMeta
      get kCrateChessGameManagerGameManagerIsCurrentPlayerWinningConstMeta =>
          const TaskConstMeta(
            debugName: "GameManager_is_current_player_winning",
            argNames: ["that"],
          );

  @override
  Future<bool> crateChessGameManagerGameManagerIsPieceValidMove(
      {required GameManager that,
      required int visualSrcRow,
      required int visualSrcCol,
      required int visualDstRow,
      required int visualDstCol}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
            that, serializer);
        sse_encode_u_8(visualSrcRow, serializer);
        sse_encode_u_8(visualSrcCol, serializer);
        sse_encode_u_8(visualDstRow, serializer);
        sse_encode_u_8(visualDstCol, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 19, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_bool,
        decodeErrorData: null,
      ),
      constMeta: kCrateChessGameManagerGameManagerIsPieceValidMoveConstMeta,
      argValues: [that, visualSrcRow, visualSrcCol, visualDstRow, visualDstCol],
      apiImpl: this,
    ));
  }

  TaskConstMeta
      get kCrateChessGameManagerGameManagerIsPieceValidMoveConstMeta =>
          const TaskConstMeta(
            debugName: "GameManager_is_piece_valid_move",
            argNames: [
              "that",
              "visualSrcRow",
              "visualSrcCol",
              "visualDstRow",
              "visualDstCol"
            ],
          );

  @override
  Future<bool> crateChessGameManagerGameManagerIsValidPiecePlacement(
      {required GameManager that,
      required int visualRow,
      required int visualCol,
      required SidePieceType pieceType}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
            that, serializer);
        sse_encode_u_8(visualRow, serializer);
        sse_encode_u_8(visualCol, serializer);
        sse_encode_side_piece_type(pieceType, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 20, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_bool,
        decodeErrorData: null,
      ),
      constMeta:
          kCrateChessGameManagerGameManagerIsValidPiecePlacementConstMeta,
      argValues: [that, visualRow, visualCol, pieceType],
      apiImpl: this,
    ));
  }

  TaskConstMeta
      get kCrateChessGameManagerGameManagerIsValidPiecePlacementConstMeta =>
          const TaskConstMeta(
            debugName: "GameManager_is_valid_piece_placement",
            argNames: ["that", "visualRow", "visualCol", "pieceType"],
          );

  @override
  bool crateChessGameManagerGameManagerIsValidSetupForGameStart(
      {required GameManager that}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
            that, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 21)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_bool,
        decodeErrorData: null,
      ),
      constMeta:
          kCrateChessGameManagerGameManagerIsValidSetupForGameStartConstMeta,
      argValues: [that],
      apiImpl: this,
    ));
  }

  TaskConstMeta
      get kCrateChessGameManagerGameManagerIsValidSetupForGameStartConstMeta =>
          const TaskConstMeta(
            debugName: "GameManager_is_valid_setup_for_game_start",
            argNames: ["that"],
          );

  @override
  void crateChessGameManagerGameManagerLoadFen(
      {required GameManager that, required String fenStr}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_RefMut_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
            that, serializer);
        sse_encode_String(fenStr, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 22)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_unit,
        decodeErrorData: sse_decode_String,
      ),
      constMeta: kCrateChessGameManagerGameManagerLoadFenConstMeta,
      argValues: [that, fenStr],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateChessGameManagerGameManagerLoadFenConstMeta =>
      const TaskConstMeta(
        debugName: "GameManager_load_fen",
        argNames: ["that", "fenStr"],
      );

  @override
  void crateChessGameManagerGameManagerMakeMoveByIccs(
      {required GameManager that, required String iccsMove}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_RefMut_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
            that, serializer);
        sse_encode_String(iccsMove, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 23)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_unit,
        decodeErrorData: null,
      ),
      constMeta: kCrateChessGameManagerGameManagerMakeMoveByIccsConstMeta,
      argValues: [that, iccsMove],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateChessGameManagerGameManagerMakeMoveByIccsConstMeta =>
      const TaskConstMeta(
        debugName: "GameManager_make_move_by_iccs",
        argNames: ["that", "iccsMove"],
      );

  @override
  GameManager crateChessGameManagerGameManagerNew() {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 24)!;
      },
      codec: SseCodec(
        decodeSuccessData:
            sse_decode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager,
        decodeErrorData: null,
      ),
      constMeta: kCrateChessGameManagerGameManagerNewConstMeta,
      argValues: [],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateChessGameManagerGameManagerNewConstMeta =>
      const TaskConstMeta(
        debugName: "GameManager_new",
        argNames: [],
      );

  @override
  PlacementValidity crateChessGameManagerGameManagerPlacePieceOnBoard(
      {required GameManager that,
      required int visualRow,
      required int visualCol,
      required SidePieceType pieceToPlace}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_RefMut_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
            that, serializer);
        sse_encode_u_8(visualRow, serializer);
        sse_encode_u_8(visualCol, serializer);
        sse_encode_side_piece_type(pieceToPlace, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 25)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_placement_validity,
        decodeErrorData: null,
      ),
      constMeta: kCrateChessGameManagerGameManagerPlacePieceOnBoardConstMeta,
      argValues: [that, visualRow, visualCol, pieceToPlace],
      apiImpl: this,
    ));
  }

  TaskConstMeta
      get kCrateChessGameManagerGameManagerPlacePieceOnBoardConstMeta =>
          const TaskConstMeta(
            debugName: "GameManager_place_piece_on_board",
            argNames: ["that", "visualRow", "visualCol", "pieceToPlace"],
          );

  @override
  void crateChessGameManagerGameManagerReset({required GameManager that}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_RefMut_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
            that, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 26)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_unit,
        decodeErrorData: null,
      ),
      constMeta: kCrateChessGameManagerGameManagerResetConstMeta,
      argValues: [that],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateChessGameManagerGameManagerResetConstMeta =>
      const TaskConstMeta(
        debugName: "GameManager_reset",
        argNames: ["that"],
      );

  @override
  void crateChessGameManagerGameManagerSwitchPlayer(
      {required GameManager that}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_RefMut_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
            that, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 27)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_unit,
        decodeErrorData: null,
      ),
      constMeta: kCrateChessGameManagerGameManagerSwitchPlayerConstMeta,
      argValues: [that],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateChessGameManagerGameManagerSwitchPlayerConstMeta =>
      const TaskConstMeta(
        debugName: "GameManager_switch_player",
        argNames: ["that"],
      );

  @override
  Future<void> crateChessGameManagerGameManagerUpdateBoard(
      {required GameManager that,
      required int visualRow,
      required int visualCol,
      required int pieceIndex}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_RefMut_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
            that, serializer);
        sse_encode_u_8(visualRow, serializer);
        sse_encode_u_8(visualCol, serializer);
        sse_encode_u_8(pieceIndex, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 28, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_unit,
        decodeErrorData: null,
      ),
      constMeta: kCrateChessGameManagerGameManagerUpdateBoardConstMeta,
      argValues: [that, visualRow, visualCol, pieceIndex],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateChessGameManagerGameManagerUpdateBoardConstMeta =>
      const TaskConstMeta(
        debugName: "GameManager_update_board",
        argNames: ["that", "visualRow", "visualCol", "pieceIndex"],
      );

  @override
  void crateChessGameManagerGameManagerValidateFullBoardLayout(
      {required GameManager that}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
            that, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 29)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_unit,
        decodeErrorData: sse_decode_String,
      ),
      constMeta:
          kCrateChessGameManagerGameManagerValidateFullBoardLayoutConstMeta,
      argValues: [that],
      apiImpl: this,
    ));
  }

  TaskConstMeta
      get kCrateChessGameManagerGameManagerValidateFullBoardLayoutConstMeta =>
          const TaskConstMeta(
            debugName: "GameManager_validate_full_board_layout",
            argNames: ["that"],
          );

  @override
  String crateChessBoardUtilsBoardToFen(
      {required List<int> board, required Player currentPlayer}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_list_prim_u_8_loose(board, serializer);
        sse_encode_player(currentPlayer, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 30)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_String,
        decodeErrorData: null,
      ),
      constMeta: kCrateChessBoardUtilsBoardToFenConstMeta,
      argValues: [board, currentPlayer],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateChessBoardUtilsBoardToFenConstMeta =>
      const TaskConstMeta(
        debugName: "board_to_fen",
        argNames: ["board", "currentPlayer"],
      );

  @override
  int? crateChessBoardUtilsConvertArray256IndexToArray90Index(
      {required int index256}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_u_8(index256, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 31)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_opt_box_autoadd_u_8,
        decodeErrorData: null,
      ),
      constMeta:
          kCrateChessBoardUtilsConvertArray256IndexToArray90IndexConstMeta,
      argValues: [index256],
      apiImpl: this,
    ));
  }

  TaskConstMeta
      get kCrateChessBoardUtilsConvertArray256IndexToArray90IndexConstMeta =>
          const TaskConstMeta(
            debugName: "convert_array_256_index_to_array_90_index",
            argNames: ["index256"],
          );

  @override
  int crateChessBoardUtilsConvertArray90IndexToArray256Index(
      {required int index90}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_u_8(index90, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 32)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_u_8,
        decodeErrorData: null,
      ),
      constMeta:
          kCrateChessBoardUtilsConvertArray90IndexToArray256IndexConstMeta,
      argValues: [index90],
      apiImpl: this,
    ));
  }

  TaskConstMeta
      get kCrateChessBoardUtilsConvertArray90IndexToArray256IndexConstMeta =>
          const TaskConstMeta(
            debugName: "convert_array_90_index_to_array_256_index",
            argNames: ["index90"],
          );

  @override
  U8Array90 crateChessBoardUtilsConvertBoard256ToBoard90(
      {required U8Array256 board256}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_u_8_array_256(board256, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 33)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_u_8_array_90,
        decodeErrorData: null,
      ),
      constMeta: kCrateChessBoardUtilsConvertBoard256ToBoard90ConstMeta,
      argValues: [board256],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateChessBoardUtilsConvertBoard256ToBoard90ConstMeta =>
      const TaskConstMeta(
        debugName: "convert_board_256_to_board_90",
        argNames: ["board256"],
      );

  @override
  U8Array256 crateChessBoardUtilsConvertBoard90ToBoard256(
      {required U8Array90 board90}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_u_8_array_90(board90, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 34)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_u_8_array_256,
        decodeErrorData: null,
      ),
      constMeta: kCrateChessBoardUtilsConvertBoard90ToBoard256ConstMeta,
      argValues: [board90],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateChessBoardUtilsConvertBoard90ToBoard256ConstMeta =>
      const TaskConstMeta(
        debugName: "convert_board_90_to_board_256",
        argNames: ["board90"],
      );

  @override
  (U8Array256, Player) crateChessBoardUtilsFenToBoard({required String fen}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_String(fen, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 35)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_record_u_8_array_256_player,
        decodeErrorData: sse_decode_String,
      ),
      constMeta: kCrateChessBoardUtilsFenToBoardConstMeta,
      argValues: [fen],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateChessBoardUtilsFenToBoardConstMeta =>
      const TaskConstMeta(
        debugName: "fen_to_board",
        argNames: ["fen"],
      );

  @override
  Future<String> crateChessOthersGenerateStatePicture(
      {required U8Array90 board}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_u_8_array_90(board, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 36, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_String,
        decodeErrorData: null,
      ),
      constMeta: kCrateChessOthersGenerateStatePictureConstMeta,
      argValues: [board],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateChessOthersGenerateStatePictureConstMeta =>
      const TaskConstMeta(
        debugName: "generate_state_picture",
        argNames: ["board"],
      );

  @override
  int crateChessBoardUtilsGetArray256IndexFromBoardRowCol(
      {required int row, required int col}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_u_8(row, serializer);
        sse_encode_u_8(col, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 37)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_u_8,
        decodeErrorData: null,
      ),
      constMeta: kCrateChessBoardUtilsGetArray256IndexFromBoardRowColConstMeta,
      argValues: [row, col],
      apiImpl: this,
    ));
  }

  TaskConstMeta
      get kCrateChessBoardUtilsGetArray256IndexFromBoardRowColConstMeta =>
          const TaskConstMeta(
            debugName: "get_array_256_index_from_board_row_col",
            argNames: ["row", "col"],
          );

  @override
  Future<String> crateApiUcciApiGetEngineName({required Player player}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_player(player, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 38, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_String,
        decodeErrorData: null,
      ),
      constMeta: kCrateApiUcciApiGetEngineNameConstMeta,
      argValues: [player],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiUcciApiGetEngineNameConstMeta =>
      const TaskConstMeta(
        debugName: "get_engine_name",
        argNames: ["player"],
      );

  @override
  Future<String> crateChessMoveUtilsGetIccsMoveStrFromPos(
      {required int srcIndex, required int dstIndex}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_u_8(srcIndex, serializer);
        sse_encode_u_8(dstIndex, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 39, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_String,
        decodeErrorData: null,
      ),
      constMeta: kCrateChessMoveUtilsGetIccsMoveStrFromPosConstMeta,
      argValues: [srcIndex, dstIndex],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateChessMoveUtilsGetIccsMoveStrFromPosConstMeta =>
      const TaskConstMeta(
        debugName: "get_iccs_move_str_from_pos",
        argNames: ["srcIndex", "dstIndex"],
      );

  @override
  (int, int, int, int) crateChessMoveUtilsGetSrcDstRowColFromIccsMove(
      {required String iccsMove}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_String(iccsMove, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 40)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_record_u_8_u_8_u_8_u_8,
        decodeErrorData: null,
      ),
      constMeta: kCrateChessMoveUtilsGetSrcDstRowColFromIccsMoveConstMeta,
      argValues: [iccsMove],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateChessMoveUtilsGetSrcDstRowColFromIccsMoveConstMeta =>
      const TaskConstMeta(
        debugName: "get_src_dst_row_col_from_iccs_move",
        argNames: ["iccsMove"],
      );

  @override
  PieceType crateChessPieceUtilsGetUnsidePieceBySidePiece(
      {required SidePieceType sidePiece}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_side_piece_type(sidePiece, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 41)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_piece_type,
        decodeErrorData: null,
      ),
      constMeta: kCrateChessPieceUtilsGetUnsidePieceBySidePieceConstMeta,
      argValues: [sidePiece],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateChessPieceUtilsGetUnsidePieceBySidePieceConstMeta =>
      const TaskConstMeta(
        debugName: "get_unside_piece_by_side_piece",
        argNames: ["sidePiece"],
      );

  @override
  Future<void> crateApiLogApiInitApp() {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 42, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_unit,
        decodeErrorData: null,
      ),
      constMeta: kCrateApiLogApiInitAppConstMeta,
      argValues: [],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiLogApiInitAppConstMeta => const TaskConstMeta(
        debugName: "init_app",
        argNames: [],
      );

  @override
  Future<bool> crateChessPieceUtilsIsBlackPiece(
      {required SidePieceType sidePiece}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_side_piece_type(sidePiece, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 43, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_bool,
        decodeErrorData: null,
      ),
      constMeta: kCrateChessPieceUtilsIsBlackPieceConstMeta,
      argValues: [sidePiece],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateChessPieceUtilsIsBlackPieceConstMeta =>
      const TaskConstMeta(
        debugName: "is_black_piece",
        argNames: ["sidePiece"],
      );

  @override
  Future<bool> crateChessBoardUtilsIsPosInBoard({required int pos}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_u_8(pos, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 44, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_bool,
        decodeErrorData: null,
      ),
      constMeta: kCrateChessBoardUtilsIsPosInBoardConstMeta,
      argValues: [pos],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateChessBoardUtilsIsPosInBoardConstMeta =>
      const TaskConstMeta(
        debugName: "is_pos_in_board",
        argNames: ["pos"],
      );

  @override
  Future<bool> crateChessBoardUtilsIsPosInFort({required int pos}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_u_8(pos, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 45, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_bool,
        decodeErrorData: null,
      ),
      constMeta: kCrateChessBoardUtilsIsPosInFortConstMeta,
      argValues: [pos],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateChessBoardUtilsIsPosInFortConstMeta =>
      const TaskConstMeta(
        debugName: "is_pos_in_fort",
        argNames: ["pos"],
      );

  @override
  Future<bool> crateApiUcciApiIsProcessLoaded(
      {required int msec, required Player player}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_u_32(msec, serializer);
        sse_encode_player(player, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 46, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_bool,
        decodeErrorData: null,
      ),
      constMeta: kCrateApiUcciApiIsProcessLoadedConstMeta,
      argValues: [msec, player],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiUcciApiIsProcessLoadedConstMeta =>
      const TaskConstMeta(
        debugName: "is_process_loaded",
        argNames: ["msec", "player"],
      );

  @override
  Future<bool> crateApiUcciApiIsProcessUnloaded(
      {required int msec, required Player player}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_u_32(msec, serializer);
        sse_encode_player(player, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 47, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_bool,
        decodeErrorData: null,
      ),
      constMeta: kCrateApiUcciApiIsProcessUnloadedConstMeta,
      argValues: [msec, player],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiUcciApiIsProcessUnloadedConstMeta =>
      const TaskConstMeta(
        debugName: "is_process_unloaded",
        argNames: ["msec", "player"],
      );

  @override
  Future<bool> crateChessPieceUtilsIsRedPiece(
      {required SidePieceType sidePiece}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_side_piece_type(sidePiece, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 48, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_bool,
        decodeErrorData: null,
      ),
      constMeta: kCrateChessPieceUtilsIsRedPieceConstMeta,
      argValues: [sidePiece],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateChessPieceUtilsIsRedPieceConstMeta =>
      const TaskConstMeta(
        debugName: "is_red_piece",
        argNames: ["sidePiece"],
      );

  @override
  PieceMove crateChessPieceUtilsPieceMoveFromIccs(
      {required String iccs, required GameManager gameManager}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_String(iccs, serializer);
        sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
            gameManager, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 49)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_piece_move,
        decodeErrorData: null,
      ),
      constMeta: kCrateChessPieceUtilsPieceMoveFromIccsConstMeta,
      argValues: [iccs, gameManager],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateChessPieceUtilsPieceMoveFromIccsConstMeta =>
      const TaskConstMeta(
        debugName: "piece_move_from_iccs",
        argNames: ["iccs", "gameManager"],
      );

  @override
  PieceMove crateChessPieceUtilsPieceMoveNew(
      {required int srcRow,
      required int srcCol,
      required int dstRow,
      required int dstCol,
      required Player player}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_u_8(srcRow, serializer);
        sse_encode_u_8(srcCol, serializer);
        sse_encode_u_8(dstRow, serializer);
        sse_encode_u_8(dstCol, serializer);
        sse_encode_player(player, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 50)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_piece_move,
        decodeErrorData: null,
      ),
      constMeta: kCrateChessPieceUtilsPieceMoveNewConstMeta,
      argValues: [srcRow, srcCol, dstRow, dstCol, player],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateChessPieceUtilsPieceMoveNewConstMeta =>
      const TaskConstMeta(
        debugName: "piece_move_new",
        argNames: ["srcRow", "srcCol", "dstRow", "dstCol", "player"],
      );

  @override
  Future<int> crateChessPieceUtilsPieceTypeGetMaxCount(
      {required PieceType that}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_piece_type(that, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 51, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_u_8,
        decodeErrorData: null,
      ),
      constMeta: kCrateChessPieceUtilsPieceTypeGetMaxCountConstMeta,
      argValues: [that],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateChessPieceUtilsPieceTypeGetMaxCountConstMeta =>
      const TaskConstMeta(
        debugName: "piece_type_get_max_count",
        argNames: ["that"],
      );

  @override
  String crateChessPieceUtilsPlayerGetName({required Player that}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_player(that, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 52)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_String,
        decodeErrorData: null,
      ),
      constMeta: kCrateChessPieceUtilsPlayerGetNameConstMeta,
      argValues: [that],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateChessPieceUtilsPlayerGetNameConstMeta =>
      const TaskConstMeta(
        debugName: "player_get_name",
        argNames: ["that"],
      );

  @override
  Player crateChessPieceUtilsPlayerOpposite({required Player that}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_player(that, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 53)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_player,
        decodeErrorData: null,
      ),
      constMeta: kCrateChessPieceUtilsPlayerOppositeConstMeta,
      argValues: [that],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateChessPieceUtilsPlayerOppositeConstMeta =>
      const TaskConstMeta(
        debugName: "player_opposite",
        argNames: ["that"],
      );

  @override
  void crateChessPieceUtilsSidePieceTypeGetChineseName(
      {required SidePieceType that}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_side_piece_type(that, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 54)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_unit,
        decodeErrorData: null,
      ),
      constMeta: kCrateChessPieceUtilsSidePieceTypeGetChineseNameConstMeta,
      argValues: [that],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateChessPieceUtilsSidePieceTypeGetChineseNameConstMeta =>
      const TaskConstMeta(
        debugName: "side_piece_type_get_chinese_name",
        argNames: ["that"],
      );

  @override
  Player crateChessPieceUtilsSidePieceTypeGetSide(
      {required SidePieceType that}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_side_piece_type(that, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 55)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_player,
        decodeErrorData: null,
      ),
      constMeta: kCrateChessPieceUtilsSidePieceTypeGetSideConstMeta,
      argValues: [that],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateChessPieceUtilsSidePieceTypeGetSideConstMeta =>
      const TaskConstMeta(
        debugName: "side_piece_type_get_side",
        argNames: ["that"],
      );

  @override
  SidePieceType crateChessPieceUtilsSidePieceTypeGetSidePieceTypeByIndex(
      {required int index}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_u_8(index, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 56)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_side_piece_type,
        decodeErrorData: null,
      ),
      constMeta:
          kCrateChessPieceUtilsSidePieceTypeGetSidePieceTypeByIndexConstMeta,
      argValues: [index],
      apiImpl: this,
    ));
  }

  TaskConstMeta
      get kCrateChessPieceUtilsSidePieceTypeGetSidePieceTypeByIndexConstMeta =>
          const TaskConstMeta(
            debugName: "side_piece_type_get_side_piece_type_by_index",
            argNames: ["index"],
          );

  @override
  int crateChessPieceUtilsSidePieceTypeGetSidePieceTypeIndex(
      {required SidePieceType that}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_side_piece_type(that, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 57)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_u_8,
        decodeErrorData: null,
      ),
      constMeta:
          kCrateChessPieceUtilsSidePieceTypeGetSidePieceTypeIndexConstMeta,
      argValues: [that],
      apiImpl: this,
    ));
  }

  TaskConstMeta
      get kCrateChessPieceUtilsSidePieceTypeGetSidePieceTypeIndexConstMeta =>
          const TaskConstMeta(
            debugName: "side_piece_type_get_side_piece_type_index",
            argNames: ["that"],
          );

  @override
  Stream<String> crateApiUcciApiSubscribeUcciEngine(
      {required Player player, required String enginePath}) {
    final listener = RustStreamSink<String>();
    unawaited(handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_player(player, serializer);
        sse_encode_String(enginePath, serializer);
        sse_encode_StreamSink_String_Sse(listener, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 58, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_unit,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta: kCrateApiUcciApiSubscribeUcciEngineConstMeta,
      argValues: [player, enginePath, listener],
      apiImpl: this,
    )));
    return listener.stream;
  }

  TaskConstMeta get kCrateApiUcciApiSubscribeUcciEngineConstMeta =>
      const TaskConstMeta(
        debugName: "subscribe_ucci_engine",
        argNames: ["player", "enginePath", "listener"],
      );

  @override
  Future<bool> crateApiUcciApiWriteToProcess(
      {required String command,
      required int msec,
      required Player player,
      String? checkStrOption}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_String(command, serializer);
        sse_encode_u_32(msec, serializer);
        sse_encode_player(player, serializer);
        sse_encode_opt_String(checkStrOption, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 59, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_bool,
        decodeErrorData: null,
      ),
      constMeta: kCrateApiUcciApiWriteToProcessConstMeta,
      argValues: [command, msec, player, checkStrOption],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiUcciApiWriteToProcessConstMeta =>
      const TaskConstMeta(
        debugName: "write_to_process",
        argNames: ["command", "msec", "player", "checkStrOption"],
      );

  RustArcIncrementStrongCountFnType
      get rust_arc_increment_strong_count_GameManager => wire
          .rust_arc_increment_strong_count_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager;

  RustArcDecrementStrongCountFnType
      get rust_arc_decrement_strong_count_GameManager => wire
          .rust_arc_decrement_strong_count_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager;

  @protected
  AnyhowException dco_decode_AnyhowException(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return AnyhowException(raw as String);
  }

  @protected
  GameManager
      dco_decode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
          dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return GameManagerImpl.frbInternalDcoDecode(raw as List<dynamic>);
  }

  @protected
  GameManager
      dco_decode_Auto_RefMut_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
          dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return GameManagerImpl.frbInternalDcoDecode(raw as List<dynamic>);
  }

  @protected
  GameManager
      dco_decode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
          dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return GameManagerImpl.frbInternalDcoDecode(raw as List<dynamic>);
  }

  @protected
  GameManager
      dco_decode_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
          dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return GameManagerImpl.frbInternalDcoDecode(raw as List<dynamic>);
  }

  @protected
  RustStreamSink<String> dco_decode_StreamSink_String_Sse(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    throw UnimplementedError();
  }

  @protected
  String dco_decode_String(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return raw as String;
  }

  @protected
  bool dco_decode_bool(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return raw as bool;
  }

  @protected
  PieceMove dco_decode_box_autoadd_piece_move(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return dco_decode_piece_move(raw);
  }

  @protected
  int dco_decode_box_autoadd_u_8(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return raw as int;
  }

  @protected
  int dco_decode_i_32(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return raw as int;
  }

  @protected
  List<int> dco_decode_list_prim_u_8_loose(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return raw as List<int>;
  }

  @protected
  Uint8List dco_decode_list_prim_u_8_strict(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return raw as Uint8List;
  }

  @protected
  String? dco_decode_opt_String(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return raw == null ? null : dco_decode_String(raw);
  }

  @protected
  PieceMove? dco_decode_opt_box_autoadd_piece_move(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return raw == null ? null : dco_decode_box_autoadd_piece_move(raw);
  }

  @protected
  int? dco_decode_opt_box_autoadd_u_8(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return raw == null ? null : dco_decode_box_autoadd_u_8(raw);
  }

  @protected
  PieceMove dco_decode_piece_move(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    final arr = raw as List<dynamic>;
    if (arr.length != 7)
      throw Exception('unexpected arr length: expect 7 but see ${arr.length}');
    return PieceMove.raw(
      chinese: dco_decode_String(arr[0]),
      iccs: dco_decode_String(arr[1]),
      srcRow: dco_decode_u_8(arr[2]),
      srcCol: dco_decode_u_8(arr[3]),
      dstRow: dco_decode_u_8(arr[4]),
      dstCol: dco_decode_u_8(arr[5]),
      player: dco_decode_player(arr[6]),
    );
  }

  @protected
  PieceType dco_decode_piece_type(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return PieceType.values[raw as int];
  }

  @protected
  PlacementValidity dco_decode_placement_validity(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    switch (raw[0]) {
      case 0:
        return const PlacementValidity_Valid();
      case 1:
        return PlacementValidity_InvalidLocation(
          dco_decode_String(raw[1]),
        );
      case 2:
        return PlacementValidity_MaxPiecesReached(
          dco_decode_String(raw[1]),
        );
      case 3:
        return PlacementValidity_KingsFacing(
          dco_decode_String(raw[1]),
        );
      default:
        throw Exception("unreachable");
    }
  }

  @protected
  Player dco_decode_player(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return Player.values[raw as int];
  }

  @protected
  (U8Array256, Player) dco_decode_record_u_8_array_256_player(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    final arr = raw as List<dynamic>;
    if (arr.length != 2) {
      throw Exception('Expected 2 elements, got ${arr.length}');
    }
    return (
      dco_decode_u_8_array_256(arr[0]),
      dco_decode_player(arr[1]),
    );
  }

  @protected
  (int, int, int, int) dco_decode_record_u_8_u_8_u_8_u_8(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    final arr = raw as List<dynamic>;
    if (arr.length != 4) {
      throw Exception('Expected 4 elements, got ${arr.length}');
    }
    return (
      dco_decode_u_8(arr[0]),
      dco_decode_u_8(arr[1]),
      dco_decode_u_8(arr[2]),
      dco_decode_u_8(arr[3]),
    );
  }

  @protected
  SidePieceType dco_decode_side_piece_type(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return SidePieceType.values[raw as int];
  }

  @protected
  int dco_decode_u_32(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return raw as int;
  }

  @protected
  int dco_decode_u_8(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return raw as int;
  }

  @protected
  U8Array256 dco_decode_u_8_array_256(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return U8Array256(dco_decode_list_prim_u_8_strict(raw));
  }

  @protected
  U8Array90 dco_decode_u_8_array_90(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return U8Array90(dco_decode_list_prim_u_8_strict(raw));
  }

  @protected
  void dco_decode_unit(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return;
  }

  @protected
  BigInt dco_decode_usize(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return dcoDecodeU64(raw);
  }

  @protected
  AnyhowException sse_decode_AnyhowException(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var inner = sse_decode_String(deserializer);
    return AnyhowException(inner);
  }

  @protected
  GameManager
      sse_decode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
          SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return GameManagerImpl.frbInternalSseDecode(
        sse_decode_usize(deserializer), sse_decode_i_32(deserializer));
  }

  @protected
  GameManager
      sse_decode_Auto_RefMut_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
          SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return GameManagerImpl.frbInternalSseDecode(
        sse_decode_usize(deserializer), sse_decode_i_32(deserializer));
  }

  @protected
  GameManager
      sse_decode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
          SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return GameManagerImpl.frbInternalSseDecode(
        sse_decode_usize(deserializer), sse_decode_i_32(deserializer));
  }

  @protected
  GameManager
      sse_decode_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
          SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return GameManagerImpl.frbInternalSseDecode(
        sse_decode_usize(deserializer), sse_decode_i_32(deserializer));
  }

  @protected
  RustStreamSink<String> sse_decode_StreamSink_String_Sse(
      SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    throw UnimplementedError('Unreachable ()');
  }

  @protected
  String sse_decode_String(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var inner = sse_decode_list_prim_u_8_strict(deserializer);
    return utf8.decoder.convert(inner);
  }

  @protected
  bool sse_decode_bool(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return deserializer.buffer.getUint8() != 0;
  }

  @protected
  PieceMove sse_decode_box_autoadd_piece_move(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return (sse_decode_piece_move(deserializer));
  }

  @protected
  int sse_decode_box_autoadd_u_8(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return (sse_decode_u_8(deserializer));
  }

  @protected
  int sse_decode_i_32(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return deserializer.buffer.getInt32();
  }

  @protected
  List<int> sse_decode_list_prim_u_8_loose(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var len_ = sse_decode_i_32(deserializer);
    return deserializer.buffer.getUint8List(len_);
  }

  @protected
  Uint8List sse_decode_list_prim_u_8_strict(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var len_ = sse_decode_i_32(deserializer);
    return deserializer.buffer.getUint8List(len_);
  }

  @protected
  String? sse_decode_opt_String(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs

    if (sse_decode_bool(deserializer)) {
      return (sse_decode_String(deserializer));
    } else {
      return null;
    }
  }

  @protected
  PieceMove? sse_decode_opt_box_autoadd_piece_move(
      SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs

    if (sse_decode_bool(deserializer)) {
      return (sse_decode_box_autoadd_piece_move(deserializer));
    } else {
      return null;
    }
  }

  @protected
  int? sse_decode_opt_box_autoadd_u_8(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs

    if (sse_decode_bool(deserializer)) {
      return (sse_decode_box_autoadd_u_8(deserializer));
    } else {
      return null;
    }
  }

  @protected
  PieceMove sse_decode_piece_move(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var var_chinese = sse_decode_String(deserializer);
    var var_iccs = sse_decode_String(deserializer);
    var var_srcRow = sse_decode_u_8(deserializer);
    var var_srcCol = sse_decode_u_8(deserializer);
    var var_dstRow = sse_decode_u_8(deserializer);
    var var_dstCol = sse_decode_u_8(deserializer);
    var var_player = sse_decode_player(deserializer);
    return PieceMove.raw(
        chinese: var_chinese,
        iccs: var_iccs,
        srcRow: var_srcRow,
        srcCol: var_srcCol,
        dstRow: var_dstRow,
        dstCol: var_dstCol,
        player: var_player);
  }

  @protected
  PieceType sse_decode_piece_type(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var inner = sse_decode_i_32(deserializer);
    return PieceType.values[inner];
  }

  @protected
  PlacementValidity sse_decode_placement_validity(
      SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs

    var tag_ = sse_decode_i_32(deserializer);
    switch (tag_) {
      case 0:
        return const PlacementValidity_Valid();
      case 1:
        var var_field0 = sse_decode_String(deserializer);
        return PlacementValidity_InvalidLocation(var_field0);
      case 2:
        var var_field0 = sse_decode_String(deserializer);
        return PlacementValidity_MaxPiecesReached(var_field0);
      case 3:
        var var_field0 = sse_decode_String(deserializer);
        return PlacementValidity_KingsFacing(var_field0);
      default:
        throw UnimplementedError('');
    }
  }

  @protected
  Player sse_decode_player(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var inner = sse_decode_i_32(deserializer);
    return Player.values[inner];
  }

  @protected
  (U8Array256, Player) sse_decode_record_u_8_array_256_player(
      SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var var_field0 = sse_decode_u_8_array_256(deserializer);
    var var_field1 = sse_decode_player(deserializer);
    return (var_field0, var_field1);
  }

  @protected
  (int, int, int, int) sse_decode_record_u_8_u_8_u_8_u_8(
      SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var var_field0 = sse_decode_u_8(deserializer);
    var var_field1 = sse_decode_u_8(deserializer);
    var var_field2 = sse_decode_u_8(deserializer);
    var var_field3 = sse_decode_u_8(deserializer);
    return (var_field0, var_field1, var_field2, var_field3);
  }

  @protected
  SidePieceType sse_decode_side_piece_type(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var inner = sse_decode_i_32(deserializer);
    return SidePieceType.values[inner];
  }

  @protected
  int sse_decode_u_32(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return deserializer.buffer.getUint32();
  }

  @protected
  int sse_decode_u_8(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return deserializer.buffer.getUint8();
  }

  @protected
  U8Array256 sse_decode_u_8_array_256(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var inner = sse_decode_list_prim_u_8_strict(deserializer);
    return U8Array256(inner);
  }

  @protected
  U8Array90 sse_decode_u_8_array_90(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var inner = sse_decode_list_prim_u_8_strict(deserializer);
    return U8Array90(inner);
  }

  @protected
  void sse_decode_unit(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
  }

  @protected
  BigInt sse_decode_usize(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return deserializer.buffer.getBigUint64();
  }

  @protected
  void sse_encode_AnyhowException(
      AnyhowException self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_String(self.message, serializer);
  }

  @protected
  void
      sse_encode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
          GameManager self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_usize(
        (self as GameManagerImpl).frbInternalSseEncode(move: true), serializer);
  }

  @protected
  void
      sse_encode_Auto_RefMut_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
          GameManager self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_usize(
        (self as GameManagerImpl).frbInternalSseEncode(move: false),
        serializer);
  }

  @protected
  void
      sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
          GameManager self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_usize(
        (self as GameManagerImpl).frbInternalSseEncode(move: false),
        serializer);
  }

  @protected
  void
      sse_encode_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGameManager(
          GameManager self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_usize(
        (self as GameManagerImpl).frbInternalSseEncode(move: null), serializer);
  }

  @protected
  void sse_encode_StreamSink_String_Sse(
      RustStreamSink<String> self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_String(
        self.setupAndSerialize(
            codec: SseCodec(
          decodeSuccessData: sse_decode_String,
          decodeErrorData: sse_decode_AnyhowException,
        )),
        serializer);
  }

  @protected
  void sse_encode_String(String self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_list_prim_u_8_strict(utf8.encoder.convert(self), serializer);
  }

  @protected
  void sse_encode_bool(bool self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    serializer.buffer.putUint8(self ? 1 : 0);
  }

  @protected
  void sse_encode_box_autoadd_piece_move(
      PieceMove self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_piece_move(self, serializer);
  }

  @protected
  void sse_encode_box_autoadd_u_8(int self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_u_8(self, serializer);
  }

  @protected
  void sse_encode_i_32(int self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    serializer.buffer.putInt32(self);
  }

  @protected
  void sse_encode_list_prim_u_8_loose(
      List<int> self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_i_32(self.length, serializer);
    serializer.buffer
        .putUint8List(self is Uint8List ? self : Uint8List.fromList(self));
  }

  @protected
  void sse_encode_list_prim_u_8_strict(
      Uint8List self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_i_32(self.length, serializer);
    serializer.buffer.putUint8List(self);
  }

  @protected
  void sse_encode_opt_String(String? self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs

    sse_encode_bool(self != null, serializer);
    if (self != null) {
      sse_encode_String(self, serializer);
    }
  }

  @protected
  void sse_encode_opt_box_autoadd_piece_move(
      PieceMove? self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs

    sse_encode_bool(self != null, serializer);
    if (self != null) {
      sse_encode_box_autoadd_piece_move(self, serializer);
    }
  }

  @protected
  void sse_encode_opt_box_autoadd_u_8(int? self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs

    sse_encode_bool(self != null, serializer);
    if (self != null) {
      sse_encode_box_autoadd_u_8(self, serializer);
    }
  }

  @protected
  void sse_encode_piece_move(PieceMove self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_String(self.chinese, serializer);
    sse_encode_String(self.iccs, serializer);
    sse_encode_u_8(self.srcRow, serializer);
    sse_encode_u_8(self.srcCol, serializer);
    sse_encode_u_8(self.dstRow, serializer);
    sse_encode_u_8(self.dstCol, serializer);
    sse_encode_player(self.player, serializer);
  }

  @protected
  void sse_encode_piece_type(PieceType self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_i_32(self.index, serializer);
  }

  @protected
  void sse_encode_placement_validity(
      PlacementValidity self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    switch (self) {
      case PlacementValidity_Valid():
        sse_encode_i_32(0, serializer);
      case PlacementValidity_InvalidLocation(field0: final field0):
        sse_encode_i_32(1, serializer);
        sse_encode_String(field0, serializer);
      case PlacementValidity_MaxPiecesReached(field0: final field0):
        sse_encode_i_32(2, serializer);
        sse_encode_String(field0, serializer);
      case PlacementValidity_KingsFacing(field0: final field0):
        sse_encode_i_32(3, serializer);
        sse_encode_String(field0, serializer);
    }
  }

  @protected
  void sse_encode_player(Player self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_i_32(self.index, serializer);
  }

  @protected
  void sse_encode_record_u_8_array_256_player(
      (U8Array256, Player) self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_u_8_array_256(self.$1, serializer);
    sse_encode_player(self.$2, serializer);
  }

  @protected
  void sse_encode_record_u_8_u_8_u_8_u_8(
      (int, int, int, int) self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_u_8(self.$1, serializer);
    sse_encode_u_8(self.$2, serializer);
    sse_encode_u_8(self.$3, serializer);
    sse_encode_u_8(self.$4, serializer);
  }

  @protected
  void sse_encode_side_piece_type(
      SidePieceType self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_i_32(self.index, serializer);
  }

  @protected
  void sse_encode_u_32(int self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    serializer.buffer.putUint32(self);
  }

  @protected
  void sse_encode_u_8(int self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    serializer.buffer.putUint8(self);
  }

  @protected
  void sse_encode_u_8_array_256(U8Array256 self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_list_prim_u_8_strict(self.inner, serializer);
  }

  @protected
  void sse_encode_u_8_array_90(U8Array90 self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_list_prim_u_8_strict(self.inner, serializer);
  }

  @protected
  void sse_encode_unit(void self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
  }

  @protected
  void sse_encode_usize(BigInt self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    serializer.buffer.putBigUint64(self);
  }
}

@sealed
class GameManagerImpl extends RustOpaque implements GameManager {
  // Not to be used by end users
  GameManagerImpl.frbInternalDcoDecode(List<dynamic> wire)
      : super.frbInternalDcoDecode(wire, _kStaticData);

  // Not to be used by end users
  GameManagerImpl.frbInternalSseDecode(BigInt ptr, int externalSizeOnNative)
      : super.frbInternalSseDecode(ptr, externalSizeOnNative, _kStaticData);

  static final _kStaticData = RustArcStaticData(
    rustArcIncrementStrongCount:
        RustLib.instance.api.rust_arc_increment_strong_count_GameManager,
    rustArcDecrementStrongCount:
        RustLib.instance.api.rust_arc_decrement_strong_count_GameManager,
    rustArcDecrementStrongCountPtr:
        RustLib.instance.api.rust_arc_decrement_strong_count_GameManagerPtr,
  );

  /// 检查将帅是否照面
  bool areKingsFacing() =>
      RustLib.instance.api.crateChessGameManagerGameManagerAreKingsFacing(
        that: this,
      );

  Player get currentPlayer => RustLib.instance.api
          .crateChessGameManagerGameManagerAutoAccessorGetCurrentPlayer(
        that: this,
      );

  bool get needFlipForDisplay => RustLib.instance.api
          .crateChessGameManagerGameManagerAutoAccessorGetNeedFlipForDisplay(
        that: this,
      );

  set currentPlayer(Player currentPlayer) => RustLib.instance.api
      .crateChessGameManagerGameManagerAutoAccessorSetCurrentPlayer(
          that: this, currentPlayer: currentPlayer);

  set needFlipForDisplay(bool needFlipForDisplay) => RustLib.instance.api
      .crateChessGameManagerGameManagerAutoAccessorSetNeedFlipForDisplay(
          that: this, needFlipForDisplay: needFlipForDisplay);

  void clearBoard() =>
      RustLib.instance.api.crateChessGameManagerGameManagerClearBoard(
        that: this,
      );

  int countPlayerKings({required Player player}) =>
      RustLib.instance.api.crateChessGameManagerGameManagerCountPlayerKings(
          that: this, player: player);

  /// 交换棋盘上所有棋子的红黑属性。
  /// 例如，原位置的红车变为黑车，黑马变为红马...
  /// 注意：此方法仅交换棋子颜色，不改变棋盘的翻转状态，不切换当前玩家，但内部board仍需保证黑上红下
  Future<void> exchangePieces() =>
      RustLib.instance.api.crateChessGameManagerGameManagerExchangePieces(
        that: this,
      );

  /// 获取当前逻辑视角下的棋盘数组（不改变状态）。
  Future<U8Array256> getDisplayBoard() =>
      RustLib.instance.api.crateChessGameManagerGameManagerGetDisplayBoard(
        that: this,
      );

  Future<String> getDisplayBoardFen() =>
      RustLib.instance.api.crateChessGameManagerGameManagerGetDisplayBoardFen(
        that: this,
      );

  Future<PieceMove?> getRandomLegalMove() =>
      RustLib.instance.api.crateChessGameManagerGameManagerGetRandomLegalMove(
        that: this,
      );

  SidePieceType getSidePieceByIndex({required int pos}) =>
      RustLib.instance.api.crateChessGameManagerGameManagerGetSidePieceByIndex(
          that: this, pos: pos);

  Future<String> iccsMoveToChineseMove({required String iccsMove}) =>
      RustLib.instance.api
          .crateChessGameManagerGameManagerIccsMoveToChineseMove(
              that: this, iccsMove: iccsMove);

  Future<bool> isCurrentPlayerLosing() => RustLib.instance.api
          .crateChessGameManagerGameManagerIsCurrentPlayerLosing(
        that: this,
      );

  Future<bool> isCurrentPlayerWinning() => RustLib.instance.api
          .crateChessGameManagerGameManagerIsCurrentPlayerWinning(
        that: this,
      );

  /// 根据输入的起始终止点位（视觉坐标），基于棋子规则判断是否合法（不考虑将军、困毙等特殊情况）
  Future<bool> isPieceValidMove(
          {required int visualSrcRow,
          required int visualSrcCol,
          required int visualDstRow,
          required int visualDstCol}) =>
      RustLib.instance.api.crateChessGameManagerGameManagerIsPieceValidMove(
          that: this,
          visualSrcRow: visualSrcRow,
          visualSrcCol: visualSrcCol,
          visualDstRow: visualDstRow,
          visualDstCol: visualDstCol);

  /// 校验给定类型的棋子放置在指定位置（视觉坐标 `visual_row`, `visual_col）是否符合其基本区域规则`。
  /// `visual_row`: 1-10 (从上到下), `visual_col`: 1-9 (从左到右)
  Future<bool> isValidPiecePlacement(
          {required int visualRow,
          required int visualCol,
          required SidePieceType pieceType}) =>
      RustLib.instance.api
          .crateChessGameManagerGameManagerIsValidPiecePlacement(
              that: this,
              visualRow: visualRow,
              visualCol: visualCol,
              pieceType: pieceType);

  bool isValidSetupForGameStart() => RustLib.instance.api
          .crateChessGameManagerGameManagerIsValidSetupForGameStart(
        that: this,
      );

  void loadFen({required String fenStr}) => RustLib.instance.api
      .crateChessGameManagerGameManagerLoadFen(that: this, fenStr: fenStr);

  /// 根据ICCS坐标执行棋子移动，不进行规则验证，不切换玩家
  ///
  /// # 参数
  ///
  /// * `iccs_move` - ICCS格式的移动字符串，例如 "a0a1"（车九进一）
  ///
  /// # 说明
  ///
  /// 此函数根据ICCS坐标移动棋子，但不会：
  /// - 验证移动是否符合规则
  /// - 检查是否轮到当前玩家行棋
  /// - 自动切换当前玩家
  /// - 检查将军、将死或困毙状态
  ///
  /// 如果需要切换玩家，请在调用此函数后手动调用 `switch_player()`。
  ///
  /// # 示例
  ///
  /// ```
  /// let mut gm = GameManager::new();
  /// gm.make_move_by_iccs("a0a1"); // 车九进一
  /// gm.switch_player(); // 切换到黑方
  /// ```
  void makeMoveByIccs({required String iccsMove}) =>
      RustLib.instance.api.crateChessGameManagerGameManagerMakeMoveByIccs(
          that: this, iccsMove: iccsMove);

  PlacementValidity placePieceOnBoard(
          {required int visualRow,
          required int visualCol,
          required SidePieceType pieceToPlace}) =>
      RustLib.instance.api.crateChessGameManagerGameManagerPlacePieceOnBoard(
          that: this,
          visualRow: visualRow,
          visualCol: visualCol,
          pieceToPlace: pieceToPlace);

  void reset() => RustLib.instance.api.crateChessGameManagerGameManagerReset(
        that: this,
      );

  void switchPlayer() =>
      RustLib.instance.api.crateChessGameManagerGameManagerSwitchPlayer(
        that: this,
      );

  Future<void> updateBoard(
          {required int visualRow,
          required int visualCol,
          required int pieceIndex}) =>
      RustLib.instance.api.crateChessGameManagerGameManagerUpdateBoard(
          that: this,
          visualRow: visualRow,
          visualCol: visualCol,
          pieceIndex: pieceIndex);

  /// 全面校验当前棋盘布局是否合规，用于退出摆谱模式等场景。
  /// 返回 Ok(()) 表示合规，Err(String) 表示不合规及其原因。
  void validateFullBoardLayout() => RustLib.instance.api
          .crateChessGameManagerGameManagerValidateFullBoardLayout(
        that: this,
      );
}
