// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../frb_generated.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';

// These functions are ignored because they are not marked as `pub`: `get_pos_str_from_row_col`

Future<String> getIccsMoveStrFromPos(
        {required int srcIndex, required int dstIndex}) =>
    RustLib.instance.api.crateChessMoveUtilsGetIccsMoveStrFromPos(
        srcIndex: srcIndex, dstIndex: dstIndex);

/// 从ICCS棋谱表示法中获取源位置和目标位置的行列坐标（行从上往下数，列从左往右数，都是从1开始）
///
/// # 参数
///
/// * `iccs_move` - ICCS格式的棋步字符串，例如 "a3a4"
///
/// # 返回值
///
/// 返回一个包含四个u8值的行列值元组 (src_row, src_col, dst_row, dst_col)
///
/// # 示例
///
/// ```
/// let (src_row, src_col, dst_row, dst_col) = get_src_dst_row_col_from_iccs_move("a3a4");
/// assert_eq!((src_row, src_col, dst_row, dst_col), (7, 1, 6, 1));
/// ```
(int, int, int, int) getSrcDstRowColFromIccsMove({required String iccsMove}) =>
    RustLib.instance.api
        .crateChessMoveUtilsGetSrcDstRowColFromIccsMove(iccsMove: iccsMove);
