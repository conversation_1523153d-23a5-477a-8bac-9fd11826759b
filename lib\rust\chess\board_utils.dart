// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../frb_generated.dart';
import 'game_manager.dart';
import 'package:collection/collection.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';
import 'piece_utils.dart';

// These functions are ignored because they are not marked as `pub`: `board_256_to_fen`, `fen_char_to_piece_type`, `get_board_row_col_from_array_256_index`, `piece_type_to_fen_char`

Future<bool> isPosInFort({required int pos}) =>
    RustLib.instance.api.crateChessBoardUtilsIsPosInFort(pos: pos);

Future<bool> isPosInBoard({required int pos}) =>
    RustLib.instance.api.crateChessBoardUtilsIsPosInBoard(pos: pos);

/// 将棋盘的行列坐标转换为内部256字节数组的索引
///
/// # 参数
///
/// * `row` - 棋盘上的行号(1-10) 由上往下数
/// * `col` - 棋盘上的列号(1-9) 由左往右数
///
/// # 返回值
///
/// 返回对应的内部256字节数组的索引(0-255)
///
/// # 示例
///
/// ```
/// let index = get_array_256_index_from_board_row_col(1, 1);
/// assert_eq!(index, 0x33); // 第一个棋子位置对应在16*16的数组中的索引
/// ```
int getArray256IndexFromBoardRowCol(int row, int col) => RustLib.instance.api
    .crateChessBoardUtilsGetArray256IndexFromBoardRowCol(row: row, col: col);

int? convertArray256IndexToArray90Index(int index256) => RustLib.instance.api
    .crateChessBoardUtilsConvertArray256IndexToArray90Index(index256: index256);

int convertArray90IndexToArray256Index(int index90) => RustLib.instance.api
    .crateChessBoardUtilsConvertArray90IndexToArray256Index(index90: index90);

/// 将256数组棋盘转换为90数组棋盘
U8Array90 convertBoard256ToBoard90(U8Array256 board256) => RustLib.instance.api
    .crateChessBoardUtilsConvertBoard256ToBoard90(board256: board256);

/// 将90数组棋盘转换为256数组棋盘
U8Array256 convertBoard90ToBoard256(U8Array90 board90) => RustLib.instance.api
    .crateChessBoardUtilsConvertBoard90ToBoard256(board90: board90);

/// 将棋盘数组转换为FEN字符串，既可以处理256数组，也可以处理90数组
///
/// # 参数
///
/// * `board` - 棋盘数组，可以是256长度的数组或90长度的数组
/// * `current_player` - 当前轮到哪一方行棋（红方或黑方）
///
/// # 返回值
///
/// 返回FEN格式的字符串，格式为：`<棋盘状态> <当前行棋方> - - 0 1`
/// 其中当前行棋方用'w'表示红方，'b'表示黑方
///
/// # 示例
///
/// ```
/// let fen = board_to_fen(&board, Player::Red);
/// // 返回类似 "rnbakabnr/9/1c5c1/p1p1p1p1p/9/9/P1P1P1P1P/1C5C1/9/RNBAKABNR w - - 0 1"
/// ```
///
/// # 注意
///
/// 在测试移动后的棋盘状态时，应该传入移动后轮到哪一方行棋的信息。
/// 例如，如果红方刚刚移动了棋子，那么下一步应该是黑方行棋，此时应传入 `Player::Black`。
/// 但在测试中，我们通常保持与预期FEN字符串一致的玩家参数。
String boardToFen(List<int> board, Player currentPlayer) => RustLib.instance.api
    .crateChessBoardUtilsBoardToFen(board: board, currentPlayer: currentPlayer);

(U8Array256, Player) fenToBoard(String fen) =>
    RustLib.instance.api.crateChessBoardUtilsFenToBoard(fen: fen);

class U8Array90 extends NonGrowableListView<int> {
  static const arraySize = 90;

  @internal
  Uint8List get inner => _inner;
  final Uint8List _inner;

  U8Array90(this._inner)
      : assert(_inner.length == arraySize),
        super(_inner);

  U8Array90.init() : this(Uint8List(arraySize));
}
