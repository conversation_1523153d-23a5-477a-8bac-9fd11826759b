{"version": "0.2.0", "configurations": [{"name": "meng_ru_ling_shi", "request": "launch", "type": "dart"}, {"name": "meng_ru_ling_shi (profile mode)", "request": "launch", "type": "dart", "flutterMode": "profile"}, {"name": "meng_ru_ling_shi (release mode)", "request": "launch", "type": "dart", "flutterMode": "release"}, {"name": "C/C++ Runner: Debug Session", "type": "cppdbg", "request": "launch", "args": [], "stopAtEntry": false, "externalConsole": true, "cwd": "d:/DATA/BaiduSyncdisk/project/personal/chinese_chess/meng_ru_ling_shi/windows/runner", "program": "d:/DATA/BaiduSyncdisk/project/personal/chinese_chess/meng_ru_ling_shi/windows/runner/build/Debug/outDebug", "MIMode": "gdb", "miDebuggerPath": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}]}]}