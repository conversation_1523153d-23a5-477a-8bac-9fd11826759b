// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../frb_generated.dart';
import 'game_manager.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';

// These function are ignored because they are on traits that is not defined in current crate (put an empty `#[frb]` on it to unignore): `assert_receiver_is_total_eq`, `assert_receiver_is_total_eq`, `assert_receiver_is_total_eq`, `clone`, `clone`, `clone`, `eq`, `eq`, `eq`, `fmt`, `fmt`, `fmt`, `fmt`, `fmt`, `fmt`, `hash`, `partial_cmp`

Future<bool> isRedPiece({required SidePieceType sidePiece}) =>
    RustLib.instance.api.crateChessPieceUtilsIsRedPiece(sidePiece: sidePiece);

Future<bool> isBlackPiece({required SidePieceType sidePiece}) =>
    RustLib.instance.api.crateChessPieceUtilsIsBlackPiece(sidePiece: sidePiece);

PieceType getUnsidePieceBySidePiece({required SidePieceType sidePiece}) =>
    RustLib.instance.api
        .crateChessPieceUtilsGetUnsidePieceBySidePiece(sidePiece: sidePiece);

class PieceMove {
  final String chinese;
  final String iccs;
  final int srcRow;
  final int srcCol;
  final int dstRow;
  final int dstCol;
  final Player player;

  const PieceMove.raw({
    required this.chinese,
    required this.iccs,
    required this.srcRow,
    required this.srcCol,
    required this.dstRow,
    required this.dstCol,
    required this.player,
  });

  /// 从ICCS坐标创建PieceMove
  static PieceMove fromIccs(
          {required String iccs, required GameManager gameManager}) =>
      RustLib.instance.api.crateChessPieceUtilsPieceMoveFromIccs(
          iccs: iccs, gameManager: gameManager);

  factory PieceMove(
          int srcRow, int srcCol, int dstRow, int dstCol, Player player) =>
      RustLib.instance.api.crateChessPieceUtilsPieceMoveNew(
          srcRow: srcRow,
          srcCol: srcCol,
          dstRow: dstRow,
          dstCol: dstCol,
          player: player);

  @override
  int get hashCode =>
      chinese.hashCode ^
      iccs.hashCode ^
      srcRow.hashCode ^
      srcCol.hashCode ^
      dstRow.hashCode ^
      dstCol.hashCode ^
      player.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PieceMove &&
          runtimeType == other.runtimeType &&
          chinese == other.chinese &&
          iccs == other.iccs &&
          srcRow == other.srcRow &&
          srcCol == other.srcCol &&
          dstRow == other.dstRow &&
          dstCol == other.dstCol &&
          player == other.player;
}

enum PieceType {
  king,
  advisor,
  bishop,
  knight,
  rook,
  cannon,
  pawn,
  none,
  ;

  Future<int> getMaxCount() =>
      RustLib.instance.api.crateChessPieceUtilsPieceTypeGetMaxCount(
        that: this,
      );
}

enum Player {
  red,
  black,
  unknown,
  ;

  String getName() => RustLib.instance.api.crateChessPieceUtilsPlayerGetName(
        that: this,
      );

  Player opposite() => RustLib.instance.api.crateChessPieceUtilsPlayerOpposite(
        that: this,
      );
}

enum SidePieceType {
  none,
  redKing,
  redAdvisor,
  redBishop,
  redKnight,
  redRook,
  redCannon,
  redPawn,
  blackKing,
  blackAdvisor,
  blackBishop,
  blackKnight,
  blackRook,
  blackCannon,
  blackPawn,
  ;

  void getChineseName() =>
      RustLib.instance.api.crateChessPieceUtilsSidePieceTypeGetChineseName(
        that: this,
      );

  Player getSide() =>
      RustLib.instance.api.crateChessPieceUtilsSidePieceTypeGetSide(
        that: this,
      );

  static SidePieceType getSidePieceTypeByIndex(int index) => RustLib
      .instance.api
      .crateChessPieceUtilsSidePieceTypeGetSidePieceTypeByIndex(index: index);

  int getSidePieceTypeIndex() => RustLib.instance.api
          .crateChessPieceUtilsSidePieceTypeGetSidePieceTypeIndex(
        that: this,
      );
}
