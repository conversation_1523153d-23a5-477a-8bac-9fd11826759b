/*
 * <AUTHOR> 老董
 * @Date         : 2022-04-29 22:27:48
 * @LastEditors  : 老董
 * @LastEditTime : 2025-05-15 16:50:30
 * @Description  : 全局属性定义
 */
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:meng_ru_ling_shi/pages/home/<USER>';
import 'package:meng_ru_ling_shi/rust/chess/board_utils.dart';
import 'package:meng_ru_ling_shi/rust/chess/game_manager.dart';
import 'package:meng_ru_ling_shi/rust/chess/piece_utils.dart' as chess_utils; // 使用前缀

enum NeumorphicButtonState {
  deepPressed,
  middlePressed,
  noPressed,
}

// 一些默认参数
const newGameBtnLog = "新建棋局";
const newAIBtnLog = "AI点击";
const newSettingBtnLog = "SETTING";
const newLinkBtnLog = "LINK";

const skinPath = "./assets/skins/";
const playerIconPath = "./assets/icon/"; //帅 将 图标
const boardPath = "./assets/skins/board.svg";
const samplePiecePath = "./assets/skins/bb.svg";
const selected1Path = "./assets/skins/mask.svg";
const selected2Path = "./assets/skins/mask2.svg";

const backgroundStartColor = Color(0xffffd500);
const backgroundEndColor = Color(0xfff6a00c);

const maxSizeScale = 1.5;
const minSizeScale = 0.7;

const testScale = 1.0;
const appWidth = 705.0 * testScale; //用以设置的窗口大小
const appHeight = 545.0 * testScale; //用以设置的窗口大小
var devicePixelRatio = 1.25; //TODO:和windows的缩放比例，暂时先硬编码
var testWidth = 865.0; //基于上述appSize后用spy++实际捕获到的尺寸
var testHeight = 673.0; //基于上述appSize后用spy++实际捕获到的尺寸

// appWidth-->winWidth : appWidth*devicePixelRatio* -16.5
// var winWidth = appWidth * devicePixelRatio * -16.5;
// appHeight-->winHeight : appHeight*devicePixelRatio* - 8.5
const minAppWidth = appWidth * minSizeScale;
const minAppHeight = appHeight * minSizeScale;
const maxAppWidth = appWidth * maxSizeScale;
const maxAppHeight = appHeight * maxSizeScale;
const chessUiWidthRatio = 0.78; //棋盘和状态ui遵行7:3的比例

const aspectRatio = appWidth / appHeight;
var boardWidth = 521.0;
var boardHeight = 577.0;
var testPieceSize = 57.0 * 1.18; //宽高一致

const testBoardLeftTopCornerPos = Offset(460, 199); //捕获时时的窗口左上角全局offset
const testLeftTop1stPos = Offset(498, 235); //捕获时左上角第一点全局offset
const testLeftTop2edPos = Offset(564.47, 235); //捕获时左上角向右第二点全局offset

const stateUiWidth = 250.0; //右侧状态ui的固定宽度
const toobarHeight = 40.0;
const hideToobarHeight = 20.0;

const testPanelWidth = 50.0;
const testBorderRadius = 10.0;

// 常用函数
String getCurrentTimeString() {
  DateTime now = DateTime.now();
  String formattedDate = DateFormat('kk:mm:ss').format(now);
  return formattedDate;
}

// 表示每一步棋的结构体，主要用于画箭头使用，rust中不需要
class ChessMove {
  final int srcRow;
  final int srcCol;
  final int dstRow;
  final int dstCol;
  final chess_utils.Player player; // 使用前缀

  ChessMove({
    required this.srcRow,
    required this.srcCol,
    required this.dstRow,
    required this.dstCol,
    required this.player,
  })  : assert(srcRow >= 1 && srcRow <= 10),
        assert(srcCol >= 1 && srcCol <= 9),
        assert(dstRow >= 1 && dstRow <= 10),
        assert(dstCol >= 1 && dstCol <= 9);

  @override
  bool operator ==(Object other) {
    return other is ChessMove &&
        srcRow == other.srcRow &&
        srcCol == other.srcCol &&
        dstRow == other.dstRow &&
        dstCol == other.dstCol;
  }

  @override
  int get hashCode => Object.hash(srcRow, srcCol, dstRow, dstCol, player);
}

// 被选择的mask类型
enum MaskedType {
  none, //未被选中
  focused, //被鼠标点击后的遮罩UI类型
  moved, //移动后的遮罩UI类型
}

// 注意：被MaskedPiece包裹的piece可以是none，因棋盘中可能存在没有棋子的位置
class MaskedPiece {
  late int row;
  late int col;
  chess_utils.SidePieceType _sidePieceType; // 使用前缀
  var _maskedType = MaskedType.none;
  int index; //从0开始计数，最大89

  MaskedPiece(this._sidePieceType, this.index) : assert(index >= 0 && index <= 89) {
    row = index ~/ GameManager.boardColCount() + 1; // 注意：这个也是基于Column
    col = index % GameManager.boardColCount() + 1;
  }

  chess_utils.SidePieceType pieceType() {
    // 使用前缀
    return _sidePieceType;
  }

  setSidePieceTypeByIndex(int index) {
    _sidePieceType = chess_utils.SidePieceType.getSidePieceTypeByIndex(index); // 使用前缀
  }

  setSidePieceType(chess_utils.SidePieceType sidePieceType) {
    // 使用前缀
    _sidePieceType = sidePieceType;
  }

  int getIndexOfSidePieceType() {
    return _sidePieceType.getSidePieceTypeIndex();
  }

  MaskedType maskType() {
    return _maskedType;
  }

  void setMaskType(MaskedType maskType) {
    _maskedType = maskType;
  }

  chess_utils.Player? player() {
    // 使用前缀
    return _sidePieceType.getSide();
  }
}

//↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓Board90Pieces↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓
class BoardPiecesView {
  final RxList<MaskedPiece> _piecesOf90;
  final RxList<chess_utils.PieceMove> moveHistory = <chess_utils.PieceMove>[].obs; // 使用前缀
  final List<String> _fenHistory = <String>[];
  chess_utils.Player _player = chess_utils.Player.red; // 使用前缀
  final RxInt _activeMoveIndex = (-1).obs; // -1 表示初始棋盘状态

  List<MaskedPiece> get pieces => _piecesOf90;
  chess_utils.Player get currentPlayer => _player; // 使用前缀
  int get fenHistoryLength => _fenHistory.length; // _fenHistory.length 的 getter 方法
  int get activeMoveIndex => _activeMoveIndex.value;

  BoardPiecesView()
      : _piecesOf90 = RxList<MaskedPiece>(
          List.generate(
            GameManager.boardRowCount() * GameManager.boardColCount(),
            (index) => MaskedPiece(chess_utils.SidePieceType.none, index), // 使用前缀
          ),
        );

  void reset(U8Array256 board, chess_utils.Player playerToPlay) {
    // 使用前缀
    final board90 = convertBoard256ToBoard90(board);
    for (int i = 0; i < board90.length; i++) {
      _piecesOf90[i].setSidePieceType(chess_utils.SidePieceType.getSidePieceTypeByIndex(board90[i]));
      // 重新设置每个MaskedPiece的视觉坐标，因为board参数已经是displayBoard（视觉坐标）
      // i是90格棋盘的索引，直接对应视觉坐标
      final visualRow = i ~/ GameManager.boardColCount() + 1; // 视觉行 (1-10)
      final visualCol = i % GameManager.boardColCount() + 1; // 视觉列 (1-9)
      _piecesOf90[i].row = visualRow;
      _piecesOf90[i].col = visualCol;
    }
    _player = playerToPlay;
    refresh();
    moveHistory.clear();
    _fenHistory.clear();
    _activeMoveIndex.value = -1; // 重置活动移动索引

    // 添加局面的 FEN
    _fenHistory.add(boardToFen(board, _player));
  }

  void goBackToBoardAfterIndex(int targetMoveIndex) {
    // targetMoveIndex 是 moveHistory 中我们希望作为最后一个“活动”移动的索引。
    // -1 表示初始状态。
    if (targetMoveIndex < -1 || targetMoveIndex >= moveHistory.length) {
      // 如果 targetMoveIndex 等于 moveHistory.length，则表示转到最后一次移动*之后*的状态，
      // 这不是此函数的用途。它应该严格小于 moveHistory.length。
      // 但是，允许 targetMoveIndex == moveHistory.length - 1 是可以的（最后一次实际移动）。
      if (targetMoveIndex == -1 && moveHistory.isEmpty) {
        // 有效，在尚未进行任何移动时转到初始状态
      } else if (targetMoveIndex >= moveHistory.length) {
        debugPrint(
            "goBackToBoardAfterIndex: 无效的 targetMoveIndex $targetMoveIndex，moveHistory 长度为 ${moveHistory.length}");
        return; // 或抛出错误
      }
    }

    final fenIndexToLoad = targetMoveIndex + 1;
    if (fenIndexToLoad < 0 || fenIndexToLoad >= _fenHistory.length) {
      debugPrint("goBackToBoardAfterIndex: 无效的 fenIndexToLoad $fenIndexToLoad，_fenHistory 长度为 ${_fenHistory.length}");
      return; // 或抛出错误
    }

    // 更新棋盘状态
    final fen = _fenHistory[fenIndexToLoad];
    final (board256, player) = fenToBoard(fen);
    final board90 = convertBoard256ToBoard90(board256);
    for (int i = 0; i < board90.length; i++) {
      updatePiece(i, chess_utils.SidePieceType.getSidePieceTypeByIndex(board90[i]), MaskedType.none); // 使用前缀
    }

    // 更新当前玩家
    _player = player;
    _activeMoveIndex.value = targetMoveIndex; // 更新活动移动索引
    moveHistory.refresh(); // 如果 Obx 依赖于 moveHistory，则强制重建

    // 不再在此处删除历史记录
    // _fenHistory.removeRange(fenIndexToLoad + 1, _fenHistory.length);
    // moveHistory.removeRange(targetMoveIndex + 1, moveHistory.length);

    setMovedMarkerForMove(targetMoveIndex);
  }

  void setMovedMarkerForMove(int moveIndex) {
    clearAllMasks(); // 清除之前的 'moved' 和 'focused' 遮罩
    if (moveIndex >= 0 && moveIndex < moveHistory.length) {
      final chess_utils.PieceMove move = moveHistory[moveIndex]; // 使用前缀

      // 假设 move.srcRow, srcCol, dstRow, dstCol 是从1开始的逻辑坐标
      final srcUiIndex = (move.srcRow - 1) * GameManager.boardColCount() + (move.srcCol - 1);
      final dstUiIndex = (move.dstRow - 1) * GameManager.boardColCount() + (move.dstCol - 1);

      if (srcUiIndex >= 0 && srcUiIndex < _piecesOf90.length) {
        _piecesOf90[srcUiIndex].setMaskType(MaskedType.moved);
      }
      if (dstUiIndex >= 0 && dstUiIndex < _piecesOf90.length) {
        _piecesOf90[dstUiIndex].setMaskType(MaskedType.moved);
      }
    }
    refresh(); // 刷新以显示新的遮罩。同时刷新 _piecesOf90。
  }

  void clearAllPieces() {
    for (var piece in _piecesOf90) {
      piece.setSidePieceType(chess_utils.SidePieceType.none); // 使用前缀
      piece.setMaskType(MaskedType.none); // 同时清除所有遮罩
    }
    moveHistory.clear(); // 同时清除移动历史
    _fenHistory.clear(); // 清除FEN历史
    _activeMoveIndex.value = -1; // 重置活动移动索引
    clearAllMasks(); // 确保清除遮罩
  }

  void refresh() {
    _piecesOf90.refresh();
  }

  void clearAllMasks() {
    bool changed = false;
    for (var piece in _piecesOf90) {
      if (piece.maskType() != MaskedType.none) {
        piece.setMaskType(MaskedType.none);
        changed = true;
      }
    }
    if (changed) {
      refresh(); // clearAllMasks 会调用 refresh
    }
  }

  MaskedPiece? getFocusedPiece() {
    return _piecesOf90.firstWhereOrNull((p) => p.maskType() == MaskedType.focused);
  }

  void setFocusedPiece(MaskedPiece piece) {
    // 清除之前的焦点
    for (var p in _piecesOf90) {
      if (p.maskType() == MaskedType.focused) {
        p.setMaskType(MaskedType.none);
      }
    }

    // 设置新的焦点
    final focusPiece = _piecesOf90.firstWhere(
      (p) => p.row == piece.row && p.col == piece.col,
      orElse: () => throw StateError('在_piecesOf90中未找到指定的棋位'),
    );
    focusPiece.setMaskType(MaskedType.focused);

    refresh();
  }

  List<int> _piecesToBoardArray() => _piecesOf90.map((p) => p.pieceType().getSidePieceTypeIndex()).toList();

  void updatePiece(int index, chess_utils.SidePieceType newType, MaskedType newMaskType) {
    // 使用前缀
    _piecesOf90[index].setSidePieceType(newType);
    _piecesOf90[index].setMaskType(newMaskType);
    refresh();
  }

  void move(MaskedPiece srcPiece, MaskedPiece dstPiece, chess_utils.PieceMove pieceMove) {
    // 使用前缀
    clearAllMasks();
    updatePiece(dstPiece.index, srcPiece.pieceType(), MaskedType.moved);
    updatePiece(srcPiece.index, chess_utils.SidePieceType.none, MaskedType.moved); // 使用前缀

    // 如果在当前历史记录未到“末尾”时进行移动，则截断未来的移动。
    if (_activeMoveIndex.value < moveHistory.length - 1) {
      // 当前 moveHistory.length 是在添加新移动之前。
      // 因此，如果 _activeMoveIndex 为 2，而 moveHistory.length 为 5，
      // 则表示我们处于 moveHistory[2] 之后的状态。
      // 进行新的移动后，必须删除索引 3（即 _activeMoveIndex.value + 1）及之后的移动。
      moveHistory.removeRange(_activeMoveIndex.value + 1, moveHistory.length);
      // _fenHistory 多一个元素（初始状态）。
      // 如果删除了 moveHistory[x]，则应删除 _fenHistory[x+1]。
      // 因此，从 (_activeMoveIndex.value + 1) + 1 = _activeMoveIndex.value + 2 开始删除
      _fenHistory.removeRange(_activeMoveIndex.value + 2, _fenHistory.length);
    }

    // 添加移动到历史记录
    moveHistory.add(pieceMove);
    _activeMoveIndex.value = moveHistory.length - 1; // 新的移动现在是活动的移动

    // 切换当前玩家
    _player = _player == chess_utils.Player.red ? chess_utils.Player.black : chess_utils.Player.red; // 使用前缀

    // 添加局面的 FEN
    _fenHistory.add(boardToFen(_piecesToBoardArray(), _player));

    setMovedUiByPieces(srcPiece, dstPiece);
  }

  // 标记移动的棋子并UI刷新
  void setMovedUiByPieces(MaskedPiece srcPiece, MaskedPiece dstPiece) {
    srcPiece.setMaskType(MaskedType.moved);
    dstPiece.setMaskType(MaskedType.moved);
    refresh();
  }
}
//↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑Board90Pieces↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑
